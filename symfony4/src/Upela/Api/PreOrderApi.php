<?php

namespace Upela\Api;

use Open\LogBundle\Utils\EventNameEnum;
use Open\LogBundle\Utils\LogUtil;
use Symfony\Component\Serializer\Normalizer\AbstractObjectNormalizer;
use Symfony\Contracts\HttpClient\Exception\ExceptionInterface;
use Upela\Model\PreOrder;
use Upela\Request\PreOrder\CreateRequest;

final class PreOrderApi extends Api
{
    /**
     * @param CreateRequest $request
     * @throws Exception
     */
    public function create(CreateRequest $request)
    {
        $this->initHttpClient();

        $createUrl = $this->buildUrl('/api/pre-order');

        $this->logger->info(
            sprintf(
                'UPELA CREATE REQUEST SENT; url: %s, request params: %s',
                $createUrl,
                json_encode(['json' => $request->toArray()])
            ),
            LogUtil::buildContext([LogUtil::EVENT_NAME => EventNameEnum::UPELA_API_PRE_ORDER_REQUEST])

        );

        try {
            $response = $this->httpClient->request(
                'POST',
                $createUrl,
                [
                    'json' => $request->toArray()
                ]
            );

            if (!in_array($response->getStatusCode(), [200, 201, 202, 204, 206, 207])) {
                throw new Exception('Cannot create UPELA preorder');
            }

        } catch (ExceptionInterface $exception) {
            throw new Exception(sprintf('Cannot create UPELA preorder: %s', $exception->getMessage()));
        }
    }

    /**
     * @param int $preorderId
     * @return PreOrder
     * @throws Exception
     */
    public function fetchPreorder(int $preorderId): PreOrder
    {
        $this->initHttpClient();

        $uri = $this->buildUrl(sprintf('/api/pre-order/%d', $preorderId));

        $this->logger->info(
            sprintf(
                'UPELA FETCHING PREORDER : %s',
                $preorderId
            ),
            LogUtil::buildContext([LogUtil::EVENT_NAME => EventNameEnum::UPELA_API_PRODUCT_REQUEST])
        );

        try {
            $response = $this->httpClient->request('GET', $uri);
            if (!in_array($response->getStatusCode(), [200, 201, 202, 204, 206, 207])) {
                throw new Exception(sprintf('Cannot fetch UPELA preorder %d', $preorderId));
            }

            $content = $response->getContent();
        } catch (ExceptionInterface $exception) {
            throw new Exception(sprintf('Cannot fetch UPELA preorder %d - %s', $preorderId, $exception->getMessage()));
        }

        $preOrder = $this->serializer->deserialize($content, PreOrder::class, 'json', [AbstractObjectNormalizer::DISABLE_TYPE_ENFORCEMENT => true]);

        if (!$preOrder instanceOf PreOrder) {
            throw new Exception('Failed fetching UPELA preorder', 500);
        }

        return $preOrder;
    }
}
