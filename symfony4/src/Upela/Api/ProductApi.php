<?php

namespace Upela\Api;

use Open\LogBundle\Utils\EventNameEnum;
use Open\LogBundle\Utils\LogUtil;
use Symfony\Component\Serializer\Normalizer\AbstractObjectNormalizer;
use Symfony\Contracts\HttpClient\Exception\ExceptionInterface;
use Symfony\Contracts\HttpClient\Exception\HttpExceptionInterface;
use Upela\Model\Product;
use Upela\Model\ProductCollection;
use Upela\Request\Product\CancelRequest;

final class ProductApi extends Api
{
    /**
     * @param array $productIds
     * @return array
     * @throws Exception
     */
    public function fetchProductByIds(array $productIds): array
    {
        $this->initHttpClient();

        $products = [];
        $uris = array_map(
            function(int $productId) {
                return $this->buildUrl(sprintf('/api/product/%d', $productId));
            },
            $productIds
        );

        $this->logger->info(
            sprintf(
                'UPELA FETCHING PRODUCTS; product ids: %s',
                json_encode($productIds)
            ),
            LogUtil::buildContext([LogUtil::EVENT_NAME => EventNameEnum::UPELA_API_PRODUCT_REQUEST])
        );

        $responses = [];

        try {
            foreach ($uris as $uri) {
                $responses[] = $this->httpClient->request('GET', $uri);
            }

            foreach ($this->httpClient->stream($responses) as $response => $chunk) {

                if ($chunk->isFirst()) {
                    if (!in_array($response->getStatusCode(), [200, 201, 202, 204, 206, 207])) {
                        unset($responses);
                    }
                }
                if ($chunk->isLast()) {
                    // the full content of $response just completed
                    // $response->getContent() is now a non-blocking call
                    $products[] = $this->serializer->deserialize($response->getContent(), Product::class, 'json', [AbstractObjectNormalizer::DISABLE_TYPE_ENFORCEMENT => true]);
                }
            }

        } catch (ExceptionInterface|HttpExceptionInterface $exception) {
            throw new Exception(sprintf('Cannot fetch UPELA product: %s', $exception->getMessage()), intval($exception->getCode()));
        }

        return $products;
    }

    /**
     * @param int $shipmentId
     * @return Product[]
     * @throws Exception
     */
    public function fetchProductsByShipmentId(int $shipmentId): array
    {
        $this->initHttpClient();

        $uri = $this->buildUrl(sprintf('/api/product/shipment/%d', $shipmentId));

        $this->logger->info(
            sprintf(
                'UPELA FETCHING PRODUCT FROM SHIPMENT ID: %s',
                $shipmentId
            ),
            LogUtil::buildContext([LogUtil::EVENT_NAME => EventNameEnum::UPELA_API_PRODUCT_REQUEST])
        );

        try {
            $response = $this->httpClient->request('GET', $uri);
            if (!in_array($response->getStatusCode(), [200, 201, 202, 204, 206, 207])) {
                throw new Exception('Cannot fetch UPELA product from shipment ID');
            }

            $content = $response->getContent();
        } catch (ExceptionInterface $exception) {
            throw new Exception(sprintf('Cannot fetch UPELA product from shipment ID: %s', $exception->getMessage()));
        }

        $content = sprintf('{"products":%s}', $content);

        /** @var ProductCollection $productCollection */
        $productCollection = $this->serializer->deserialize($content, ProductCollection::class, 'json', [AbstractObjectNormalizer::DISABLE_TYPE_ENFORCEMENT => true]);

        return $productCollection->getProducts();
    }

    /**
     * @param CancelRequest $cancelRequest
     * @throws Exception
     */
    public function cancelProducts(CancelRequest $cancelRequest)
    {
        $this->initHttpClient();

        /** @var Product $product */
        foreach($cancelRequest->getProducts() as $product) {
            if ($product->getQuantity() > 0) {
                $productUpdateQuantityUrl = $this->buildUrl(sprintf(
                    '/api/product/%d/quantity',
                    $product->getProductId()
                ));

                try {
                    $response = $this->httpClient->request(
                        'PATCH',
                        $productUpdateQuantityUrl,
                        [
                            'json' => [
                                'quantity' => $product->getQuantity()
                            ]
                        ]
                    );

                    if (!in_array($response->getStatusCode(), [200, 201, 202, 204, 206, 207])) {
                        throw new Exception('Cannot update UPELA quantity');
                    }
                } catch(ExceptionInterface $exception) {
                    throw new Exception(sprintf('Cannot update UPELA quantity: %s', $exception->getMessage()));
                }
            }

            if ($product->getQuantity() === 0) {
                $productCancelUrl = $this->buildUrl(sprintf(
                    '/api/product/%d/delete',
                    $product->getProductId()
                ));

                try {
                    $this->httpClient->request(
                        'PATCH',
                        $productCancelUrl
                    );

                    if (!in_array($response->getStatusCode(), [200, 201, 202, 204, 206, 207])) {
                        throw new Exception('Cannot delete UPELA product');
                    }

                } catch(ExceptionInterface $exception) {
                    throw new Exception(sprintf('Cannot delete UPELA product: %s', $exception->getMessage()));
                }
            }
        }
    }
}
