<?php

namespace Upela\Model;

final class PreOrder
{
    /**
     * @var int
     */
    protected $merchantOrderId;

    /**
     * @var Product[]
     */
    protected $products;

    public function getMerchantOrderId(): int
    {
        return $this->merchantOrderId;
    }

    public function setMerchantOrderId(int $merchantOrderId): self
    {
        $this->merchantOrderId = $merchantOrderId;
        return $this;
    }

    /**
     * @return Product[]
     */
    public function getProducts(): array
    {
        return $this->products;
    }

    /**
     * @param Product[] $products
     * @return $this
     */
    public function setProducts(array $products): self
    {
        $this->products = $products;
        return $this;
    }
}
