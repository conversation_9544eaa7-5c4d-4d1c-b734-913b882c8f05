<?php

namespace Upela\Model;

final class Product
{
    public const STATUS_OPEN = 'Open';

    /**
     * @var string
     */
    protected $productId;

    /**
     * @var int|null
     */
    protected $shipmentId;

    /**
     * @var int
     */
    protected $quantity;

    /**
     * @var int
     */
    protected $quantitySent;

    /**
     * @var string
     */
    protected $sku;

    /**
     * @var string
     */
    protected $status;

    /**
     * @var bool|null
     */
    protected $stackable;

    /**
     * @var string|null
     */
    protected $hsCode;

    /**
     * @var string|null
     */
    protected $designation;

    /**
     * @var PreOrder|null
     */
    protected $preOrder;

    /**
     * @var array<string>
     */
    protected $parcels;

    /**
     * @var int
     */
    protected $parcelType;

    /**
     * @var string|null
     */
    protected $currency;

    /**
     * @var string
     */
    protected $expectedPickupDate;

    /**
     * @var string
     */
    protected $expectedDeliveryDate;

    /**
     * @var Offer
     */
    protected $selectedOffer;

    /**
     * @var Address
     */
    protected $addressTo;

    /**
     * @var Address
     */
    protected $addressFrom;

    /**
     * @var Shipment[]
     */
    protected $shipments;

    /**
     * @return string
     */
    public function getProductId(): string
    {
        return $this->productId;
    }

    /**
     * @param string $productId
     * @return $this
     */
    public function setProductId(string $productId): self
    {
        $this->productId = $productId;
        return $this;
    }

    /**
     * @return int|null
     */
    public function getShipmentId(): ?int
    {
        return $this->shipmentId;
    }

    /**
     * @param int|null $shipmentId
     * @return $this
     */
    public function setShipmentId(?int $shipmentId): self
    {
        $this->shipmentId = $shipmentId;
        return $this;
    }

    /**
     * @return int
     */
    public function getQuantity(): int
    {
        return $this->quantity;
    }

    /**
     * @param int $quantity
     * @return $this
     */
    public function setQuantity(int $quantity): self
    {
        $this->quantity = $quantity;
        return $this;
    }

    /**
     * @return int
     */
    public function getQuantitySent(): int
    {
        return $this->quantitySent;
    }

    /**
     * @param int $quantitySent
     * @return $this
     */
    public function setQuantitySent(int $quantitySent): self
    {
        $this->quantitySent = $quantitySent;
        return $this;
    }

    /**
     * @return string
     */
    public function getSku(): string
    {
        return $this->sku;
    }

    /**
     * @param string $sku
     * @return $this
     */
    public function setSku(string $sku): self
    {
        $this->sku = $sku;
        return $this;
    }

    /**
     * @return string
     */
    public function getStatus(): string
    {
        return $this->status;
    }

    /**
     * @param string $status
     * @return $this
     */
    public function setStatus(string $status): self
    {
        $this->status = $status;
        return $this;
    }

    /**
     * @return bool|null
     */
    public function isStackable(): ?bool
    {
        return ($this->stackable);
    }

    /**
     * @param bool|null $stackable
     * @return $this
     */
    public function setStackable(?bool $stackable): self
    {
        $this->stackable = (bool) $stackable;
        return $this;
    }

    /**
     * This data is not mandatory for creating preorder
     * @return string|null
     */
    public function getHsCode(): ?string
    {
        return $this->hsCode;
    }

    /**
     * This data is not mandatory for creating preorder
     * @param string|null $hsCode
     * @return $this
     */
    public function setHsCode(?string $hsCode): self
    {
        $this->hsCode = $hsCode;
        return $this;
    }

    /**
     * @return string|null
     */
    public function getDesignation(): ?string
    {
        return $this->designation;
    }

    /**
     * @param string $designation
     * @return $this
     */
    public function setDesignation(string $designation): self
    {
        $this->designation = $designation;
        return $this;
    }

    /**
     * @return array
     */
    public function getParcels(): array
    {
        return $this->parcels;
    }

    /**
     * @param array $parcels
     * @return $this
     */
    public function setParcels(array $parcels): self
    {
        $this->parcels = $parcels;
        return $this;
    }

    /**
     * @return int
     */
    public function getParcelType(): int
    {
        return $this->parcelType;
    }

    /**
     * @param int $parcelType
     * @return $this
     */
    public function setParcelType(int $parcelType): self
    {
        $this->parcelType = $parcelType;
        return $this;
    }

    /**
     * @return string|null
     */
    public function getCurrency(): ?string
    {
        return $this->currency;
    }

    /**
     * @param string|null $currency
     * @return $this
     */
    public function setCurrency(?string $currency): self
    {
        $this->currency = $currency;
        return $this;
    }

    /**
     * @return string
     */
    public function getExpectedPickupDate(): string
    {
        return $this->expectedPickupDate;
    }

    /**
     * @param string $expectedPickupDate
     * @return $this
     */
    public function setExpectedPickupDate(string $expectedPickupDate): self
    {
        $this->expectedPickupDate = $expectedPickupDate;
        return $this;
    }

    /**
     * @return string
     */
    public function getExpectedDeliveryDate(): string
    {
        return $this->expectedDeliveryDate;
    }

    /**
     * @param string $expectedDeliveryDate
     * @return $this
     */
    public function setExpectedDeliveryDate(string $expectedDeliveryDate): self
    {
        $this->expectedDeliveryDate = $expectedDeliveryDate;
        return $this;
    }

    /**
     * @return Offer
     */
    public function getSelectedOffer(): Offer
    {
        return $this->selectedOffer;
    }

    /**
     * @param Offer $selectedOffer
     * @return $this
     */
    public function setSelectedOffer(Offer $selectedOffer): self
    {
        $this->selectedOffer = $selectedOffer;
        return $this;
    }

    /**
     * @return Address
     */
    public function getAddressTo(): Address
    {
        return $this->addressTo;
    }

    /**
     * @param Address $addressTo
     * @return $this
     */
    public function setAddressTo(Address $addressTo): self
    {
        $this->addressTo = $addressTo;
        return $this;
    }

    /**
     * @return Address
     */
    public function getAddressFrom(): Address
    {
        return $this->addressFrom;
    }

    /**
     * @param Address $addressFrom
     * @return $this
     */
    public function setAddressFrom(Address $addressFrom): self
    {
        $this->addressFrom = $addressFrom;
        return $this;
    }

    /**
     * @return Shipment[]
     */
    public function getShipments(): array
    {
        return $this->shipments;
    }

    /**
     * @param Shipment[] $shipments
     * @return $this
     */
    public function setShipments(array $shipments): self
    {
        $this->shipments = $shipments;
        return $this;
    }

    /**
     * @return PreOrder|null
     */
    public function getPreOrder(): ?PreOrder
    {
        return $this->preOrder;
    }

    /**
     * @param PreOrder|null $preOrder
     * @return $this
     */
    public function setPreOrder(?PreOrder $preOrder): self
    {
        $this->preOrder = $preOrder;
        return $this;
    }

    /**
     * @return array
     */
    public function toArray()
    {
        return [
            'shipmentId' => $this->getShipmentId(),
            'productId' => $this->getProductId(),
            'quantity' => $this->getQuantity(),
            'quantitySent' => $this->getQuantitySent(),
            'parcels' => $this->getParcels(),
            'sku' => $this->getSku(),
            'stackable' => $this->isStackable(),
            'parcelType' => $this->getParcelType(),
            'currency' => $this->getCurrency(),
            'expectedPickupDate' => $this->getExpectedPickupDate(),
            'expectedDeliveryDate' => $this->getExpectedDeliveryDate(),
            'hsCode' => $this->getHsCode(),
            'status' => $this->getStatus(),
            'designation' => $this->getDesignation(),
            'selectedOffer' => $this->getSelectedOffer()->toArray(),
            'shipments' => [],
            'addressTo' => $this->getAddressTo()->toArray(),
            'addressFrom' => $this->getAddressFrom()->toArray(),
        ];
    }
}
