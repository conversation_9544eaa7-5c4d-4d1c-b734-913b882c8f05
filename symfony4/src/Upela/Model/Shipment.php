<?php

namespace Upela\Model;

final class Shipment
{
    /**
     * @var string
     */
    protected $shipmentId;

    /**
     * @var string
     */
    protected $carrierName;

    /**
     * @var int
     */
    protected $quantityProduct;

    /**
     * @var string|null
     */
    protected $expectedDeliveryDate;

    /**
     * @var string|null
     */
    protected $expectedPickupDate;

    /**
     * @var bool|null
     */
    protected $isDelivered;

    /**
     * @var float|null
     */
    protected $priceTi;

    /**
     * @var string|null
     */
    protected $status;

    /**
     * @return string
     */
    public function getShipmentId(): string
    {
        return $this->shipmentId;
    }

    /**
     * @param string $shipmentId
     * @return $this
     */
    public function setShipmentId(string $shipmentId): self
    {
        $this->shipmentId = $shipmentId;
        return $this;
    }

    /**
     * @return int
     */
    public function getQuantityProduct(): int
    {
        return $this->quantityProduct;
    }

    /**
     * @param int $quantityProduct
     * @return $this
     */
    public function setQuantityProduct(int $quantityProduct): self
    {
        $this->quantityProduct = $quantityProduct;
        return $this;
    }

    /**
     * @return string|null
     */
    public function getExpectedDeliveryDate(): ?string
    {
        return $this->expectedDeliveryDate;
    }

    /**
     * @param string|null $expectedDeliveryDate
     * @return $this
     */
    public function setExpectedDeliveryDate(?string $expectedDeliveryDate): self
    {
        $this->expectedDeliveryDate = $expectedDeliveryDate;
        return $this;
    }

    /**
     * @return string|null
     */
    public function getExpectedPickupDate(): ?string
    {
        return $this->expectedPickupDate;
    }

    /**
     * @param string|null $expectedPickupDate
     * @return $this
     */
    public function setExpectedPickupDate(?string $expectedPickupDate): self
    {
        $this->expectedPickupDate = $expectedPickupDate;
        return $this;
    }

    /**
     * @return bool|null
     */
    public function getIsDelivered(): ?bool
    {
        return $this->isDelivered;
    }

    /**
     * @param bool|null $isDelivered
     * @return $this
     */
    public function setIsDelivered(?bool $isDelivered): self
    {
        $this->isDelivered = $isDelivered;
        return $this;
    }

    /**
     * @return float|null
     */
    public function getPriceTi(): ?float
    {
        return $this->priceTi;
    }

    /**
     * @param float|null $priceTi
     * @return $this
     */
    public function setPriceTi(?float $priceTi): self
    {
        $this->priceTi = $priceTi;
        return $this;
    }

    /**
     * @return string|null
     */
    public function getStatus(): ?string
    {
        return $this->status;
    }

    /**
     * @param string|null $status
     * @return $this
     */
    public function setStatus(?string $status): self
    {
        $this->status = $status;
        return $this;
    }

    /**
     * @return string
     */
    public function getCarrierName(): string
    {
        return $this->carrierName;
    }

    /**
     * @param string $carrierName
     * @return $this
     */
    public function setCarrierName(string $carrierName): self
    {
        $this->carrierName = $carrierName;
        return $this;
    }
}
