<?php

namespace Upela\Model;

final class Offer
{
    /**
     * @var int
     */
    protected $id;

    /**
     * @var string
     */
    protected $carrierCode;

    /**
     * @var string
     */
    protected $carrierName;

    /**
     * @var int|null
     */
    protected $serviceCode;

    /**
     * @var string
     */
    protected $serviceName;

    /**
     * @var int
     */
    protected $isExpress;

    /**
     * @var int
     */
    protected $allowPickup;

    /**
     * @var int
     */
    protected $allowDropoff;

    /**
     * @var int
     */
    protected $isSaas;

    /**
     * @var int
     */
    protected $vatRate;

    /**
     * @var float
     */
    protected $priceTe;

    /**
     * @var string
     */
    protected $currency;

    /**
     * @var float
     */
    protected $priceTi;

    /**
     * @return int
     */
    public function getId(): int
    {
        return $this->id;
    }

    /**
     * @param int $id
     * @return $this
     */
    public function setId(int $id): self
    {
        $this->id = $id;
        return $this;
    }

    /**
     * @return string
     */
    public function getCarrierCode(): string
    {
        return $this->carrierCode;
    }

    /**
     * @param string $carrierCode
     * @return $this
     */
    public function setCarrierCode(string $carrierCode): self
    {
        $this->carrierCode = $carrierCode;
        return $this;
    }

    /**
     * @return string
     */
    public function getCarrierName(): string
    {
        return $this->carrierName;
    }

    /**
     * @param string $carrierName
     * @return $this
     */
    public function setCarrierName(string $carrierName): self
    {
        $this->carrierName = $carrierName;
        return $this;
    }

    /**
     * @return int|null
     */
    public function getServiceCode(): ?int
    {
        return $this->serviceCode;
    }

    /**
     * @param int|null $serviceCode
     * @return $this
     */
    public function setServiceCode(?int $serviceCode): self
    {
        $this->serviceCode = $serviceCode;
        return $this;
    }

    /**
     * @return string
     */
    public function getServiceName(): string
    {
        return $this->serviceName;
    }

    /**
     * @param string $serviceName
     * @return $this
     */
    public function setServiceName(string $serviceName): self
    {
        $this->serviceName = $serviceName;
        return $this;
    }

    /**
     * @return int
     */
    public function getVatRate(): int
    {
        return $this->vatRate;
    }

    /**
     * @param int $vatRate
     * @return $this
     */
    public function setVatRate(int $vatRate): self
    {
        $this->vatRate = $vatRate;
        return $this;
    }

    /**
     * @return float
     */
    public function getPriceTe(): float
    {
        return $this->priceTe;
    }

    /**
     * @param float $priceTe
     * @return $this
     */
    public function setPriceTe(float $priceTe): self
    {
        $this->priceTe = $priceTe;
        return $this;
    }

    /**
     * @return string
     */
    public function getCurrency(): string
    {
        return $this->currency;
    }

    /**
     * @param string $currency
     * @return $this
     */
    public function setCurrency(string $currency): self
    {
        $this->currency = $currency;
        return $this;
    }

    /**
     * @return float
     */
    public function getPriceTi(): float
    {
        return $this->priceTi;
    }

    /**
     * @param float $priceTi
     * @return $this
     */
    public function setPriceTi(float $priceTi): self
    {
        $this->priceTi = $priceTi;
        return $this;
    }

    /**
     * @return int
     */
    public function getIsExpress(): int
    {
        return $this->isExpress;
    }

    /**
     * @param int $isExpress
     * @return $this
     */
    public function setIsExpress(int $isExpress): self
    {
        $this->isExpress = $isExpress;
        return $this;
    }

    /**
     * @return int
     */
    public function getAllowPickup(): int
    {
        return $this->allowPickup;
    }

    /**
     * @param int $allowPickup
     * @return $this
     */
    public function setAllowPickup(int $allowPickup): self
    {
        $this->allowPickup = $allowPickup;
        return $this;
    }

    /**
     * @return int
     */
    public function getAllowDropoff(): int
    {
        return $this->allowDropoff;
    }

    /**
     * @param int $allowDropoff
     * @return $this
     */
    public function setAllowDropoff(int $allowDropoff): self
    {
        $this->allowDropoff = $allowDropoff;
        return $this;
    }

    /**
     * @return int
     */
    public function getIsSaas(): int
    {
        return $this->isSaas;
    }

    /**
     * @param int $isSaas
     * @return $this
     */
    public function setIsSaas(int $isSaas): self
    {
        $this->isSaas = $isSaas;
        return $this;
    }

    /**
     * @return array
     */
    public function toArray(): array
    {
        return [
            'carrier_code' => $this->getCarrierCode(),
            'carrier_name' => $this->getCarrierName(),
            'service_code' => $this->getServiceCode(),
            'service_name' => $this->getServiceName(),
            'is_express' => $this->getIsExpress(),
            'allow_pickup' => $this->getAllowPickup(),
            'allow_dropoff' => $this->getAllowDropoff(),
            'is_saas' => $this->getIsSaas(),
            'vat_rate' => $this->getVatRate(),
            'price_te' => $this->getPriceTe(),
            'currency' => $this->getCurrency(),
            'price_ti' => $this->getPriceTi(),
        ];
    }
}
