<?php

namespace Upela\Request\Product;

use Upela\Model\Product;

final class CancelRequest
{
    /**
     * @var Product[]
     */
    private $products;

    /**
     * CancelRequest constructor.
     */
    public function __construct()
    {
        $this->products = [];
    }

    /**
     * @return Product[]
     */
    public function getProducts(): array
    {
        return $this->products;
    }

    /**
     * @param Product[] $products
     * @return $this
     */
    public function setProducts(array $products): self
    {
        $this->products = $products;
        return $this;
    }
}
