<?php

namespace Upela\Request\Product\Adapter\IzbergMerchantOrder;

use Open\IzbergBundle\Api\OrderApi;
use Open\IzbergBundle\Model\Item;
use Upela\Model\Product;
use Upela\Request\Product\CancelRequest;
use Upela\Request\Product\CancelRequestBuilderInterface;

class CancelRequestBuilder implements CancelRequestBuilderInterface
{
    /**
     * @var OrderApi
     */
    private $orderApi;

    public function __construct(OrderApi $orderApi)
    {
        $this->orderApi = $orderApi;
    }

    /**
     * @param array $orderItems
     * @return CancelRequest
     */
    public function build($orderItems): CancelRequest
    {
        // quantity = Max_invoiceable + invoiced - current refund qty
        $products = array_filter(
            array_map(
                function(Item $item): ?Product
                {
                    // retrieve order Item to get max_invoiceable, invoiced fields
                    $izbergOrderItem = $this->orderApi->fetchOrderItemById($item->getId());

                    // Do not handle shipping order item
                    if (preg_match('/^shipment-[0-9]+$/', $izbergOrderItem->getOfferExternalId())) {
                        return null;
                    }

                    $refundQuantity = $item->getQuantity();
                    $maxInvoiceableQuantity = $izbergOrderItem->getMaxInvoiceable();
                    $invoicedQuantity = $izbergOrderItem->getInvoicedQuantity();

                    $quantity = $maxInvoiceableQuantity + $invoicedQuantity - $refundQuantity;

                    if ($item->getStatus() === "2000") {
                        $quantity = 0;
                    }

                    $product = (new Product())
                        ->setProductId($item->getId())
                        ->setQuantity($quantity)
                    ;

                    return $product;
                },
                $orderItems
            )
        );

        $request = (new CancelRequest())
            ->setProducts($products);

        return $request;
    }
}
