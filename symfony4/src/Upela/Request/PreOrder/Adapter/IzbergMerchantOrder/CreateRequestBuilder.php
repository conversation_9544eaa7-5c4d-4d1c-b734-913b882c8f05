<?php

namespace Upela\Request\PreOrder\Adapter\IzbergMerchantOrder;

use AppBundle\Entity\Order;
use AppBundle\Repository\OrderRepository;
use AppBundle\Services\MerchantService;
use AppBundle\Services\OfferService;
use AppBundle\Services\ShippingService;
use Open\IzbergBundle\Api\OrderApi;
use Open\IzbergBundle\Model\MerchantOrder;
use Upela\Model\Address;
use Upela\Model\Offer;
use Upela\Model\Product;
use Upela\Request\PreOrder\CreateRequest;
use Upela\Request\PreOrder\CreateRequestBuilderInterface;
use Upela\Request\PreOrder\MerchantOrderInterface;

class CreateRequestBuilder implements CreateRequestBuilderInterface
{
    /**
     * @var OrderApi
     */
    private $orderApi;

    /**
     * @var OrderRepository
     */
    private $orderRepository;

    /**
     * @var OfferService
     */
    private $offerService;

    /**
     * @var ShippingService
     */
    private $shippingService;

    /**
     * @var MerchantService
     */
    private $merchantService;

    public function __construct(
        OrderApi $orderApi,
        OrderRepository $orderRepository,
        OfferService $offerService,
        ShippingService $shippingService,
        MerchantService $merchantService
    )
    {
        $this->orderApi = $orderApi;
        $this->orderRepository = $orderRepository;
        $this->offerService = $offerService;
        $this->shippingService = $shippingService;
        $this->merchantService = $merchantService;
    }

    /**
     * @param MerchantOrderInterface $data
     * @return CreateRequest
     * @throws \Exception
     */
    public function build(MerchantOrderInterface $data): CreateRequest
    {
        if (!$data instanceof MerchantOrder) {
            throw new \InvalidArgumentException();
        }
        $merchantOrder = $data;

        $shippingOptions = $this->orderApi->fetchMerchantOrderShippingOptions($merchantOrder->getId());

        /** @var Order $orderEntity */
        $orderEntity = $this->orderRepository->findOneBy(['izbergId' => $merchantOrder->getOrder()->getId()]);
        $shippingAddress = $orderEntity->getAddress();

        $buyerInternalOrderId = $merchantOrder->getAttributes()['ZZC-Internal-Buyer-Order-ID'] ?? '';

        $products = [];
        foreach($shippingOptions as $shippingOption) {
            array_walk(
                $shippingOption->order_items,
                function(\stdClass $orderItem) use ($shippingOption, $shippingAddress, $orderEntity, &$products) {
                    $izbergOrderItem = $this->orderApi->getOrderItemById($orderItem->id);

                    // do not create product in upela if order item is cancelled (2000)
                    if ($izbergOrderItem->status == 2000) {
                        return;
                    }

                    // do not create product for order item that represents shipping price
                    if (preg_match('/^shipment-[0-9]+$/', $izbergOrderItem->offer_external_id)) {
                        return;
                    }

                    $izbergShippingOffer = $shippingOption->options->offer;
                    $productOffer = $this->offerService->findOfferById($izbergOrderItem->product_offer->id);

                    $merchant = $this->merchantService->findMerchantById($productOffer->getMerchant()->getId());

                    $addressFrom = (new Address())
                        ->setZip($productOffer->getFcaZipCode() ?? '')
                        ->setCity($productOffer->getFcaZipTown() ?? '')
                        ->setCompany($productOffer->getMerchant()->getName() ?? '')
                        ->setAddress2('')
                        ->setCountryISO($izbergShippingOffer->countryCode ?? '')
                        ->setAddress1($productOffer->getFcaAddress() ?? '')
                        ->setContactName($merchant->getMainContactName() ?? '')
                        ->setContactEmail($merchant->getMainContactEmail() ?? '')
                        ->setContactPhone($merchant->getMainContactPhone() ?? '');

                    $addressTo = (new Address())
                        ->setZip($shippingAddress->getZipCode() ?? '')
                        ->setCity($shippingAddress->getCity() ?? '')
                        ->setCompany($orderEntity->getCompany()->getName() ?? '')
                        ->setAddress2($shippingAddress->getAddress2() ?? '')
                        ->setCountryISO($shippingAddress->getCountry()->getIzbergCode())
                        ->setAddress1($shippingAddress->getAddress() ?? '')
                        ->setContactName((string)$orderEntity->getShippingPoint()->getContact())
                        ->setContactEmail($orderEntity->getShippingPoint()->getContact()->getEmail() ?? '')
                        ->setContactPhone($orderEntity->getShippingPoint()->getContact()->getPhone1() ?? '');


                    $izbergShippingParcel = $this->shippingService->computePackage($productOffer, $orderItem->quantity);

                    // need to stringify parcel info prior to send creation preorder request to upela
                    $izbergShippingParcel = array_map(
                        function($parcel) {
                            return json_encode($parcel);
                        },
                        $izbergShippingParcel
                    );

                    $offer = (new Offer())
                        ->setCarrierCode($izbergShippingOffer->carrierCode ?? '')
                        ->setCarrierName($izbergShippingOffer->carrierName ?? '')
                        ->setServiceCode((int)$izbergShippingOffer->serviceCode)
                        ->setServiceName($izbergShippingOffer->serviceName ?? '')
                        ->setIsExpress(0)
                        ->setAllowPickup(1)
                        ->setAllowDropoff(0)
                        ->setIsSaas(0)
                        ->setVatRate(intval($izbergShippingOffer->vatRate))
                        ->setPriceTe(floatval($izbergShippingOffer->upelaPriceTe))
                        ->setCurrency($izbergShippingOffer->currency ?? '')
                        ->setPriceTi(floatval($izbergShippingOffer->upelaPriceTi))
                    ;

                    $product = (new Product())
                        ->setShipmentId($shippingOption->options->shipmentId ?? null)
                        ->setProductId($orderItem->id)
                        ->setQuantity($orderItem->quantity)
                        ->setQuantitySent(0)
                        ->setSku($productOffer->getSellerRef())
                        ->setStatus(Product::STATUS_OPEN)
                        ->setStackable($productOffer->isStackableProduct())
                        ->setHsCode($productOffer->getCustomTariffCode())
                        ->setDesignation($productOffer->getOfferTitle())
                        ->setParcels($izbergShippingParcel)
                        ->setParcelType($izbergShippingOffer->parcelType)
                        ->setCurrency($productOffer->getCurrency())
                        ->setExpectedPickupDate((new \DateTimeImmutable($shippingOption->options->offer->shipmentDate))->format('Y-m-d H:i:s'))
                        ->setExpectedDeliveryDate((new \DateTimeImmutable($shippingOption->options->offer->deliveryDate))->format('Y-m-d H:i:s'))
                        ->setSelectedOffer($offer)
                        ->setAddressFrom($addressFrom)
                        ->setAddressTo($addressTo);

                    $products[] = $product;
                }
            );
        }

        return (new CreateRequest())
            ->setMerchantOrderId($merchantOrder->getId())
            ->setBuyerInternalOrderId($buyerInternalOrderId)
            ->setOrderNumber($merchantOrder->getOrder()->getIdNumber())
            ->setToken(strval($merchantOrder->getMerchant()->getId()))
            ->setProducts($products);
    }
}
