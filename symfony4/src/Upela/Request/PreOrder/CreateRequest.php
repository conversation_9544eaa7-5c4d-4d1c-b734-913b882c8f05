<?php

namespace Upela\Request\PreOrder;

use Upela\Model\Product;

final class CreateRequest
{
    private const PREORDER_STATUS_OPEN = 'Open';

    /**
     * @var int
     */
    private $merchantOrderId;

    /**
     * @var string
     */
    private $buyerInternalOrderId;

    /**
     * @var string
     */
    private $orderNumber;

    /**
     * @var string
     */
    private $token;

    /**
     * @var array<Product>
     */
    private $products;

    public function getMerchantOrderId(): int
    {
        return $this->merchantOrderId;
    }

    public function setMerchantOrderId(int $merchantOrderId): self
    {
        $this->merchantOrderId = $merchantOrderId;
        return $this;
    }

    public function getBuyerInternalOrderId(): string
    {
        return $this->buyerInternalOrderId;
    }

    public function setBuyerInternalOrderId(string $buyerInternalOrderId): self
    {
        $this->buyerInternalOrderId = $buyerInternalOrderId;
        return $this;
    }

    public function getOrderNumber(): string
    {
        return $this->orderNumber;
    }

    public function setOrderNumber(string $orderNumber): self
    {
        $this->orderNumber = $orderNumber;
        return $this;
    }

    public function getToken(): string
    {
        return $this->token;
    }

    public function setToken(string $token): self
    {
        $this->token = $token;
        return $this;
    }

    public function getProducts(): array
    {
        return $this->products;
    }

    public function setProducts(array $products): self
    {
        $this->products = $products;
        return $this;
    }

    public function toArray() {
        return [
            'merchantOrderId' => $this->merchantOrderId,
            'buyerInternalOrderId' => $this->buyerInternalOrderId,
            'orderNumber' => $this->orderNumber,
            'token' => $this->token,
            'status' => self::PREORDER_STATUS_OPEN,
            'products' => array_map(
                function(Product $product) {
                    return $product->toArray();
                },
                $this->products
            ),
        ];
    }
}
