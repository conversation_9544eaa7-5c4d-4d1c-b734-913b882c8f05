<?php

namespace AppBundle\Twig;

use AppBundle\Services\OfferService;
use Twig\Extension\AbstractExtension;
use Twig\TwigFunction;

class pdfCheckExtension extends AbstractExtension
{
    /**
     * @return TwigFunction[]
     */
    public function getFunctions()
    {
        return array(
            new TwigFunction('checkPdf', array($this, 'checkPdf')),
        );
    }

    /**
     * @param string $url
     * @return bool
     */
    public function checkPdf(string $url)
    {
        if (filter_var($url, FILTER_VALIDATE_URL)) {
            return OfferService::isPdf($url);
        }
        return false;
    }
}
