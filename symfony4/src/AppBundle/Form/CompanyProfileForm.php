<?php
/**
 * Created by PhpStorm.
 * User: AQU04740
 * Date: 08/03/2017
 * Time: 16:23
 */

namespace AppBundle\Form;

use AppBundle\Entity\User;
use AppBundle\Services\LanguageService;
use Symfony\Component\Form\AbstractType;
use Symfony\Component\Form\Extension\Core\Type\ChoiceType;
use Symfony\Component\Form\FormBuilderInterface;
use Symfony\Component\Form\Extension\Core\Type\SubmitType;
use Symfony\Component\Form\Extension\Core\Type\TextType;
use Symfony\Component\OptionsResolver\OptionsResolver;
use Symfony\Component\Validator\Constraints\NotBlank;

class CompanyProfileForm extends AbstractType
{
    const LABEL = "label";
    const PLACEHOLDER = "placeholder";
    const SUBMIT = "submit";
    const VALIDATION_GROUPS = "validation_groups";
    const MAIN_ADDRESS = "mainAddress";
    const BILLING_ADDRESS = "billingAddress";
    const MULTIPLE = "multiple";
    const REQUIRED = "required";

    public function buildForm(FormBuilderInterface $builder, array $options)
    {
        $languageService = $options['languageService'] ?? null;

        $builder
            ->add(
                'firstname',
                TextType::class,
                array(
                    self::LABEL => 'profile.form.firstname',
                    'attr' => array(
                        self::PLACEHOLDER => 'profile.form.firstname'
                    )
                )

            )
            ->add(
                'lastname',
                TextType::class,
                array(
                    self::LABEL => 'profile.form.lastname',
                    'attr' => array(
                        self::PLACEHOLDER => 'profile.form.lastname'
                    )
                )
            )
            ->add(
                'mainPhoneNumber',
                TextType::class,
                array(
                    self::LABEL => 'profile.form.main_phone_number',
                    'attr' => array(
                        self::PLACEHOLDER => 'profile.form.main_phone_number'
                    )
                )

            )
            ->add(
                'email',
                TextType::class,
                array(
                    self::LABEL => 'profile.form.email',
                    'attr' => array(
                        self::PLACEHOLDER => 'profile.form.email'
                    )
                )
            );

        if ($languageService instanceof LanguageService) {
            $builder
                ->add(
                    'locale',
                    ChoiceType::class,
                    [
                        'label' => 'back.user.form.language',
                        'choices' => $languageService->getLanguageCodes(),
                        'choice_label' => function($choiceValue, $key, $value) {
                            return 'node.form.lang.' . $value;
                        },
                        'choice_translation_domain' => 'AppBundle',
                        'placeholder' => 'node.form.lang.select',
                        'translation_domain' => 'AppBundle',
                        'constraints' => [new NotBlank()],
                        'attr' => [
                            'required' => 'required',
                        ],
                    ]
                );
        }

        $builder
            ->add(
                self::SUBMIT,
                SubmitType::class,
                array(
                    "attr" => array (
                        "value" => "submit",
                        "class" => "Button button_margin"
                    ),
                    self::LABEL => 'profile.form.submit'
                )
            )
            ->setMethod('POST');

    }

    public function configureOptions(OptionsResolver $resolver)
    {
        $resolver->setDefaults(
            [
                'jquery_validation_groups' => ['Default','profile'],
                'data_class' => User::class,
                'translation_domain' => 'AppBundle',
                'validation_groups' => ['Default'],
                'languageService' => null,
            ]
        );
    }
}
