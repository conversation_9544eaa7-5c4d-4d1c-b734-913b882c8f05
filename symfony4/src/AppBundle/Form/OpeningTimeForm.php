<?php
/**
 * Created by PhpStorm.
 * User: AQU04740
 * Date: 24/01/2018
 * Time: 17:20
 */

namespace AppBundle\Form;

use AppBundle\Entity\OpeningTime;
use Symfony\Component\Form\AbstractType;
use Symfony\Component\Form\Extension\Core\Type\HiddenType;
use Symfony\Component\Form\FormBuilderInterface;
use Symfony\Component\OptionsResolver\OptionsResolver;
use Symfony\Component\Form\Extension\Core\Type\TextType;


class OpeningTimeForm extends AbstractType
{
    const LABEL = "label";
    const REQUIRED = "required";
    const CONSTRAINTS = "constraints";
    const GROUPS = "groups";
    const CLAZZ = 'class';
    const TIMEPICKER = 'timepicker';

    /**
     * Build address new/edit form
     * @param FormBuilderInterface $builder
     * @param array $options
     */
    public function buildForm(FormBuilderInterface $builder, array $options)
    {



        $builder
            ->add(
                'morningStart',
                TextType::class,
                array(
                    self::LABEL => false,
                    'attr'=> array(self::CLAZZ=> self::TIMEPICKER)

                )
            )
            ->add(
                'morningEnd',
                TextType::class,
                array(
                    self::LABEL => false,
                    'attr'=> array(self::CLAZZ=> self::TIMEPICKER)
                )
            )
            ->add(
                'afternoonStart',
                TextType::class,
                array(
                    self::LABEL => false,
                    'attr'=> array(self::CLAZZ=> self::TIMEPICKER)
                )
            )
            ->add(
                'afternoonEnd',
                TextType::class,
                array(
                    self::LABEL => false,
                    'attr'=> array(self::CLAZZ=> self::TIMEPICKER)
                )

            )
        ->add('day',HiddenType ::class,array());
    }

    /**
     * Configure default options
     * @param OptionsResolver $resolver
     */
    public function configureOptions(OptionsResolver $resolver)
    {
        $resolver->setDefaults(array(
            'jquery_validation_groups' => array('Default'),
            'data_class' => OpeningTime::class,
            'translation_domain' => 'AppBundle',
            'validation_groups' => array('Default')
        ));
    }
}
