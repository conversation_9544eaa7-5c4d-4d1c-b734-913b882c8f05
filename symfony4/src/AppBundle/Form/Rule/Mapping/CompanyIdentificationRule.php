<?php
namespace AppBundle\Form\Rule\Mapping;

use AppB<PERSON>le\Validator\Constraints\CompanyIdentification;
use <PERSON><PERSON><PERSON><PERSON>\Bundle\JqueryValidationBundle\Exception\LogicException;
use <PERSON><PERSON><PERSON><PERSON>\Bundle\JqueryValidationBundle\Form\Rule\ConstraintRule;
use <PERSON><PERSON><PERSON><PERSON>\Bundle\JqueryValidationBundle\Form\Rule\ConstraintMapperInterface;
use <PERSON><PERSON><PERSON><PERSON>\Bundle\JqueryValidationBundle\Form\RuleCollection;
use <PERSON><PERSON>ko<PERSON>\Bundle\JqueryValidationBundle\Form\RuleMessage;
use Symfony\Component\Form\FormInterface;
use Symfony\Component\Validator\Constraint;

class CompanyIdentificationRule implements ConstraintMapperInterface
{
    const RULE_NAME = 'company-identification';

    /**
     * {@inheritdoc}
     */
    public function resolve(Constraint $constraint, FormInterface $form, RuleCollection $collection)
    {
        if (!$this->supports($constraint, $form)) {
            throw new LogicException();
        }
        /**
         * @var CompanyIdentification $constraint
         */
        $collection->set(
            self::RULE_NAME,
            new ConstraintRule(
                self::RULE_NAME,
                true,
                new RuleMessage($constraint->getMessage()),
                $constraint->groups
            )
        );
    }

    /**
     * @param Constraint $constraint
     * @param FormInterface $form
     * @return bool
     */
    public function supports(Constraint $constraint, FormInterface $form)
    {

        return get_class($constraint) === CompanyIdentification::class;
    }
}
