<?php
/**
 * Created by PhpStorm.
 * User: AQU04740
 * Date: 23/03/2017
 * Time: 10:31
 */

namespace AppBundle\Form;

use AppBundle\Entity\Contact;
use Symfony\Component\Form\AbstractType;
use Symfony\Component\Form\Extension\Core\Type\CheckboxType;
use Symfony\Component\Form\Extension\Core\Type\EmailType;
use Symfony\Component\Form\FormBuilderInterface;
use Symfony\Component\OptionsResolver\OptionsResolver;
use Symfony\Component\Form\Extension\Core\Type\TextType;

class ContactForm extends AbstractType
{
    const LABEL = "label";
    const REQUIRED = "required";
    const CONSTRAINTS = "constraints";
    const GROUPS = "groups";

    /**
     * Build address new/edit form
     * @param FormBuilderInterface $builder
     * @param array $options
     */
    public function buildForm(FormBuilderInterface $builder, array $options)
    {

        if(!$options['no_check']){
            $builder
                ->add(
                    'check',
                    CheckboxType::class,
                    array(
                        self::LABEL => 'company.form.contact.check.billing'
                    )
                );
        }

        $builder
            ->add(
                'firstName',
                TextType::class,
                array(
                    self::LABEL => 'contact.form.firstname',
                    self::REQUIRED => true
                )
            )
            ->add(
                'lastName',
                TextType::class,
                array(
                    self::LABEL => 'contact.form.lastname',
                    self::REQUIRED => true
                )
            )
            ->add(
                'email',
                EmailType::class,
                array(
                    self::LABEL => 'contact.form.email',
                    self::REQUIRED => true
                )
            )
            ->add(
                'phone1',
                TextType::class,
                array(
                    self::LABEL => 'contact.form.phone1',
                    self::REQUIRED => true,
                )
            )
            ->add(
                'phone2',
                TextType::class,
                array(
                    self::LABEL => 'contact.form.phone2',
                    self::REQUIRED => true
                )

            )
            ->add(
                'function',
                TextType::class,
                array(
                    self::LABEL => 'contact.form.function',
                    self::REQUIRED => true
                )
            );
    }

    /**
     * Configure default options
     * @param OptionsResolver $resolver
     */
    public function configureOptions(OptionsResolver $resolver)
    {
        $resolver->setDefaults(array(
            'no_check' => false,
            'data_class' => Contact::class,
            'translation_domain' => 'AppBundle',
            'validation_groups' => array('Default','contact')
        ));
    }
}
