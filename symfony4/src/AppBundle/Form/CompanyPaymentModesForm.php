<?php
/**
 * Created by PhpStorm.
 * User: AQU04740
 * Date: 08/03/2017
 * Time: 16:23
 */

namespace AppBundle\Form;

use AppBundle\Entity\Company;
use Symfony\Component\Form\AbstractType;
use Symfony\Component\Form\Extension\Core\Type\FileType;
use Symfony\Component\Form\FormBuilderInterface;
use Symfony\Component\Form\Extension\Core\Type\SubmitType;
use Symfony\Component\Form\Extension\Core\Type\TextType;
use Symfony\Component\Form\Extension\Core\Type\CheckboxType;
use Symfony\Component\OptionsResolver\OptionsResolver;
use Symfony\Component\Validator\Constraints\All;
use Symfony\Component\Validator\Constraints\File;

class CompanyPaymentModesForm extends AbstractType
{
    const LABEL = "label";
    const SUBMIT = "submit";
    const MULTIPLE = "multiple";
    const VALIDATION_GROUPS = "validation_groups";
    const REQUIRED = "required";


    public function buildForm(FormBuilderInterface $builder, array $options)
    {
        $builder
            ->add(
                'businessRegistration',
                FileType::class,
                array(
                    'mapped' => false,
                    self::LABEL => 'company.form.businessRegistration',
                    self::MULTIPLE => true,
                    'constraints' => new All(new File(['maxSize' => '5M'])),
                )
            )
            ->add(
                'prepaymentCreditcardEnabled',
              CheckboxType::class,
                array(
                    self::LABEL => Company::CREDIT_CARD,
                  'disabled'=>true,
                )
            )
          ->add(
            'prepaymentMoneyTransfertEnabled',
            CheckboxType::class,
            array(
              self::LABEL => Company::MONEY_TRANSFERT,
              'disabled'=>true,
              )

          )
          ->add(
            'termpayment_moneytransfert_enabled',
            CheckboxType::class,
            array(
              self::LABEL => Company::TERM_PAYMENT,
              'disabled'=> ($options['payment_term_is_pending'] || $options['payment_term_is_enabled']),
            )

          )
            ->add(
                self::SUBMIT,
                SubmitType::class,
                array(
                    "attr" => array (
                        "value" => "submit",
                        "class" => "Button button_margin"
                    ),
                    self::LABEL => 'payment.form.submit'
                )
            )
            ->setMethod('POST');

    }

  public function configureOptions(OptionsResolver $resolver)
  {
    $resolver->setDefaults(array(
      'data_class' => Company::class,
      'translation_domain' => 'AppBundle',
      'validation_groups' => array('Default'),
      'payment_term_is_pending' => false,
      'payment_term_is_enabled' => false,
      'disabled' => false,
    ));
  }
}
