<?php
/**
 * Created by PhpStorm.
 * User: AQU04740
 * Date: 08/03/2017
 * Time: 16:23
 */

namespace AppBundle\Form;

use Symfony\Component\Form\AbstractType;
use Symfony\Component\Form\FormBuilderInterface;
use Symfony\Component\Form\Extension\Core\Type\SubmitType;
use Symfony\Component\Form\FormEvent;
use Symfony\Component\Form\FormEvents;
use Symfony\Component\Form\FormInterface;
use Symfony\Component\OptionsResolver\OptionsResolver;


class CompanyContactForm extends AbstractType
{
    const LABEL = "label";
    const SUBMIT = "submit";
    const VALIDATION_GROUPS = "validation_groups";
    const MAIN_CONTACT = "mainContact";
    const BILLING_CONTACT = "billingContact";
    const DEF = 'Default';
    const CONTACT = 'Contact';
    const CHECK = 'check';


    public function buildForm(FormBuilderInterface $builder, array $options)
    {
        $builder
            ->add(
                'mainContact',
                ContactForm::class,
                array(self::LABEL => 'company.form.name')

            )
            ->addEventListener(FormEvents::PRE_SET_DATA, function (FormEvent $event) {
                $form = $event->getForm();

                $form->add('billingContact', ContactForm::class, array(
                    self::VALIDATION_GROUPS => function (FormInterface $form) {
                        if ($form->get(self::CHECK)->getData() === false) {
                            return array();
                        }
                        return array(self::DEF, self::CONTACT);
                    }
                ));

                $form->add('advContact', ContactForm::class, array(
                    self::VALIDATION_GROUPS => function (FormInterface $form) {
                        if ($form->get(self::CHECK)->getData() === false) {
                            return array();
                        }
                        return array(self::DEF, self::CONTACT);
                    }
                ));

                $form->add('logisticContact', ContactForm::class, array(
                    self::VALIDATION_GROUPS => function (FormInterface $form) {
                        if ($form->get(self::CHECK)->getData() === false) {
                            return array();
                        }
                        return array(self::DEF, self::CONTACT);
                    }
                ));

            })
            ->add(
                'save',
                SubmitType::class,
                array(
                    "attr" => array (
                        "value" => "save",
                        "class" => "Button button_margin"
                    ),
                    self::LABEL => 'company.form.next'
                )
            )
            ->setMethod('POST');

    }

    public function configureOptions(OptionsResolver $resolver)
    {
        $resolver->setDefaults(array(

            'translation_domain' => 'AppBundle'
        ));
    }
}
