<?php

namespace AppBundle\Form;

use AppBundle\Entity\Company;
use AppBundle\Validator\Constraints\CompanyIdentification;
use Symfony\Component\Form\AbstractType;
use AppBundle\FormAddressWoCountryEditableForm;
use Symfony\Component\Form\Extension\Core\Type\CheckboxType;
use Symfony\Component\Form\FormBuilderInterface;
use Symfony\Component\Form\Extension\Core\Type\SubmitType;
use Symfony\Component\Form\Extension\Core\Type\TextType;
use Symfony\Component\Form\FormEvent;
use Symfony\Component\Form\FormEvents;
use Symfony\Component\Form\FormInterface;
use Symfony\Component\OptionsResolver\OptionsResolver;
use Symfony\Component\Form\Extension\Core\Type\FileType;
use Symfony\Component\Validator\Constraints\All;
use Symfony\Component\Validator\Constraints\File;

class CompanyInfoForm extends AbstractType
{

  const LABEL = "label";

  const SUBMIT = "submit";

  const VALIDATION_GROUPS = "validation_groups";

  const MAIN_ADDRESS = "mainAddress";

  const BILLING_ADDRESS = "billingAddress";

  const MULTIPLE = "multiple";

  const CONSTRAINTS = 'constraints';

  public function buildForm(FormBuilderInterface $builder, array $options)
  {

    $builder
      ->add(
        'identification',
        null,
        array(
          'mapped' => true,
          self::CONSTRAINTS => array(
            new CompanyIdentification(),
          ),
          self::LABEL => 'company.form.identification',
          'disabled' => true,
          'required' => false,
        )
      )
      ->add(
        'name',
        TextType::class,
        array(
          self::LABEL => 'company.form.name',
          'disabled' => true,
          'required' => false,
        )
      )
      ->add(
        'businessRegistration',
        FileType::class,
        array(
            'mapped' => false,
            self::LABEL => 'company.form.businessRegistration',
            self::MULTIPLE => true,
            'constraints' => new All(new File(['maxSize' => '5M'])),
        )
      )
     ->add(
            'cgu',
            CheckboxType::class,
            [
                self::LABEL => 'cgu.accept',
            ]
        )
     ->add(
        self::MAIN_ADDRESS,
        AddressWoCountryEditableForm::class,
        array(
          self::LABEL => false,
        )
      )
      ->add(
        'billingService',
        TextType::class,
        array(
          self::LABEL => 'company.form.billingService',
        )
      )
      ->addEventListener(FormEvents::PRE_SET_DATA, function (FormEvent $event) {
        $form = $event->getForm();

        $form->add(self::BILLING_ADDRESS, AddressForm::class, array(
            'validation_groups' => function (FormInterface $form) {
              if ($form->get('check')->getData() === false) {
                return array();
              }
              return array('Default', 'Address');
            }
          , self::LABEL => false,
          )
        );
      })->add(
        'save',
        SubmitType::class,
        array(
          "attr" => array(
            "value" => "save",
            "class" => "Button button_margin",
          ),
          self::LABEL => 'company.form.submit',
        )
      )
      ->setMethod('POST');

  }

  public function configureOptions(OptionsResolver $resolver)
  {
    $resolver->setDefaults(
      array(
        'jquery_validation_groups' => array('Default'),
        'data_class' => Company::class,
        'translation_domain' => 'AppBundle',
      )
    );

  }
}
