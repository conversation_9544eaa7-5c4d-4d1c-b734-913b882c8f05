<?php
/**
 * Created by PhpStorm.
 * User: QAR14123
 * Date: 10/07/2018
 * Time: 09:30
 */

namespace AppBundle\Form;


use Symfony\Component\Form\AbstractType;
use Symfony\Component\Form\Extension\Core\Type\SubmitType;
use Symfony\Component\Form\Extension\Core\Type\TextareaType;
use Symfony\Component\Form\FormBuilderInterface;
use Symfony\Component\OptionsResolver\OptionsResolver;

class DisputeForm extends AbstractType
{
    const LABEL = "label";
    const REQUIRED = "required";
    const CONSTRAINTS = "constraints";
    const GROUPS = "groups";
    const TRANSLATION_DOMAIN = "AppBundle";

    /**
     * Build address new/edit form
     * @param FormBuilderInterface $builder
     * @param array $options
     */
    public function buildForm(FormBuilderInterface $builder, array $options)
    {
        $builder
            ->add('message',
                TextareaType::class,
                array(
                    self::LABEL => 'dispute.form.message',
                    "attr" => array (
                        "class" => "full_width",
                        "placeholder" => "dispute.form.placeholder",
                        self::REQUIRED => self::REQUIRED
                    ),
                    'translation_domain' => self::TRANSLATION_DOMAIN,
                )
            )->add(
                'save',
                SubmitType::class,
                array(
                    "attr" => array (
                        "value" => "submit",
                        "class" => "Button button_margin"
                    ),
                    self::LABEL => 'contactMerchant.form.save'
                )
            );
    }

    /**
     * Configure default options
     * @param OptionsResolver $resolver
     */
    public function configureOptions(OptionsResolver $resolver)
    {
        $resolver->setDefaults(array(
            'no_check' => false,
            'jquery_validation_groups' => array('Default'),
            'translation_domain' => 'AppBundle',
            'validation_groups' => array('Default')
        ));
    }
}
