<?php
/**
 * Created by PhpStorm.
 * User: CMA15450
 * Date: 28/03/2017
 * Time: 17:54
 */
namespace AppBundle\Form\Type;

use Symfony\Component\Form\AbstractType;
use Symfony\Component\OptionsResolver\OptionsResolver;
use Symfony\Component\Form\Extension\Core\Type\TextType;

class PhoneType extends AbstractType
{

    public function configureOptions(OptionsResolver $resolver)
    {
        $resolver->setDefaults(array(
            'placeholder' => 'profile.phone.choose'
        ));
    }

    public function getParent()
    {
        return TextType::class;
    }
}
