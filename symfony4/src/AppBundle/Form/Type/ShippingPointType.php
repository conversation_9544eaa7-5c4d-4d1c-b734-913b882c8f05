<?php

namespace AppBundle\Form\Type;

use AppBundle\Entity\ShippingPoint;
use Symfony\Component\Form\AbstractType;
use Symfony\Component\Form\Extension\Core\Type\EmailType;
use Symfony\Component\Form\Extension\Core\Type\SubmitType;
use Symfony\Component\Form\Extension\Core\Type\TextareaType;
use Symfony\Component\Form\Extension\Core\Type\TextType;
use Symfony\Component\Form\FormBuilderInterface;
use Symfony\Component\OptionsResolver\OptionsResolver;

class ShippingPointType extends AbstractType
{
    const LABEL = "label";
    const REQUIRED = "required";
    const ADDRESS = "address";
    const CONTACT = "contact";

    public function buildForm(FormBuilderInterface $builder, array $options)
    {
        $builder
            ->add(
                'name',
                TextType::class,
                array(
                    self::LABEL => 'shipping_point.form.name',
                    self::REQUIRED => true,
                ))
            ->add('comment', TextareaType::class, array(
                self::LABEL => 'shipping_point.form.comment.label',
                self::REQUIRED => true,
                'attr' => [
                    'class' => 'custom-text-area'
                ]
            ))
            ->add(
                self::ADDRESS,
                AddressType::class,
                array(
                    self::LABEL => false,
                    self::REQUIRED => true,
                ))
            ->add(
                self::CONTACT,
                ContactType::class,
                array(
                    self::LABEL => 'shipping_point.form.contact.label'
                ))
            ->add('accountantEmail', EmailType::class, [
                self::LABEL => 'site.form.accountant_email',
                'attr' => [
                    'placeholder' => 'site.form.accountant_email',
                    'autocomplete' => 'off'
                ]
            ])
            ->add('packagingRequest1', TextType::class, [
                self::LABEL => 'site.form.packaging_request_1',
                'attr' => [
                    'placeholder' => 'site.form.packaging_request_1',
                ]
            ])
            ->add('packagingRequest2', TextType::class, [
                self::LABEL => 'site.form.packaging_request_2',
                'attr' => [
                    'placeholder' => 'site.form.packaging_request_2',
                ]
            ])
            ->add('packagingRequest3', TextType::class, [
                self::LABEL => 'site.form.packaging_request_3',
                'attr' => [
                    'placeholder' => 'site.form.packaging_request_3',
                ]
            ])
            ->add('documentationRequest1', TextType::class, [
                self::LABEL => 'site.form.documentation_request_1',
                'attr' => [
                    'placeholder' => 'site.form.documentation_request_1'
                ]
            ])
            ->add('documentationRequest2', TextType::class, [
                self::LABEL => 'site.form.documentation_request_2',
                'attr' => [
                    'placeholder' => 'site.form.documentation_request_2'
                ]
            ])
            ->add('documentationRequest3', TextType::class, [
                self::LABEL => 'site.form.documentation_request_3',
                'attr' => [
                    'placeholder' => 'site.form.documentation_request_3'
                ]
            ])
            ->add('documentationRequest4', TextType::class, [
                self::LABEL => 'site.form.documentation_request_4',
                'attr' => [
                    'placeholder' => 'site.form.documentation_request_4'
                ]
            ])
            ->add('documentationRequest5', TextType::class, [
                self::LABEL => 'site.form.documentation_request_5',
                'attr' => [
                    'placeholder' => 'site.form.documentation_request_5'
                ]
            ])
            ->add('documentationRequest6', TextType::class, [
                self::LABEL => 'site.form.documentation_request_6',
                'attr' => [
                    'placeholder' => 'site.form.documentation_request_6'
                ]
            ])
            ->add('documentationRequest7', TextType::class, [
                self::LABEL => 'site.form.documentation_request_7',
                'attr' => [
                    'placeholder' => 'site.form.documentation_request_7'
                ]
            ])
            ->add('documentationRequest8', TextType::class, [
                self::LABEL => 'site.form.documentation_request_8',
                'attr' => [
                    'placeholder' => 'site.form.documentation_request_8'
                ]
            ])
            ->add('documentationRequest9', TextType::class, [
                self::LABEL => 'site.form.documentation_request_9',
                'attr' => [
                    'placeholder' => 'site.form.documentation_request_9'
                ]
            ])
            ->add('documentationRequest10', TextType::class, [
                self::LABEL => 'site.form.documentation_request_10',
                'attr' => [
                    'placeholder' => 'site.form.documentation_request_10'
                ]
            ])
            ->add(
                'save',
                SubmitType::class,
                array(
                    "attr" => array(
                        "value" => "save",
                        "class" => "Button button_margin",
                    ),
                    self::LABEL => 'shipping_point.form.save',
                )
            )
            ->setMethod('POST');

        $builder->get(self::ADDRESS)->remove('country');
    }

    public function configureOptions(OptionsResolver $resolver)
    {
        $resolver->setDefaults(array(
            'jquery_validation_groups' => ['Default'],
            'data_class' => ShippingPoint::class,
            'translation_domain' => 'AppBundle',
            'validation_groups' => ['Default']
        ));
    }
}
