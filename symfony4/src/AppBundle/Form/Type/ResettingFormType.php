<?php
/**
 * Created by PhpStorm.
 * User: CMA15450
 * Date: 29/03/2017
 * Time: 13:00
 */

namespace AppBundle\Form\Type;

use Symfony\Component\Form\AbstractType;
use Symfony\Component\Form\FormBuilderInterface;
use Symfony\Component\Form\Extension\Core\Type\PasswordType;
use Symfony\Component\OptionsResolver\OptionsResolverInterface;

class ResettingFormType extends AbstractType
{
    const LABEL = "label";
    const DOMAIN = "translation_domain";
    const DOMAIN_VALUE = "OpenFrontBundle";

    public function buildForm(FormBuilderInterface $builder, array $options)
    {
        //$plainPassword = $builder->get('plainPassword');

        //$first = $plainPassword->get('first');
        //$second = $plainPassword->get('second');

        //$first->setAttribute('')

        //$options['translation_domain'] = 'OpenFrontBundle';

    }

    /*public function setDefaultOptions(OptionsResolverInterface $resolver)
    {
        $resolver->setDefaults(array(
            'translation_domain' => 'Zobi'
        ));

    }*/

    public function getParent()
    {
        return 'FOS\UserBundle\Form\Type\ResettingFormType';
    }

    /**
     * {@inheritdoc}
     */
    public function getBlockPrefix()
    {
        return 'front_user_resetpassword';
    }

    // For Symfony 2.x
    /**
     * {@inheritdoc}
     */
    public function getName()
    {
        return $this->getBlockPrefix();
    }

}
