<?php
/**
 * Created by PhpStorm.
 * User: LRO16285
 * Date: 29/03/2018
 * Time: 13:05
 */

namespace AppBundle\Form\Type;

use AppBundle\Entity\Contact;
use Symfony\Component\Form\AbstractType;
use Symfony\Component\Form\FormBuilderInterface;
use Symfony\Component\OptionsResolver\OptionsResolver;
use Symfony\Component\Form\Extension\Core\Type\TextType;
use Symfony\Component\Form\Extension\Core\Type\EmailType;

class ContactType extends AbstractType
{
  const LABEL = "label";

  public function buildForm(FormBuilderInterface $builder, array $options)
  {
    $builder
      ->add(
        'firstName',
        TextType::class,
        array(
          self::LABEL => 'contact.form.firstname'
        )
      )
      ->add(
        'lastName',
        TextType::class,
        array(
          self::LABEL => 'contact.form.lastname'
        )
      )
      ->add(
        'email',
        EmailType::class,
        array(
          self::LABEL => 'contact.form.email'
        )
      )
      ->add(
        'phone1',
        TextType::class,
        array(
          self::LABEL => 'contact.form.phone1'
        )
      )
      ->add(
        'phone2',
        TextType::class,
        array(
          self::LABEL => 'contact.form.phone2'
        )

      )
      ->add(
        'function',
        TextType::class,
        array(
          self::LABEL => 'contact.form.function'
        )
      )
      ;
  }

  public function configureOptions(OptionsResolver $resolver)
  {
    $resolver->setDefaults(array(
      'data_class' => Contact::class,
    ));
  }
}
