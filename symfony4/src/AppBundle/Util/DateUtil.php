<?php
/**
 * Created by PhpStorm.
 * User: PPH04712
 * Date: 12/06/2018
 * Time: 17:18
 */

namespace AppBundle\Util;


class DateUtil
{

    /**
     * @param \DateTime $date
     * @param $nbDays
     * @return \DateTime
     * @throws \Exception
     */
    public static function addDaysEndOfMonth(\DateTime $date, $nbDays){
        $date->add(new \DateInterval('P'.strval($nbDays).'D'));
        $date->modify('last day of this month');
        return $date;
    }

    /**
     * move given date to the next working day if the date is during the weekend
     * if the date is a Saturday or a Sunday then move the date to the next Monday
     *
     * @param \DateTimeImmutable $date
     * @return \DateTimeImmutable
     */
    public static function moveToNextWorkingDay(\DateTimeImmutable $date): \DateTimeImmutable
    {
        // adjust shipment date to be on the working days of the week
        $weekDay = date('w', strtotime($date->format('Y-m-d')));
        if($weekDay == 0 ) $date = $date->modify('+1 day');
        if($weekDay == 6 ) $date = $date->modify('+2 day');

        return $date;
    }
}
