<?php

namespace AppBundle\Command;

use AppBundle\Entity\User;
use AppBundle\Services\MailService;
use Doctrine\ORM\EntityManagerInterface;
use FOS\UserBundle\Model\UserManagerInterface;
use FOS\UserBundle\Util\TokenGeneratorInterface;
use Symfony\Component\Console\Attribute\AsCommand;
use Symfony\Component\Console\Command\Command;
use Symfony\Component\Console\Input\InputArgument;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Output\OutputInterface;
use Symfony\Component\Console\Style\SymfonyStyle;

#[AsCommand(
    name: 'app:test-password-reset-flow',
    description: 'Test the complete password reset flow including FOSUserBundle integration'
)]
class TestPasswordResetFlowCommand extends Command
{
    private MailService $mailService;
    private UserManagerInterface $userManager;
    private TokenGeneratorInterface $tokenGenerator;
    private EntityManagerInterface $entityManager;

    public function __construct(
        MailService $mailService,
        UserManagerInterface $userManager,
        TokenGeneratorInterface $tokenGenerator,
        EntityManagerInterface $entityManager
    ) {
        $this->mailService = $mailService;
        $this->userManager = $userManager;
        $this->tokenGenerator = $tokenGenerator;
        $this->entityManager = $entityManager;
        parent::__construct();
    }

    protected function configure(): void
    {
        $this->addArgument('email', InputArgument::OPTIONAL, 'Email address to test (will create test user if not provided)');
    }

    protected function execute(InputInterface $input, OutputInterface $output): int
    {
        $io = new SymfonyStyle($input, $output);
        
        $io->title('Password Reset Flow Test');
        
        $email = $input->getArgument('email');
        
        if (!$email) {
            $email = '<EMAIL>';
            $io->note('No email provided, using test email: ' . $email);
        }

        // Step 1: Find or create test user
        $io->section('Step 1: Finding/Creating Test User');
        
        $user = $this->userManager->findUserByEmail($email);
        
        if (!$user) {
            $io->info('User not found, creating test user...');
            $user = $this->userManager->createUser();
            $user->setEmail($email);
            $user->setUsername($email);
            $user->setFirstname('Test');
            $user->setLastname('User');
            $user->setPlainPassword('temporary123');
            $user->setEnabled(true);
            $user->setLocale('en');
            
            $this->userManager->updateUser($user);
            $io->success('Test user created: ' . $email);
        } else {
            $io->success('Found existing user: ' . $email);
        }

        // Step 2: Check user properties
        $io->section('Step 2: User Properties Check');
        
        $io->table(['Property', 'Value'], [
            ['Email', $user->getEmail()],
            ['Username', $user->getUsername()],
            ['Enabled', $user->isEnabled() ? 'Yes' : 'No'],
            ['Firstname', $user->getFirstname()],
            ['Lastname', $user->getLastname()],
            ['Locale', $user->getLocale()],
            ['Current Token', $user->getConfirmationToken() ?: 'None'],
            ['Password Requested At', $user->getPasswordRequestedAt() ? $user->getPasswordRequestedAt()->format('Y-m-d H:i:s') : 'Never'],
        ]);

        // Step 3: Generate token (simulating FOSUserBundle flow)
        $io->section('Step 3: Token Generation');
        
        if (null === $user->getConfirmationToken()) {
            $token = $this->tokenGenerator->generateToken();
            $user->setConfirmationToken($token);
            $io->success('Generated new token: ' . $token);
        } else {
            $io->info('User already has token: ' . $user->getConfirmationToken());
        }

        // Step 4: Test email sending (the critical part)
        $io->section('Step 4: Email Sending Test');
        
        try {
            $io->info('Calling MailService->sendResettingEmailMessage()...');
            $this->mailService->sendResettingEmailMessage($user);
            $io->success('Email sent successfully!');
        } catch (\Exception $e) {
            $io->error('Email sending failed: ' . $e->getMessage());
            $io->note('Exception class: ' . get_class($e));
            $io->note('Stack trace: ' . $e->getTraceAsString());
            return Command::FAILURE;
        }

        // Step 5: Update user (simulating FOSUserBundle flow)
        $io->section('Step 5: User Update');
        
        $user->setPasswordRequestedAt(new \DateTime());
        $this->userManager->updateUser($user);
        $io->success('User updated with password request timestamp');

        // Step 6: Verify email template exists
        $io->section('Step 6: Email Template Verification');
        
        try {
            $templateRepo = $this->entityManager->getRepository(\AppBundle\Entity\Node::class);
            $template = $templateRepo->findOneBy(['slug' => 'RESET_PASSWORD_TO_USER', 'type' => 'email']);
            
            if ($template) {
                $io->success('Email template found in database');
                $io->table(['Property', 'Value'], [
                    ['ID', $template->getId()],
                    ['Slug', $template->getSlug()],
                    ['Type', $template->getType()],
                    ['Status', $template->getStatus()],
                ]);
            } else {
                $io->warning('Email template not found in database');
            }
        } catch (\Exception $e) {
            $io->warning('Could not check email template: ' . $e->getMessage());
        }

        // Step 7: Check transactional email settings
        $io->section('Step 7: Transactional Email Settings');
        
        try {
            $transEmailRepo = $this->entityManager->getRepository(\AppBundle\Entity\TransactionalEmail::class);
            $transEmail = $transEmailRepo->findOneBy(['emailIdentifier' => 'RESET_PASSWORD_TO_USER']);
            
            if ($transEmail) {
                $io->success('Transactional email configuration found');
                $io->table(['Property', 'Value'], [
                    ['ID', $transEmail->getIdTransactionalEmail()],
                    ['Identifier', $transEmail->getEmailIdentifier()],
                    ['Active', $transEmail->isActive() ? 'Yes' : 'No'],
                ]);
                
                if (!$transEmail->isActive()) {
                    $io->error('CRITICAL: Transactional email is DISABLED!');
                    $io->note('This could be why emails are not being sent.');
                }
            } else {
                $io->warning('Transactional email configuration not found');
            }
        } catch (\Exception $e) {
            $io->warning('Could not check transactional email settings: ' . $e->getMessage());
        }

        // Step 8: Final summary
        $io->section('Test Summary');
        
        $io->success('Password reset flow test completed');
        $io->note('Check MailHog at http://localhost:8025 to see if email was received');
        $io->note('Reset URL would be: /resetting/reset/' . $user->getConfirmationToken());

        return Command::SUCCESS;
    }
}
