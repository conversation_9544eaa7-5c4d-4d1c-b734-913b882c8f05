<?php

namespace AppBundle\Command;

use AppBundle\Entity\User;
use AppBundle\Services\MailService;
use FOS\UserBundle\Util\TokenGeneratorInterface;
use Symfony\Component\Console\Attribute\AsCommand;
use Symfony\Component\Console\Command\Command;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Output\OutputInterface;
use Symfony\Component\Console\Style\SymfonyStyle;

#[AsCommand(
    name: 'app:test-email-direct',
    description: 'Test email sending directly without database dependencies'
)]
class TestEmailDirectCommand extends Command
{
    private MailService $mailService;
    private TokenGeneratorInterface $tokenGenerator;

    public function __construct(
        MailService $mailService,
        TokenGeneratorInterface $tokenGenerator
    ) {
        $this->mailService = $mailService;
        $this->tokenGenerator = $tokenGenerator;
        parent::__construct();
    }

    protected function execute(InputInterface $input, OutputInterface $output): int
    {
        $io = new SymfonyStyle($input, $output);
        
        $io->title('Direct Email Test - Password Reset');
        
        // Create a test user object (not persisted to database)
        $testUser = new User();
        $testUser->setEmail('<EMAIL>');
        $testUser->setFirstname('Test');
        $testUser->setLastname('User');
        $testUser->setLocale('en');
        $testUser->setEnabled(true);
        $testUser->setConfirmationToken($this->tokenGenerator->generateToken());

        $io->section('Test User Created');
        $io->table(['Property', 'Value'], [
            ['Email', $testUser->getEmail()],
            ['Firstname', $testUser->getFirstname()],
            ['Lastname', $testUser->getLastname()],
            ['Locale', $testUser->getLocale()],
            ['Enabled', $testUser->isEnabled() ? 'Yes' : 'No'],
            ['Token', $testUser->getConfirmationToken()],
        ]);

        $io->section('Testing Email Send');
        
        try {
            $io->info('Calling MailService->sendResettingEmailMessage()...');
            $this->mailService->sendResettingEmailMessage($testUser);
            $io->success('✅ Email sent successfully!');
            
            $io->note('Check MailHog at http://localhost:8025 to verify email delivery');
            $io->note('Reset URL would be: /resetting/reset/' . $testUser->getConfirmationToken());
            
            return Command::SUCCESS;
            
        } catch (\Exception $e) {
            $io->error('❌ Email sending failed!');
            $io->error('Error: ' . $e->getMessage());
            $io->note('Exception class: ' . get_class($e));
            
            // Show more details for debugging
            $io->section('Debug Information');
            $io->text('Stack trace:');
            $io->text($e->getTraceAsString());
            
            return Command::FAILURE;
        }
    }
}
