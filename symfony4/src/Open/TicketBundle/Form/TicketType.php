<?php

namespace Open\TicketBundle\Form;

use AppBundle\Entity\Company;
use AppBundle\Services\SecurityService;
use Doctrine\ORM\EntityRepository;
use EWZ\Bundle\RecaptchaBundle\Form\Type\EWZRecaptchaType;
use EWZ\Bundle\RecaptchaBundle\Validator\Constraints\IsTrue;
use Open\TicketBundle\Entity\Ticket;
use Symfony\Bridge\Doctrine\Form\Type\EntityType;
use Symfony\Component\Form\AbstractType;
use Symfony\Component\Form\Extension\Core\Type\CollectionType;
use Symfony\Component\Form\Extension\Core\Type\HiddenType;
use Symfony\Component\Form\Extension\Core\Type\TextType;
use Symfony\Component\Form\FormBuilderInterface;
use Symfony\Component\OptionsResolver\OptionsResolver;

class TicketType extends AbstractType
{

    const LABEL = 'label';
    const REQUIRED = "required";
    const TICKET_STATUS_PREFIX = 'ticket.status.';

    private SecurityService $security;

    public function __construct(SecurityService $security){
        $this->security = $security;
    }

    /**
     * @param FormBuilderInterface $builder
     * @param array                $options
     */
    public function buildForm(FormBuilderInterface $builder, array $options)
    {
        $builder
            ->add(
                'subject',
                TextType::class,
                [
                    self::LABEL => 'ticket.common.subject',
                ]
            )->add(
                'ticketNumber',
                HiddenType::class,
                [
                    self::LABEL => 'ticket.common.number',
                    'attr' => array ("readonly" => true)
                ]
            )->add(
                'messages',
                CollectionType::class,
                [
                    'entry_type' => TicketMessageType::class,
                ]);


            //recipient is only added, if the user is admin
            if ($this->security->isAdmin($this->security->getUser())){
                $builder->add(
                    'recipient',
                    EntityType::class,
                    [
                        self::LABEL => 'ticket.create.recipient',
                        'class' => Company::class,
                        'query_builder' => function (EntityRepository $er) {
                            return $er->createQueryBuilder('u')
                                ->orderBy('u.name', 'asc');
                        },
                        'choice_label' => 'name',
                    ]
                );
            }

            if ( $this->security->getUser() !== null && !is_string($this->security->getUser()) ) {
                $builder->add('merchant', HiddenType::class, ['mapped' => false]);
            }

            //anonymous user must add firstname, lastname, email and phone
            if ($this->security->getUser() === null || is_string($this->security->getUser())){

                $builder->add(
                    'anonymousEmail',
                    TextType::class,
                    [
                        self::LABEL => 'ticket.create.email'
                    ]
                )->add(
                    'anonymousFirstName',
                    TextType::class,
                    [
                        self::LABEL => 'ticket.create.firstname'
                    ]
                )->add(
                    'anonymousLastName',
                    TextType::class,
                    [
                        self::LABEL => 'ticket.create.lastname'
                    ]
                )->add(
                    'anonymousCompany',
                    TextType::class,
                    [
                        self::LABEL => 'ticket.create.company'
                    ]
                )->add(
                    'anonymousFunction',
                    TextType::class,
                    [
                        self::LABEL => 'ticket.create.function',
                        self::REQUIRED => false
                    ]
                )->add(
                    'anonymousPhone',
                    TextType::class,
                    [
                        self::LABEL => 'ticket.create.phone',
                        self::REQUIRED => false
                    ]
                );
            }

            if ($options['captcha_enabled']) {
                $builder->add(
                    'recaptcha',
                    EWZRecaptchaType::class,
                    [
                        'label' => false,
                        'mapped' => false,
                        'constraints' => [
                            new IsTrue(
                                ['groups' => ['Default', 'TicketAnonymous']]
                            )
                        ],
                    ]
                );
			}

    }


    public function configureOptions(OptionsResolver $resolver)
    {
        $resolver->setDefaults([
            'translation_domain' => 'AppBundle',
            'data_class' => Ticket::class,
			'captcha_enabled' => false,
			'is_response' => false
        ]);
    }



}
