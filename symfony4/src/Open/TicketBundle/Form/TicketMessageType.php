<?php

namespace Open\TicketBundle\Form;

use AppBundle\Services\SecurityService;
use Open\TicketBundle\Entity\TicketMessage;
use Open\TicketBundle\Model\TicketConstant;
use Sensio\Bundle\FrameworkExtraBundle\Configuration\Security;
use Symfony\Component\Form\AbstractType;
use Symfony\Component\Form\Extension\Core\Type\CheckboxType;
use Symfony\Component\Form\Extension\Core\Type\ChoiceType;
use Symfony\Component\Form\Extension\Core\Type\FileType;
use Symfony\Component\Form\Extension\Core\Type\SubmitType;
use Symfony\Component\Form\Extension\Core\Type\TextareaType;
use Symfony\Component\Form\FormBuilderInterface;
use Symfony\Component\OptionsResolver\OptionsResolver;
use Symfony\Component\Validator\Constraints\All;
use Symfony\Component\Validator\Constraints\File;
use Symfony\Component\Validator\Constraints\NotBlank;

class TicketMessageType extends AbstractType
{

    const LABEL = 'label';
    const TICKET_STATUS_PREFIX = 'ticket.status.';
    const REQUIRED = 'required';
    const PLACEHOLDER = 'placeholder';

    /**
     * @var SecurityService $security
     */
    private $security;

    /**
     * TicketMessageType constructor.
     * @param SecurityService $security
     */
    public function __construct(SecurityService $security){
        $this->security = $security;
    }

    /**
     * @param FormBuilderInterface $builder
     * @param array                $options
     */
    public function buildForm(FormBuilderInterface $builder, array $options)
    {
        $builder
            ->add(
                'content',
                TextareaType::class,
                array (
                    self::LABEL => $options['is_response'] ? false : 'ticket.common.message',
					'attr' => array (
						self::PLACEHOLDER => 'ticket.edit.message_placeholder',
					)
				)
            )
			->add(
                'resolved',
                CheckboxType::class,
                array (
                    self::LABEL => 'ticket.edit.close'
				)
            )
            ->add(
                'attachments',
                FileType::class,
                array (
                    //'mapped' => false,
                    self::LABEL => 'ticket.edit.attachment_button',
                    self::REQUIRED => false,
                    'multiple' => true,
                    'constraints' => new All(new File(['maxSize' => '5M'])),
                )
            )
            ->add(
                'save',
                SubmitType::class,
                array (
                    self::LABEL => 'contactMerchant.form.save',
                )
            )
        ;



    }

    public function configureOptions(OptionsResolver $resolver)
    {
        $resolver->setDefaults([
            'translation_domain' => 'AppBundle',
            'data_class' => TicketMessage::class,
			'is_response' => false,
			'validation_groups' => array('Default')
        ]);
    }

}
