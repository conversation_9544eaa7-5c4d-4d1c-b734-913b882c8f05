<?php

namespace Open\TicketBundle\Form;

use AppBundle\Entity\Company;
use AppBundle\Entity\User;
use AppBundle\Repository\UserRepository;
use AppBundle\Services\SecurityService;
use AppBundle\Validator\Constraints\ReCaptcha;
use Doctrine\ORM\EntityRepository;
use Open\TicketBundle\Entity\Ticket;
use Symfony\Bridge\Doctrine\Form\Type\EntityType;
use Symfony\Component\Form\AbstractType;
use Symfony\Component\Form\Extension\Core\Type\CollectionType;
use Symfony\Component\Form\Extension\Core\Type\HiddenType;
use Symfony\Component\Form\Extension\Core\Type\TextType;
use Symfony\Component\Form\FormBuilderInterface;
use Symfony\Component\OptionsResolver\OptionsResolver;
use Symfony\Component\Security\Core\Authorization\AuthorizationChecker;
use Symfony\Component\Validator\Constraints\NotBlank;

class TicketWaylfType extends AbstractType
{

    const LABEL = 'label';
    const REQUIRED = "required";
    const TICKET_STATUS_PREFIX = 'ticket.status.';


    /**
     * @var SecurityService $security
     */

    private $security;

    public function __construct($security){
        $this->security = $security;
    }

    /**
     * @param FormBuilderInterface $builder
     * @param array                $options
     */
    public function buildForm(FormBuilderInterface $builder, array $options)
    {
        $builder
           ->add(
                'ticketNumber',
                HiddenType::class,
                [
                    self::LABEL => 'ticket.common.number',
                    'attr' => array ("readonly" => true)
                ]
            )->add(
                'messages',
                CollectionType::class,
                [
                    'entry_type' => TicketMessageType::class,
                ]);

            //anonymous user must add firstname, lastname, email
            if ($this->security->getUser() === null || is_string($this->security->getUser())){

                $builder->add(
                    'anonymousEmail',
                    TextType::class,
                    [
                        self::LABEL => 'ticket.create.email'
                    ]
                )->add(
                    'anonymousFirstName',
                    TextType::class,
                    [
                        self::LABEL => 'ticket.create.firstname'
                    ]
                )->add(
                    'anonymousLastName',
                    TextType::class,
                    [
                        self::LABEL => 'ticket.create.lastname'
                    ]
                )->add(
                    'anonymousCompany',
                    TextType::class,
                    [
                        self::LABEL => 'ticket.create.company'
                    ]
                );
            }

            if ($options['captcha_enabled'] && $options['captcha_secret']) {
				$builder->add(
					'hiddenRecaptcha',
					TextType::class,
					array(
						'label' => false,
						'mapped' => false,
						'constraints' => array(
							new NotBlank(),
							new ReCaptcha(
								array('captcha_secret' => $options['captcha_secret'])
							)
						)
					)
				);
			}

    }


    public function configureOptions(OptionsResolver $resolver)
    {
        $resolver->setDefaults([
            'translation_domain' => 'AppBundle',
            'data_class' => Ticket::class,
			'captcha_enabled' => false,
			'captcha_secret' => null,
			'is_response' => false
        ]);
    }



}
