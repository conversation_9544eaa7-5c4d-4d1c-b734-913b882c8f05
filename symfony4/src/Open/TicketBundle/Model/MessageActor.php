<?php

namespace Open\TicketBundle\Model;

class MessageActor
{
    public const TYPE_MERCHANT = 'merchant';
    public const TYPE_APPLICATION = 'application';
    public const TYPE_UNDEFINED = 'undefined';

    private $id;
    private $type;

    public static function create(int $id, string $type = self::TYPE_UNDEFINED)
    {
        if (!in_array($type, [self::TYPE_APPLICATION, self::TYPE_MERCHANT, self::TYPE_UNDEFINED])) {
            return new \RuntimeException(sprintf('Invalid reveiver type'));
        }

        return (new self)
            ->setId($id)
            ->setType($type);
    }

    public static function createFromIzbergResourceUrl(string $resourceUri, int $id)
    {
        if (strpos($resourceUri, '/application/') !== false) {
            return self::create($id, self::TYPE_APPLICATION);
        }

        if (strpos($resourceUri, '/merchant/') !== false) {
            return self::create($id, self::TYPE_MERCHANT);
        }

        return self::create($id, self::TYPE_UNDEFINED);
    }

    public function getId()
    {
        return $this->id;
    }

    public function setId($id): self
    {
        $this->id = $id;

        return $this;
    }

    public function getType()
    {
        return $this->type;
    }

    public function setType($type): self
    {
        $this->type = $type;

        return $this;
    }

    public function isMerchantType(): bool
    {
        return ($this->type === self::TYPE_MERCHANT);
    }

    public function isApplicationType(): bool
    {
        return ($this->type === self::TYPE_APPLICATION);
    }
}
