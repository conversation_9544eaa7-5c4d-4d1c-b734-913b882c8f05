{% extends base %}

{% if front %}
    {% form_theme form 'Form/appli_layout.html.twig' %}
{% else %}
    {% form_theme form 'bootstrap_4_layout.html.twig' %}
{% endif %}

{% trans_default_domain "AppBundle" %}

{% block title %}
    {{ 'ticket.create.title' | trans}}
{% endblock %}

{% block body %}
    <div class="container-inner">
        <div class="card Form Ticket-Form">
            <div class="card-header">
                {{ 'ticket.create.title' | trans}}
            </div>
            {% if isAnonymous %}
                {{ form_start(form, {'attr': {'id':'form-ticket-edit', 'class': 'anonymous-ticket-form'}}) }}
            {% else %}
                {{ form_start(form, {'attr': {'id':'form-ticket-edit'}}) }}
            {% endif %}
            <div class="card-body">

                {% if isAnonymous %}
                    <div class="user-info">
                    {{ form_row(form.anonymousLastName) }}
                    {{ form_row(form.anonymousFirstName) }}
                    {{ form_row(form.anonymousCompany) }}
                    {{ form_row(form.anonymousFunction) }}
                    {{ form_row(form.anonymousEmail) }}
                    {{ form_row(form.anonymousPhone) }}
                    </div>
                {% endif %}

                {% if isAdmin %}
                    <div class="form-row">
                        <div class="col-md-12">
                            <div class="form-group">
                                {{ form_row(form.recipient) }}
                            </div>
                        </div>
                    </div>
                {% endif %}

                {% if not isAnonymous and not isAdmin %}
                <div class="ticket--is-for-admin pl-4 mb-4">
                    <div class="custom-control custom-radio">
                        <input type="radio" name="isadmin" value="1" id="isadmin1" checked class="custom-control-input">
                        <label class="custom-control-label pt-1" for="isadmin1">{{ 'ticket.create.foradmin' | trans}}</label>
                    </div>
                    <div class="custom-control custom-radio">
                        <input type="radio" name="isadmin" value="0" id="isadmin0" class="custom-control-input">
                        <label class="custom-control-label pt-1" for="isadmin0">{{ 'ticket.create.forvendor' | trans}}</label>
                    </div>
                </div>

                <div class="ticket--merchant mb-4 hidden">
                    <select title="" data-live-search="true" class="selectpicker form-control" name="merchants" id="merchants">
                        {% for merchant in merchants %}
                            <option value="{{ merchant.izbergId }}">
                                {{ merchant.izbMerchantName }}
                            </option>
                        {% endfor %}
                    </select>
                </div>
                {% endif %}

                <div class="ticket--subject">
                {{ form_row(form.subject) }}
                </div>

                <div class="ticket--message">
                {% for message in form.messages %}
                    {{ form_label(message.content) }}

                    <div class="form-row has-text">
                    {{ form_widget(message.content) }}
                        <small class="error">{{ form_errors(message.content) }}</small>
                    </div>


                    <div class="ticket--attachment js-attachments-container">
                        <div class="flex jc-sb ai-c">
                            {{ 'ticket.common.authorized_files_extensions_message' | trans }}
                            <div class="flex" style="margin-left:auto">
                                {{ form_widget(message.attachments) }}
                                <button type="button" class="Button Button--upload js-file-reset-button button--reset">
                                    <svg class="Icon"><use xlink:href="#icon-trash"/></svg>
                                </button>
                            </div>

                        </div>

                        <ul class="attachment-files js-attachment-files">
                            <li class="file--warning">{{ 'ticket.common.nofiles'|trans }}</li>
                        </ul>


                    </div>

                    {%  do message.resolved.setRendered %}
                {% endfor %}
                </div>


                {% if isAnonymous %}
                    {{ form_row(form.recaptcha) }}
                {% endif %}


                <div class="Form-group Buttons-group">
                    <button id="js-submit-button" type="submit" class="btn btn-primary">{{ 'ticket.common.submit'|trans }}</button>
                </div>
                {{ form_row(form._token) }}
                {{ form_row(form.ticketNumber) }}
                {% if not isAnonymous and not isAdmin %}
                    {{ form_row(form.merchant) }}
                {% endif %}
                {{ form_end(form, {'render_rest': false}) }}

            </div>
        </div>

    </div>

   {% if form is defined %}
   {% include '@OpenTicket/shared/_javascript.html.twig' with {form: form} %}
   {% endif %}

{% endblock %}


{% block javascripts %}

    <script type="text/javascript">
        'use strict';

        document.addEventListener('DOMContentLoaded', function() {

            $('.js-file-reset-button').hide();

            if ($('#ticket_merchant').attr('value') !== '' && $('#ticket_merchant').attr('value') !== undefined) {
                $('#isadmin0').attr('checked','checked');
                $('.ticket--merchant').removeClass('hidden');
                $('#merchants').children("option").each(function(k,v) {
                    if($(v).attr('value') === $('#ticket_merchant').attr('value')) {
                        $(v).attr('selected','selected');
                    }
                });
            }

            $('input[type=radio][name=isadmin]').change(function() {
                if (this.value == '0') {
                    $('.ticket--merchant').removeClass('hidden');
                }else {
                    $('.ticket--merchant').addClass('hidden');
                    $('#ticket_merchant').attr('value', '');
                }
            });

            $('#js-submit-button').click(function (e) {
                e.preventDefault();
                var is_admin = $('input[type=radio][name=isadmin]:checked').val()
                $('#ticket_isForAdmin').attr('value',is_admin);
                if (is_admin === '0') {
                    var vendor = $('#merchants').children("option:selected").val();
                    $('#ticket_merchant').attr('value', vendor)
                }
                $("#form-ticket-edit").submit();
            });

            $('[type=file]').hide();
            $('.js-button-upload').on('click', () => {
                $('[type=file]').click();
            });

            $.widget("custom.combobox", {
                _create: function () {
                    this.wrapper = $("<span>")
                        .addClass("custom-combobox")
                        .insertAfter(this.element);

                    this.element.hide();
                    this._createAutocomplete();
                    this._createShowAllButton();
                },

                _createAutocomplete: function () {
                    let selected = this.element.children(":selected"),
                        value = selected.val() ? selected.text() : "";

                    this.input = $("<input>")
                        .appendTo(this.wrapper)
                        .val(value)
                        .attr("title", "")
                        .addClass("custom-combobox-input ui-widget ui-widget-content ui-state-default ui-corner-left")
                        .autocomplete({
                            delay: 0,
                            minLength: 0,
                            source: $.proxy(this, "_source")
                        })
                        .tooltip({
                            classes: {
                                "ui-tooltip": "ui-state-highlight"
                            }
                        });

                    this._on(this.input, {
                        autocompleteselect: function (event, ui) {
                            ui.item.option.selected = true;
                            $('#ticket_recipient').change(); // Trigger real select change
                            this.input.removeClass('company--invalid');

                            this._trigger("select", event, {
                                item: ui.item.option
                            });
                        },

                        autocompletechange: "_removeIfInvalid"
                    });
                },

                _createShowAllButton: function () {
                    let input = this.input,
                        wasOpen = false;

                    let svg = document.createElementNS("http://www.w3.org/2000/svg", "svg");

                    svg.classList.add('Icon');

                    //let use = document.createElementNS("http://www.w3.org/1999/xlink", "use");

                    //use.setAttributeNS("http://www.w3.org/1999/xlink", 'xlink:href', '#eye');

                    //svg.appendChild(use);

                    //console.log(svg);

                    let $a = $("<a>")
                        .attr("tabIndex", -1)
                        .attr("title", "{{ 'user.form.company_all'|trans({}, 'AppBundle') }}")
                        //.attr( "height", "" )
                        .addClass('custom-combobox-showall')
                        .tooltip()
                        .appendTo(this.wrapper)
                        //.append(svg)
                        .on("mousedown", function () {
                            wasOpen = input.autocomplete("widget").is(":visible");
                        })
                        .on("click", function () {
                            input.trigger("focus");

                            // Close if already visible
                            if (wasOpen) {
                                return;
                            }

                            // Pass empty string as value to search for, displaying all results
                            input.autocomplete("search", "");
                        });
                    $a.append('<img width="24" height="24" class="custom-combobox-toggle" src="/images/search.svg" />');

                },

                _source: function (request, response) {
                    var matcher = new RegExp($.ui.autocomplete.escapeRegex(request.term), "i");
                    response(this.element.children("option").map(function () {
                        var text = $(this).text();
                        if (this.value && ( !request.term || matcher.test(text) ))
                            return {
                                label: text,
                                value: text,
                                option: this
                            };
                    }));
                },

                _removeIfInvalid: function (event, ui) {

                    // Selected an item, nothing to do
                    if (ui.item) {
                        return;
                    }

                    // Search for a match (case-insensitive)
                    var value = this.input.val(),
                        valueLowerCase = value.toLowerCase(),
                        valid = false;
                    this.element.children("option").each(function () {
                        if ($(this).text().toLowerCase() === valueLowerCase) {
                            this.selected = valid = true;
                            return false;
                        }
                    });

                    // Found a match, nothing to do
                    if (valid) {
                        return;
                    }

                    this.input.addClass('company--invalid');
                    // Remove invalid value
                    /*this.input
                     .val( "" )
                     .attr( "title", value + " didn't match any item" )
                     .tooltip( "open" );*/

                    this.element.val("");
                    $('#ticket_recipient').change();

                    this._delay(function () {
                        this.input.tooltip("close").attr("title", "");
                    }, 2500);
                    this.input.autocomplete("instance").term = "";
                },

                _destroy: function () {
                    this.wrapper.remove();
                    this.element.show();
                }
            });

            $('#ticket_recipient').combobox();
        });
    </script>
{% endblock %}
