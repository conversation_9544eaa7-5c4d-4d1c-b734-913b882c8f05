{% trans_default_domain "AppBundle" %}

<script>
  'use strict';


  document.addEventListener('DOMContentLoaded', function () {

    var $ticketResolvedCB = $('#js-ticket-resolved').find('input[type=checkbox]');
    var $resetButton = $('.js-file-reset-button');

    // #21197: Reset files hide reset button
    var _updateList = function () {
      var $filesDiv = $(this).parents('.js-attachments-container').find('.js-attachment-files');

      if (this.files.length > 0) {
        $filesDiv.html('');
        $resetButton.show();
        for (var i = 0 ; i < this.files.length ; i++) {
          var $li = $('<li>' + this.files[i].name  + '</li>');

          $filesDiv.append($li);
        }
      } else {
        $filesDiv.html('<li class="file--warning">{{ 'ticket.common.nofiles'|trans }}</li>');
        $resetButton.hide();
      }
    };

      {% if captcha_enabled and form.hiddenRecaptcha is defined %}
    window.UI.ReCaptcha.init('ticket_hiddenRecaptcha', 'js-submit-button');
      {% endif %}


      {{ form_jquery_validation(form) }}

    if ($ticketResolvedCB.length) {
      var $textarea = $('#ticket_message_content');

      $textarea.rules('remove');
      $textarea.rules('add', {
        required: function() {
            return $ticketResolvedCB.get(0).checked === false;
        }
      });
    }

    $('.js-button-upload').on('click', function () {
      var $label = $(this).parent().find('label');
      if(!$label) {
        $label = $('#label_upload');
      }
      $label.click()
    });

    $('input[type=file]').on('change', _updateList);

    // #21197: Reset files hide reset button
    $('.js-file-reset-button').on('click', function () {
      var $button = $(this);
      var $parent = $button.parent();
      var $input = $parent.find('input[type=file]');
      var $filesDiv = $(this).parents('.js-attachments-container').find('.js-attachment-files');


      $input.wrap('<form>').closest('form').get(0).reset();
      $input.unwrap();

      $(this).hide();

      $filesDiv.html('<li class="file--warning">{{ 'ticket.common.nofiles'|trans }}</li>');

    });

    var $ticketCloseButton = $('#js-ticket-close');

    var $ticketResponseButton = $("#js-ticket-response-show");

    // Edit form only
    $ticketResponseButton.on('click', function () {
      $('#js-response-form-container').fadeIn();
      $resetButton.hide();
      $ticketCloseButton.hide();
    });

    $ticketCloseButton.on('click', function () {
      $ticketResolvedCB.attr('checked', true);
      $('#js-message-form').submit();
    });


      // if error from back then show it
      if ($('small.error').find('.Message--error').length) {
          $ticketResponseButton.click();
      }

  });
</script>
