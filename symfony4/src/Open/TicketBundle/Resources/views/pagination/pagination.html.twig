{% if pageCount > 1 %}
    <nav>
        <ul class="pagination{{ (align is not defined) ? '' : align=='center' ? ' justify-content-center' : (align=='right' ? ' justify-content-end' : '') }}">
            <div class="block-previous">
                {% if previous is defined %}
                    <li class="previous-item">
                        <a class="desktop-only" rel="prev" href="{{ path(route, query|merge({(pageParameterName): previous})) }}"><i class="arrow left"></i>{{ 'ticket.list.knp_previous'|trans({}, 'AppBundle') }}</a>
                        <a class="mobile-only" rel="prev" href="{{ path(route, query|merge({(pageParameterName): previous})) }}"><i class="arrow left"></i></a>
                    </li>
                {% else %}
                    <li class="disabled">
                        <a class="mobile-only disabled" rel="prev"><i class="arrow left"></i></a>
                    </li>
                {% endif %}
            </div>
            <div class="pagination-mobile-view mobile-only">
                <div class="page-numbers active">{{ current }}</div>
                sur {{ pageCount }}
            </div>
            <div class="block-number desktop-only">
                {% if startPage > 1 %}
                    <li class="">
                        <a class="page-numbers" href="{{ path(route, query|merge({(pageParameterName): 1})) }}">1</a>
                    </li>
                    {% if startPage == 3 %}
                        <li class="">
                            <a class="page-numbers" href="{{ path(route, query|merge({(pageParameterName): 2})) }}">2</a>
                        </li>
                    {% elseif startPage != 2 %}
                        <li class="disabled">
                            <span class="page-numbers">&hellip;</span>
                        </li>
                    {% endif %}
                {% endif %}

                {% for page in pagesInRange %}
                    {% if page != current %}
                        <li class="">
                            <a class="page-numbers" href="{{ path(route, query|merge({(pageParameterName): page})) }}">{{ page }}</a>
                        </li>
                    {% else %}
                        <li class="active">
                            <span class="page-numbers">{{ page }}</span>
                        </li>
                    {% endif %}

                {% endfor %}

                {% if pageCount > endPage %}
                    {% if pageCount > (endPage + 1) %}
                        {% if pageCount > (endPage + 2) %}
                            <li class="disabled">
                                <span class="page-numbers">&hellip;</span>
                            </li>
                        {% else %}
                            <li class="">
                                <a class="page-numbers" href="{{ path(route, query|merge({(pageParameterName): (pageCount - 1)})) }}">{{ pageCount -1 }}</a>
                            </li>
                        {% endif %}
                    {% endif %}
                    <li class="">
                        <a class="page-numbers" href="{{ path(route, query|merge({(pageParameterName): pageCount})) }}">{{ pageCount }}</a>
                    </li>
                {% endif %}
            </div>

            <div class="block-next">
                {% if next is defined %}
                    <li class="next-item">
                        <a class="desktop-only" rel="next" href="{{ path(route, query|merge({(pageParameterName): next})) }}"> {{ 'ticket.list.knp_next'|trans({}, 'AppBundle') }}<i class="arrow right"></i></a>
                        <a class="mobile-only" rel="next" href="{{ path(route, query|merge({(pageParameterName): next})) }}"><i class="arrow right"></i></a>
                    </li>
                {% else %}
                    <li  class="disabled">
                        <a class="mobile-only disabled" rel="next"><i class="arrow right"></i></a>
                    </li>
                {% endif %}
            </div>
        </ul>
    </nav>
{% endif %}
