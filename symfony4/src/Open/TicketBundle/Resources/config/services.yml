services:
    # default configuration for services in *this* file
    _defaults:
        autowire: true      # Automatically injects dependencies in your services.
        autoconfigure: true # Automatically registers your services as commands, event subscribers, etc.

    Open\TicketBundle\FilterQueryBuilder\TicketQueryBuilder:
        class: Open\TicketBundle\FilterQueryBuilder\TicketQueryBuilder

    Open\TicketBundle\Services\TicketService:
        class: Open\TicketBundle\Services\TicketService
        arguments:
            - "@doctrine.orm.entity_manager"
            - "@knp_paginator"
            - '@AppBundle\Services\SecurityService'
            - '@Open\TicketBundle\FilterQueryBuilder\TicketQueryBuilder'
            - '@Open\IzbergBundle\Api\MessageApi'

    ticket.type:
        class: Open\TicketBundle\Form\TicketType
        arguments: [ '@AppBundle\Services\SecurityService' ]
        tags:
            - { name: form.type }

    ticket.waylf.type:
        class: Open\TicketBundle\Form\TicketWaylfType
        arguments: [ '@AppBundle\Services\SecurityService' ]
        tags:
            - { name: form.type }

    ticket.message.type:
        class: Open\TicketBundle\Form\TicketMessageType
        arguments: [ '@AppBundle\Services\SecurityService' ]
        tags:
            - { name: form.type }
