<?php
/**
 * Created by PhpStorm.
 * User: PPH04712
 * Date: 24/04/2018
 * Time: 14:09
 */

namespace Open\FrontBundle\EventSubscriber;


use AppBundle\Services\OfferService;
use Open\BackBundle\Events\UpdateSettingsEvent;
use Open\IzbergBundle\Service\RedisService;
use Symfony\Component\EventDispatcher\EventSubscriberInterface;

class OfferSubscriber implements EventSubscriberInterface
{

    /**
     * @var RedisService $cache the cache of the application
     */
    private $cache;

    /**
     * OfferSubscriber constructor.
     * @param RedisService $cache the cache of the application
     */
    public function __construct(RedisService $cache){
        $this->cache = $cache;
    }

    public static function getSubscribedEvents()
    {
        // Catch login actions to trigger logs actions
        return array(
            UpdateSettingsEvent::EVENT_NAME => 'onAdminUpdateSettings'
        );
    }

    /**
     * actions to perform when the settings are updated on the administration
     * @param UpdateSettingsEvent $event
     */
    public function onAdminUpdateSettings (UpdateSettingsEvent $event){
        /*
         * If list of best offers is modify, remove the cache, to rebuild
         * it with the new items selected by the administrator
         */
       if ($event->getDomain() === 'offers' &&
       $event->getGroup() === "popular"){
           $this->cache->removeItem(OfferService::CACHE_BEST_OFFERS);
       }
    }
}
