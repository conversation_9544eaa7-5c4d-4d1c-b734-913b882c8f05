<?php
/**
 * Created by PhpStorm.
 * User: PPH04712
 * Date: 24/04/2018
 * Time: 09:10
 */

namespace Open\FrontBundle\Controller;


use AppBundle\Controller\MkoController;
use Open\IzbergBundle\Service\CategoryService;
use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\DependencyInjection\Container;
use Symfony\Component\HttpFoundation\Request;

class CategoriesNavBarController extends AbstractController
{
    /**
     * display the categories navbar
     *
     * @param Request $request
     * @param CategoryService $categoryService
     * @return \Symfony\Component\HttpFoundation\Response|null
     */
    public function getAction(
        array $ignoredCategories,
        Request $request,
        CategoryService $categoryService
    ){
        $categories = $categoryService->getCachedClassifiedCategories();

        return $this->render('@OpenFront/menu/header_navbar.html.twig', array(
            "categories" => $categories,
            "ignored_categories" => $ignoredCategories,
            "from" => $request->query->get("from"),
            "locale" => $request->getLocale()
        ));
    }

    public function getMobileAction(Request $request, CategoryService $categoryService) {
        $categories = $categoryService->getCachedClassifiedCategories();
        return $this->render('@OpenFront/menu/dropdown-category-mobile.html.twig', array(
            "categories" => $categories,
            "from" => $request->query->get("from")
        ));
    }

}
