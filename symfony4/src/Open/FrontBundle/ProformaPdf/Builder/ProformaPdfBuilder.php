<?php

declare(strict_types=1);

namespace Open\FrontBundle\ProformaPdf\Builder;

use AppBundle\Entity\Company;
use AppBundle\Model\Cart\Cart as CartModel;
use AppBundle\Model\DetailedOffer;
use AppBundle\Services\OfferService;
use Open\FrontBundle\Exception\OfferNotFoundException;
use Open\FrontBundle\ProformaPdf\MerchantData;
use Open\IzbergBundle\Api\MerchantApi;

final class ProformaPdfBuilder implements ProformaPdfBuilderInterface
{
    public function __construct(
        private readonly MerchantApi $merchantApi,
        private readonly OfferService $offerService,
    ) {
    }

    public function create(CartModel $cart, Company $company): \ArrayObject
    {
        $pdfData = new \ArrayObject;
        foreach ($cart->getMerchants() as $cartMerchant) {
            $pdfData[$cartMerchant->getId()]['taxes'] = $cartMerchant->getSubTotalVat();
            $pdfData[$cartMerchant->getId()]['subtotal'] = $cartMerchant->getTotal();
        }
        foreach ($cart->getItems() as $item) {
            $offerRef = $item->getOfferId();

            $detailedOffer = $this->findDetailedOffer(offerId: $offerRef, company: $company);

            $offer = $detailedOffer->getOffer();
            $merchantId = $offer->getMerchant()->getId();
            $merchant = $this->merchantApi->getMerchant(id: $merchantId);
            $customAttr = $this->merchantApi->getMerchantAllCustomAttributes($merchantId);
            $merchantCompany = $this->merchantApi->getMerchantCompany($merchantId);

            $merchantIzbergId = $merchant->id;

            $pdfData[$merchantIzbergId]['currency'] = $cart->getCurrency();
            $pdfData[$merchantIzbergId]['offers'][$offer->getProductId()]['detailedOffer'] = $detailedOffer;
            $pdfData[$merchantIzbergId]['offers'][$offer->getProductId()]['cartItem'] = $item;
            if (!isset($pdfData[$merchantIzbergId]['totalVatExcl'])) {
                $pdfData[$merchantIzbergId]['totalVatExcl'] = 0.0;
            }
            $pdfData[$merchantIzbergId]['totalVatExcl'] += $item->getTotalItem();
            $pdfData[$merchantIzbergId]['merchant'] = new MerchantData(id: $merchantIzbergId, name: $merchant->name);
            $pdfData[$merchantIzbergId]['customAttributes'][] = $customAttr;
            $pdfData[$merchantIzbergId]['merchantCompany'][] = $merchantCompany;
            $pdfData[$merchantIzbergId]['cartTaxes'] = $cart->getSubTotalVat();
            $pdfData[$merchantIzbergId]['cartTotal'] = $cart->getTotal();
        }

        return $pdfData;
    }

    private function findDetailedOffer(int $offerId, Company $company): DetailedOffer
    {
        /** @var DetailedOffer|null $detailedOffer */
        $detailedOffer = $this->offerService->findDetailedOfferById(offerId: $offerId, company: $company);

        if (!$detailedOffer instanceof DetailedOffer){
            throw new OfferNotFoundException(sprintf('No offer with id %s was found', $offerId));
        }

        return $detailedOffer;
    }
}
