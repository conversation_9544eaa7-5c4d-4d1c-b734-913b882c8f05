(function ($) {
    'use strict';

    let onClickTab = function(occurence) {
        let alreadySelected = $('#Tab-Title-'+occurence).hasClass('active') ? true : false;
        let isMobile = occurence.toString().indexOf('mobile') == -1 ? false : true;

        $('.tab-h4').removeClass('active');
        $('.tab-content').removeClass('show');

        if (!alreadySelected || !isMobile) {
            $('#Tab-Title-'+occurence).addClass('active');
            $('#Tab-Content-'+occurence).addClass('show');
        }

        if(!alreadySelected && isMobile) {
            $('html, body').animate({
                scrollTop: ($('#Tab-Content-' + occurence).offset().top - ($('#js-header').height()*2))
            }, 0);
        }

    };

    module.exports = {
        onClickTab: onClickTab
    };


})(jQuery);
