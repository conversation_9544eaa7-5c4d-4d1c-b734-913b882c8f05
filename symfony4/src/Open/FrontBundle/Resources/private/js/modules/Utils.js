(function () {
    'use strict';

    let $b = $(window);

    /**
     * Loop through a list of Elements
     * @param array
     * @param callback
     * @param scope
     */
    let forEachElements = function (array, callback, scope) {
        for (var i = 0; i < array.length; i++) {
            callback.call(scope, i, array[i]); // passes back stuff we need
        }
    };

    /**
     * return value rounded to two decimals [+0.00001 fixes a floating point issue]
     * @see http://docs.oracle.com/cd/E19957-01/806-3568/ncg_goldberg.html
     * @param value
     * @returns {string}
     */
    let quickRound = function (value) {
        let rounded = Math.round((value + 0.00001) * 100) / 100;

        return rounded.toFixed(2);
    };

    let insertAfter = function(newNode, referenceNode) {
        referenceNode.parentNode.insertBefore(newNode, referenceNode.nextSibling);
    };


    /**
     * Extend An object with other objects
     * @see http://gomakethings.com/vanilla-javascript-version-of-jquery-extend/
     * @param deep {Boolean} deep copy
     * @param obj1..n {Object} object to be merged
     */
    let extend = function() {
        // Variables
        let extended = {};
        let deep = false;
        let i = 0;
        let length = arguments.length;

        // Check if a deep merge
        if ( Object.prototype.toString.call( arguments[0] ) === '[object Boolean]' ) {
            deep = arguments[0];
            i++;
        }

        // Merge the object into the extended object
        var merge = function (obj) {
            for ( var prop in obj ) {
                if ( Object.prototype.hasOwnProperty.call( obj, prop ) ) {
                    // If deep merge and property is an object, merge properties
                    if ( deep && Object.prototype.toString.call(obj[prop]) === '[object Object]' ) {
                        extended[prop] = extend( true, extended[prop], obj[prop] );
                    } else {
                        extended[prop] = obj[prop];
                    }
                }
            }
        };

        // Loop through each object and conduct a merge
        for ( ; i < length; i++ ) {
            var obj = arguments[i];
            merge(obj);
        }

        return extended;
    };

    /**
     * Method to generate a guid
     * @return {string} Returns the guid
     */
    let generateGuid = function() {

        var s4 = function () {
            return (((1 + Math.random()) * 0x10000) | 0).toString(16).substring(1);
        };

        return (s4() + s4() + '-' + s4() + '-' + s4() + '-' + s4() + '-' + s4() + s4() + s4());
    };

    /**
     * Get URL parameters
     * @returns {Object}
     */
    let getUrlParams = function() {

        let vars = {};

        window.location.href.replace(/[?&]+([^=&]+)=([^&]*)/gi, function(m,key,value) {
            vars[key] = value;
        });

        return vars;
    };

    let getScreenOrientation = function () {
      let w = $b.width();
      let h = $b.height();

        if (w > h) {
            return 'landscape';
        } else {
            return 'portrait';
        }
    };

    let filterFloat = function (value) {

        if (/^(\-|\+)?([0-9]+(\.[0-9]+)?|Infinity)$/
                .test(value))
            return Number(value);
        return NaN;
    };

    let filterPositiveFloat = function (value) {

        if (/^(\+)?([0-9]+(\.[0-9]+)?|Infinity)$/
                .test(value))
            return Number(value);
        return NaN;
    };

    let round = function (value, decimals) {
        return Number(Math.round(value+'e'+decimals)+'e-'+decimals);
    };

    let _initInput = function(index, element) {
        if(element.value !== '') {
            element.classList.add('has-text');
        }
    };

    let initInputs = function () {
      $('form input[type=type]').each(_initInput);
      $('form input[type=email]').each(_initInput);
      $('form input[type=password]').each(_initInput);
    };


    module.exports = {
        forEachElements : forEachElements,
        quickRound : quickRound,
        insertAfter : insertAfter,
        extend: extend,
        generateGuid : generateGuid,
        getUrlParams : getUrlParams,
        getScreenOrientation : getScreenOrientation,
        filterFloat: filterFloat,
        filterPositiveFloat: filterPositiveFloat,
        round : round,
        initInputs : initInputs
    };
})();
