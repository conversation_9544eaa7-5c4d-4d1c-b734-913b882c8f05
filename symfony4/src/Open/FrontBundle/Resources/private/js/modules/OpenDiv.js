(function ($) {
    'use strict';

    let removeContactAction = function (options) {
        let $wrapper = $(options.removeId);
        $wrapper.on('click', '.contact_remove', function() {
            $(this).closest("li").remove();
        });
    };

    let createCheckAction = function (options) {
        let divId = $(options.divId);
        let checkEl = $(options.checkId);
        let inputToNotValidate = divId.find(':input');

        if(!checkEl.is(':checked')) {
            divId.hide();
            inputToNotValidate.hide();
            inputToNotValidate.prop('disabled', true);

        }

        checkEl.on('change',function () {
            if ($(this).is(':checked')) {
                divId.toggle(true);
                inputToNotValidate.show();
                inputToNotValidate.prop('disabled', false);

            } else {
                divId.toggle(false);
                inputToNotValidate.hide();
                inputToNotValidate.prop('disabled', true);
            }
        });

    };

    module.exports = {
        createCheckAction: createCheckAction,
        removeContactAction: removeContactAction
    };



})(jQuery);
