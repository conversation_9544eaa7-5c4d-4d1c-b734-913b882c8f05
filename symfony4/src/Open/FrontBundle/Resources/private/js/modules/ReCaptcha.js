(function ($) {
  'use strict';


  let _init = function (captchaId, buttonId) {
    let $recaptcha = $('#' + captchaId);

    $recaptcha.css({
      opacity : 0,
      position: 'absolute'
    });

    $recaptcha.attr('type', 'text');

    // Update captcha field with current value when clicking on the submit button
    $('#' + buttonId).on('click', function() {

      if (grecaptcha.getResponse() !== '') {
        $recaptcha.val(grecaptcha.getResponse());
      } else {
        $recaptcha.val('');
      }

    });
  };


  module.exports = {
    init: _init
  };



})(jQuery);
