(function ($) {
    'use strict';

    let hideTo;
    let initDone = false;
    let lastIdx = -1;

    let _keyPressed = function ($wrapper, ev) {
        const key = ev.key.toString().toUpperCase();
        if (!$wrapper.is(":visible")) {
            $(document).off('keypress', this);
            return;
        }

        let nbMatches = 0;
        $wrapper.find('.select-wrapper__box__options').filter(function (idx, el){
            let isMatch = el.innerHTML.indexOf(key) === 0;
            if (isMatch) {
                nbMatches++;
            }
            return isMatch;
        }).each(function (idx, el) {
            $wrapper.animate(
                {
                    scrollTop: $wrapper.scrollTop() + $(el).offset().top - 600
                },
                {
                    duration: 'medium',
                    easing: 'swing'
                }
            );
            return false;
        });

    };


    let _onSelectClick = function ($selectWrapper) {
        const $optionsWrapper = $selectWrapper.find('.select-wrapper__box');

        if ($optionsWrapper.is(":visible")) {
            _hideBox($optionsWrapper);
        } else {
            $optionsWrapper.fadeIn('fast');
            $(document).on('keypress', _keyPressed.bind(null, $optionsWrapper));
        }
    };

    let _onDocumentClicked = function(ev) {
        let $target = $(ev.target);

        // Close all opened select when clicking anywhere on the page except a select itself
        if ($target.parents('.js-select-wrapper').length <= 0) {
            $('.js-select-wrapper').each(function (idx, el) {
                let $el = $(el);
                if ($el.find('.js-select-wrapper-box').is(":visible")) {
                    $el.find('.js-select-placeholder').click();
                }
            });
        }

        $(document).off('keypress', _keyPressed);
    };

    let _hideBox = function($optionsWrapper) {
        lastIdx = -1;
        $optionsWrapper.get(0).scrollTop = 0;
        $optionsWrapper.fadeOut('fast');
        $(document).off('keypress', _keyPressed);
    };

    let _onOptionChange = function(selector, $span){
        $span.html($(selector).find(":selected").html());
    };

    let _onOptionClicked = function ($selectWrapper, index, value, option) {
        _onSelectClick($selectWrapper);

        $selectWrapper.find('.select-wrapper__placeholder').html(value);

        const $select = $selectWrapper.find('select');

        option.siblings('.select-wrapper__box__options').removeClass('selected');
        option.addClass('selected');

        $select.find('option').each(function(i, option) {
            if($(option).val() === index){
                $select.get(0).selectedIndex = i;
                $select.change();
            }
        });

        $(document).off('keypress', _keyPressed);
    };

    let _init = function (selector) {

        if (typeof selector === 'undefined') {
            selector = '.js-select-wrapper';
        }
        $(selector).each(function() {
            const $this = $(this);
            const $select = $this.find('select');
            if($select.is(':disabled')){
                $this.addClass('disabled');
            }

            const $span = $('<div>');
            $span.addClass('select-wrapper__placeholder');
            $span.addClass('js-select-placeholder');
            if($this.children('.js-select-placeholder').length === 0) {
                $span.insertBefore($select);
            }

            const $optionsWrapper = $('<div>');
            $optionsWrapper.addClass('select-wrapper__box');
            $optionsWrapper.addClass('js-select-wrapper-box');
            $optionsWrapper.css('z-index', 99999);
            $optionsWrapper.insertBefore($select);

            $optionsWrapper.mouseenter(function () {
                clearTimeout(hideTo);
            });

            $optionsWrapper.mouseleave(function () {
                hideTo = setTimeout(_hideBox.bind(null,$optionsWrapper), 800);
            });

            // sort only if requested
            if ($select.data('sort') === true) {
                let options = $select.get(0).options;

                let optionsArray = [];
                for (let i = 0; i < options.length; i++) {
                    optionsArray.push(options[i]);
                }

                optionsArray.sort(function(a, b) {
                    return $(a).text().toUpperCase().localeCompare($(b).text().toUpperCase());

                });

                for (let i = 0; i <= options.length; i++) {
                    options[i] = optionsArray[i];
                }
            }

            $select.on('change', _onOptionChange.bind(null, $select, $span));

            let placeholder = true;
            $select.find('optgroup, option').each(function(i, option) {
                const $option = $(option);
                const $disabled = $option.attr('disabled');

                if (typeof $disabled === typeof undefined || $disabled === false) {
                    if ($option.get(0).nodeName === "OPTGROUP") {
                        const $val = $('<span>');
                        $val.addClass('select-wrapper__box__optionsGroup');
                        $val.html($option.get(0).label);
                        $optionsWrapper.append($val);
                    } else {
                        if ($option.data('placeholder') === true && placeholder === true) {
                            placeholder = $option.html();
                        }

                        if (!!$option.data('placeholder') === false) {
                            const $val = $('<span>');
                            $val.addClass('select-wrapper__box__options');
                            $val.attr('data-select-index', i);
                            $val.html($option.html());

                            if ($option.attr('selected') === 'selected') {
                                $span.html($option.html());
                                placeholder = false;
                            }

                            $val.on('click', _onOptionClicked.bind(null, $this, $option.val(), $option.html(), $val));

                            if ($option.attr('selected') === 'selected') {
                                $val.addClass('selected');
                            }

                            $val.attr('data-country-code', $option.data('country-code'));

                            $optionsWrapper.append($val);
                        }

                        if (placeholder) {
                            $span.html(placeholder);
                        }
                    }
                } else {
                    placeholder = $option.html();
                }
            });

            if(!$select.is(":disabled")) {
                $span.on('click', _onSelectClick.bind(null, $this));
            }
        });

    };

    if (!initDone) {
        // close opened selects when clicking on the other elemens of the page
        $(document).on('click', _onDocumentClicked);
    }

    initDone = true;

    module.exports = {
        init: _init
    };



})(jQuery);
