(function($) {

    let hideMessage = function(message) {
        message.remove();
    }

    let setFadeOutMessages = function() {
        let success = $('.Message--success');

        for(let i = 0; i < success.length; i++) {
            setTimeout(function(){hideMessage(success[i]);}, 3000 + (i*3000));
        }
    }

    module.exports = {
        setFadeOutMessages: setFadeOutMessages
    };
})(jQuery);
