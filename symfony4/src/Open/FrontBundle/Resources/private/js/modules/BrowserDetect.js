(function () {
    'use strict';

    const dataBrowser = [
        {string: navigator.userAgent, subString: "Edge", identity: "<PERSON>"},
        {string: navigator.userAgent, subString: "MSIE", identity: "IE"},
        {string: navigator.userAgent, subString: "Trident", identity: "IE"},
        {string: navigator.userAgent, subString: "Firefox", identity: "Firefox"},
        {string: navigator.userAgent, subString: "Opera", identity: "Opera"},
        {string: navigator.userAgent, subString: "OPR", identity: "Opera"},
        {string: navigator.userAgent, subString: "Chrome", identity: "Chrome"},
        {string: navigator.userAgent, subString: "Safari", identity: "Safari"}
    ];

    let versionSearchString = '';
    let browser = '';
    let version = null;



    let searchString = function (data) {
        for (let i = 0; i < data.length; i++) {
            let dataString = data[i].string;
            versionSearchString = data[i].subString;

            if (dataString.indexOf(data[i].subString) !== -1) {
                return data[i].identity;
            }
        }
    };

    let searchVersion = function (dataString) {
        let index = dataString.indexOf(versionSearchString);
        if (index === -1) {
            return;
        }

        let rv = dataString.indexOf("rv:");
        if (versionSearchString === "Trident" && rv !== -1) {
            return parseFloat(dataString.substring(rv + 3));
        } else {
            return parseFloat(dataString.substring(index + versionSearchString.length + 1));
        }
    };


    let init = function () {
        browser = searchString(dataBrowser) || "Other";
        version = searchVersion(navigator.userAgent) || searchVersion(navigator.appVersion) || "Unknown";
    };



    module.exports = {
        init: init,
        getBrowser: function(){return browser},
        getVersion: function(){return version}
    };

})();
