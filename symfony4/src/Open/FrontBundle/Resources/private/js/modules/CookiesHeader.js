let Cookies = require('js-cookie');

(function () {
    'use strict';

    let header = null;

    /**
     * Set the cookie
     * @private
     */
    let _setCookie = function () {
        Cookies.set('cgu_cookie', true, { expires: 365 });
    };

    /**
     * Close header and set the cookie
     * @private
     */
    let _closeHeader = function() {
        header.style.display = 'none';
        _setCookie();
        window.location.reload();
    };

    /**
     * Initialize
     */
    let init = function () {
        let c = Cookies.get('cgu_cookie');
        header = document.getElementById('js-cookies-header');
        // Show bar if cookie not found
        if (c === undefined) {
            header.classList.add('is-visible');
            document.getElementById('js-close-cookies-header').addEventListener('click', _closeHeader);
        } else {
            header.style.display = 'none';
        }
    };

    module.exports = {
        init: init,
    };

})();
