<script>
import purchaseRequestStore from "../stores/app-store";
import PurchaseRequestItemCartAction from "./purchase-request-item-cart-action";
import PurchaseRequestItemOfferContainer from "./purchase-request-item-offer-container";
import Modal from "./modal";

/**
 * Component that represents a purchase request item
 * Use can remove an item from his purchase request
 * Select an offer
 * Add to cart
 * remove from cart if offer has already been added to the cart
 */
export default {
  components: {
    Modal,
    PurchaseRequestItemOfferContainer,
    PurchaseRequestItemCartAction,
  },
  data: function () {
    return {
      offers: [],
      collapsed: true,
      currentPage: 0,
      countElem: 8,
      elemPerPage: 8,
      numberOfPages: 0,
      loading: false,
      sharedState: purchaseRequestStore.state,
      selected: false,
      noProduct: false,
      showModal: false,
      qtyError: "",
      request: null,
    };
  },
  computed: {
    canPlus: function () {
      return this.countElem < this.offers.length;
    },
    formatedUnitPriceOfReference: function () {
      if (this.item.unitPriceOfReference) {
        const float = parseFloat(this.item.unitPriceOfReference.replace(",", ".")).toFixed(2);
        if (this.item.currencyOfReference) {
          new Intl.NumberFormat(this.sharedState.translation.language, {
            style: "currency",
            currency: this.item.currencyOfReference,
          }).format(
              float
          );
        }
        return float;
      }
      return '';
    },
  },
  mounted: function () {
    if (this.launchSearch) this.searchOffers();
  },
  destroyed() {
    if (this.request !== null) {
      this.request.abort();
    }
  },
  watch: {
    launchSearch(launch) {
      if (launch) {
        this.searchOffers();
      }
    },
  },
  props: ["launchSearch", "id", "item"],
  methods: {
    showDetails: function () {
      this.showModal = true;
    },
    hideModal: function () {
      this.showModal = false;
    },
    removeItem: function () {
      this.$emit("removeItem", this.id);
    },

    addToCart: function (offerId) {
      this.selected.inCart = true;
      this.selected.selected = true;
      this.offers = [this.selected];
    },

    removeFromCart: function (offerId) {
      this.currentPage = 0;
      this.numberOfPages = 0;
      this.selected = false;
      this.offers = [];
      purchaseRequestStore.removeSelectedOffer(this.id);
      this.searchOffers();
    },

    selectOffer: function (offer) {
      this.$emit("selectOffer", this.id, offer);
      this.selected = offer;
      this.selected.purchaseRequestItemId = this.id;
      this.qtyError = "";
      let vueComponent = this;
      if (offer !== null) {
        this.offers.map(function (o) {
          o.selected = false;
          if (o.id === offer.id) {
            vueComponent.qtyError = offer.error;
            o.selected = true;
          }
          return o;
        });
      }
    },

    toggleCollapse: function () {
      this.collapsed = !this.collapsed;
    },

    onQuantityChange(qty) {
      this.qtyError = "";
      if (qty.id === this.selected.id) {
        this.qtyError = qty.error;
      }
      this.offers.map(function (o) {
        if (o.id === qty.id) {
          o.userQuantity = qty.quantity;
        }
        return o;
      });
    },

    showMore() {
      this.countElem += this.elemPerPage;
    },

    searchOffers: function () {
      this.countElem = this.elemPerPage;
      let vueComponent = this;
      // initialize pagination
      let searchOfferUrl = purchaseRequestStore.state.searchOfferUrl.replace(
        "--purchaseRequestItemId--",
        vueComponent.id
      );
      vueComponent.loading = true;
      var offer = purchaseRequestStore.getSelectedOffer(vueComponent.id);
      if (offer) {
        offer.batchSize = parseInt(offer.batchSize);
        offer.inCart = true;
        offer.quantity = parseInt(offer.quantity);
        vueComponent.offers.push(offer);
        vueComponent.selectOffer(offer);
        vueComponent.collapsed = false;
        vueComponent.endSearch();
      } else {
        if (vueComponent.request !== null) {
          vueComponent.request.abort();
          vueComponent.request = null;
        }
        vueComponent.request = $.ajax({
          type: "GET",
          url: searchOfferUrl,
          contentType: "application/json; charset=utf-8",
          data: { page: vueComponent.currentPage + 1 },
          dataType: "json",
          success: function (searchResult) {
            vueComponent.checkProducts(searchResult);
            vueComponent.endSearch();
          },
          error: function error(msg) {
            if (msg.statusText !== "abort") {
              vueComponent.searchOffers();
            }
          },
        });
      }
    },
    checkProducts(searchResult) {
      this.currentPage = this.currentPage + 1;
      this.numberOfPages = this.numberOfPages;
      let vueComponent = this;
      searchResult.offers.forEach(function (offer) {
        offer.id = offer.izbergReference;
        offer.name = offer.offerTitle;
        offer.selected = false;
        offer.inCart = false;
        offer.batchSize = parseInt(offer.batchSize);
        offer.userQuantity = vueComponent.item.quantityExpected;
        vueComponent.offers.push(offer);
      });
      this.offers.length === 0
        ? (this.noProduct = true)
        : (this.collapsed = false);
      this.offers.length === 1 && !this.offers[0].limited
        ? this.selectOffer(this.offers[0])
        : null;
      let purchaseRequestItem = JSON.parse(JSON.stringify(this.item));
      delete purchaseRequestItem.id;
      delete purchaseRequestItem.offer;
      delete purchaseRequestItem.launchSearch;
      this.noProduct
        ? purchaseRequestStore.addNoProductsItem(purchaseRequestItem)
        : purchaseRequestStore.addProductsItem(this.offers, vueComponent.id);
    },
    endSearch() {
      this.loading = false;
      this.$emit("search");
      purchaseRequestStore.nextSearch();
      purchaseRequestStore.finishedSearch();
    },
  },
};
</script>

<template>
  <div class="card" v-bind:class="{ collapsed: collapsed, 'no-product': noProduct }">
    <Modal v-if="showModal" :title="item.productName" @closeModal="hideModal">
      <div style="padding: 20px">
        {{sharedState.translation.pr_item.buyer_reference}} :
        <b>{{item.buyerReference}}</b>
        <br />
        {{sharedState.translation.pr_item.manufacturer_reference}} :
        <b>{{item.manufacturerReference}}</b>
        <br />
        {{sharedState.translation.pr_item.manufacturer_name}} :
        <b>{{item.manufacturerName}}</b>
        <br />
        {{sharedState.translation.pr_item.product_name}} :
        <b>{{item.productName}}</b>
        <br />
        {{sharedState.translation.pr_item.quantity_expected}} :
        <b>{{item.quantityExpected}}</b>
        <br />
        {{sharedState.translation.pr_item.unit_price_of_reference}} :
        <b>
          <span v-if="item.unitPriceOfReference != ''">{{formatedUnitPriceOfReference}}</span>
        </b>
        <br />
        {{sharedState.translation.pr_item.expected_delivery_date}} :
        <b>{{item.expectedDeliveryDate}}</b>
        <br />
        {{sharedState.translation.pr_item.cost_center}} :
        <b>{{item.costCenter}}</b>
        <br />
        {{sharedState.translation.pr_item.purchase_request_number}} :
        <b>{{item.purchaseRequestNumber}}</b>
        <br />
        {{sharedState.translation.pr_item.buyer_order_number}} :
        <b>{{item.buyerOrderNumber}}</b>
        <br />
        {{sharedState.translation.pr_item.order_line}} :
        <b>{{item.orderLine}}</b>
      </div>
    </Modal>
    <div class="card-close" @click="removeItem">
      <svg xmlns="http://www.w3.org/2000/svg" width="26" height="26" viewBox="0 0 26 26">
        <path
          fill="#9600FF"
          d="M13 26c2.341 0 4.508-.585 6.5-1.756 1.992-1.17 3.573-2.752 4.744-4.744C25.414 17.508 26 15.341 26 13s-.585-4.508-1.756-6.5c-1.17-1.992-2.752-3.573-4.744-4.744C17.508.586 15.341 0 13 0S8.492.585 6.5 1.756C4.508 2.926 2.927 4.508 1.756 6.5.586 8.492 0 10.659 0 13s.585 4.508 1.756 6.5c1.17 1.992 2.752 3.573 4.744 4.744C8.492 25.414 10.659 26 13 26zm3.853-6.448c-.157 0-.306-.052-.446-.157L13 15.935l-3.407 3.46c-.14.105-.297.157-.472.157s-.315-.052-.42-.157l-2.096-2.097c-.105-.14-.157-.288-.157-.445 0-.157.052-.306.157-.446L10.065 13l-3.46-3.407c-.105-.14-.157-.297-.157-.472s.052-.315.157-.42l2.097-2.096c.14-.105.288-.157.445-.157.157 0 .306.052.446.157L13 10.065l3.407-3.46c.14-.105.297-.157.472-.157s.315.052.42.157l2.096 2.097c.105.14.157.288.157.445 0 .157-.052.306-.157.446L15.935 13l3.46 3.407c.105.14.157.297.157.472s-.052.315-.157.42l-2.097 2.096c-.14.105-.288.157-.445.157z"
        />
      </svg>
    </div>
    <div class="card-title">
      <div class="card-title-content">
        <div class="product-ref">
          {{sharedState.translation.pr_item.product_name}} :
          <b>{{item.productName}}</b>
          <br />
          {{sharedState.translation.pr_item.buyer_reference}} : {{item.buyerReference}}
          <br />
          {{sharedState.translation.pr_item.quantity_expected}} : {{item.quantityExpected}}
          <br />
          {{sharedState.translation.pr_item.unit_price_of_reference}} :
          <b>
            <span v-if="item.unitPriceOfReference != ''">{{formatedUnitPriceOfReference}}</span>
          </b>
          <br />
          <div class="details">
            <button @click="showDetails">{{sharedState.translation.pr_item.details}}</button>
          </div>
        </div>
        <div class="no-product-info" v-if="noProduct">{{sharedState.translation.pr_item.no_ref}}</div>
        <PurchaseRequestItemCartAction
          :inCart="selected.inCart"
          :qtyError="qtyError"
          v-if="offers.length > 0"
          @addToCart="addToCart"
          @removeFromCart="removeFromCart"
          :offer="selected"
        ></PurchaseRequestItemCartAction>
      </div>
      <div class="card-collapse-toggle" v-bind:class="{ loading: loading }">
        <svg
          @click="toggleCollapse"
          v-if="offers.length > 0"
          xmlns="http://www.w3.org/2000/svg"
          viewBox="0 0 448 512"
          width="15"
        >
          <path
            fill="currentColor"
            d="M207.029 381.476L12.686 187.132c-9.373-9.373-9.373-24.569 0-33.941l22.667-22.667c9.357-9.357 24.522-9.375 33.901-.04L224 284.505l154.745-154.021c9.379-9.335 24.544-9.317 33.901.04l22.667 22.667c9.373 9.373 9.373 24.569 0 33.941L240.971 381.476c-9.373 9.372-24.569 9.372-33.942 0z"
          />
        </svg>
      </div>
    </div>
    <div class="card-collapse-container">
      <PurchaseRequestItemOfferContainer
        @quantityChange="onQuantityChange"
        :id="id"
        :item="item"
        :loading="loading"
        @searchOffers="searchOffers"
        :canPlus="canPlus"
        @selectOffer="selectOffer"
        :offers="offers.slice(0,countElem)"
      ></PurchaseRequestItemOfferContainer>
      <div class="more" v-if="canPlus" v-bind:class="{ loading: loading }">
        <button @click="showMore">
          <span></span>
          {{sharedState.translation.pr_item.see_more}}
        </button>
      </div>
    </div>
  </div>
</template>
