<script>
import PurchaseRequestStore from "../stores/app-store";
/**
 * button interact with the cart
 * item can be added to cart
 * item can be removed from cart
 * the button is disable if offer has not been selected by the user
 */
export default {
  data: function () {
    return {
      sharedState: PurchaseRequestStore.state,
      loading: false,
    };
  },
  methods: {
    updateCounter(totalCounter) {
      $("#cart-quantity-eur").text(totalCounter.EUR).parent().addClass("hide");
      $("#cart-quantity-usd").text(totalCounter.USD).parent().addClass("hide");
      if (totalCounter.EUR > 0) {
        $("#cart-quantity-eur").parent().removeClass("hide");
      }
      if (totalCounter.USD > 0) {
        $("#cart-quantity-usd").parent().removeClass("hide");
      }
    },
    addToCart: function () {
      let vueComponent = this;
      let addToCartUrl = PurchaseRequestStore.state.addToCartUrl.replace(
        "--purchaseRequestItemId--",
        vueComponent.offer.purchaseRequestItemId
      );
      let token = PurchaseRequestStore.state.addToCartCsrf;
      this.loading = true;

      $.ajax({
        type: "POST",
        url: addToCartUrl,
        data: {
          offerId: vueComponent.offer.id,
          quantity: vueComponent.offer.quantity,
          token: token,
        },
        success: function (totalCounter) {
          vueComponent.updateCounter(totalCounter);
          vueComponent.$emit("addToCart", vueComponent.offer.id);
        },
        error: function error(msg) {
          if(msg.status == 400) {
            vueComponent.$emit("removeFromCart", vueComponent.offer.id);
          }
        },
        complete: function () {
          vueComponent.loading = false;
        },
      });
    },
    removeFromCart: function () {
      let vueComponent = this;
      let removeFromCartUrl = PurchaseRequestStore.state.removeFromCartUrl.replace(
        "--purchaseRequestItemId--",
        vueComponent.offer.purchaseRequestItemId
      );
      let token = PurchaseRequestStore.state.removeFromCartCsrf;
      vueComponent.loading = true;
      $.ajax({
        type: "POST",
        url: removeFromCartUrl,
        data: {
          token: token,
        },
        success: function (totalCounter) {
          vueComponent.updateCounter(totalCounter);
          vueComponent.$emit("removeFromCart", vueComponent.offer.id);
        },
        error: function error(msg) {},
        complete: function () {
          vueComponent.loading = false;
        },
      });
    },
  },
  computed: {
    hasOffer: function () {
      return this.offer !== false;
    },
    hasError: function () {
      return this.qtyError !== undefined && this.qtyError !== "";
    },
  },
  props: ["offer", "qtyError", "inCart"],
};
</script>

<template>
  <div class="action">
    <div style="margin: 10px" class="tooltip">
      <button
        v-if="!inCart"
        :disabled="!hasOffer || hasError"
        @click="addToCart"
        v-bind:class="{ loading: loading }"
        class="btn btn-primary"
        style="padding: 5px 15px;"
      >
        <span>{{ sharedState.translation.cart.add }}</span>
      </button>
      <div class="tooltiptext" v-if="hasError">
        <span class="info" style="color: #fff">{{qtyError}}</span>
      </div>
    </div>
    <button
      v-if="inCart"
      @click="removeFromCart"
      class="btn btn-link product-remove-cart"
      v-bind:class="{ loading: loading }"
    >
      <span>
        <svg xmlns="http://www.w3.org/2000/svg" width="17" height="16" viewBox="0 0 17 16">
          <path
            fill="#6B6F82"
            d="M12.67 1c0-.6-.4-1-1-1h-6c-.6 0-1 .4-1 1v2h-4v2h1v10c0 .6.4 1 1 1h12c.6 0 1-.4 1-1V5h1V3h-4V1zm-6 1h4v1h-4V2zm7 3v9h-10V5h10z"
          />
        </svg>
        {{ sharedState.translation.cart.remove }}
      </span>
    </button>
  </div>
</template>
