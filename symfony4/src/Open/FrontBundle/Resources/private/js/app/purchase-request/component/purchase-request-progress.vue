<script>
import PurchaseRequestStore from "../stores/app-store";
/**
 * Component that represents a purchase request
 * a purchase request is compound of purchase request items
 */

export default {
  data: function () {
    return { sharedState: PurchaseRequestStore.state };
  },
  computed: {
    percent() {
      const percent = Math.round(
        (this.finishedSearch * 100) / this.totalSearch
      );
      if (percent === 100) {
        this.sharedState.finish = true;
      } else {
        this.sharedState.finish = null;
      }
      return percent;
    },
    totalSearch() {
      return this.sharedState.items.length;
    },
    finishedSearch() {
      return this.sharedState.items.filter(function (item) {
        return item.launchSearch === true;
      }).length;
    },
  },
};
</script>

<template>
  <div class="progress">
    <div class="progress-bar" :style="{width: percent + '%'}"></div>
    <div class="progress-info">{{percent}} %</div>
  </div>
</template>
