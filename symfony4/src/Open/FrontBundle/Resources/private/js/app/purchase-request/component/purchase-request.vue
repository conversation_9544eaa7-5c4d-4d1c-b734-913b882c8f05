<script>
import PurchaseRequestItem from "./purchase-request-item";
/**
 * Component that represents a purchase request
 * a purchase request is compound of purchase request items
 */
export default {
  name: "PurchaseRequest",
  components: {
    PurchaseRequestItem,
  },
  props: ["items"],
  methods: {
    removeItem: function (itemId) {
      this.$emit("removeItem", itemId);
    },
    selectOffer: function (itemId, offerId) {
      this.$emit("selectOffer", itemId, offerId);
    },
  },
};
</script>

<template>
  <div class="products-list">
    <PurchaseRequestItem
      :key="item.id"
      v-for="item in items"
      :id="item.id"
      :item="item"
      @removeItem="removeItem"
      @selectOffer="selectOffer"
      :launchSearch="item.launchSearch"
    ></PurchaseRequestItem>
  </div>
</template>

