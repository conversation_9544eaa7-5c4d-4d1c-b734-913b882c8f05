<script>
import PurchaseRequestItemOffer from "./purchase-request-item-offer";

/**
 * Component that contains the offers resulting from the purchase request item search
 */
export default {
  components: {
    PurchaseRequestItemOffer,
  },
  props: ["offers", "canPlus", "loading", "id", "item"],
  methods: {
    selectOffer: function (offer) {
      this.$emit("selectOffer", offer);
    },
    onQuantityChange(qty) {
      this.$emit("quantityChange", qty);
    },
  },
};
</script>

<template>
  <div class="products">
    <PurchaseRequestItemOffer
      @selectOffer="selectOffer"
      v-for="offer in offers"
      :key="offer.id"
      :selected="offer.selected"
      :offer="offer"
      :id="id"
      :item="item"
      @quantityChange="onQuantityChange"
    />
  </div>
</template>
