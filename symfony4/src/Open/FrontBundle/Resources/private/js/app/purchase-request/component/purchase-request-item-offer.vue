<script>
import purchaseRequestStore from "../stores/app-store";
import SpinInput from "./spin-input";

/**
 * component that represents an offer card in the purchase request item offer container
 */
export default {
  components: {
    SpinInput,
  },
  props: ["offer", "selected", "id", "item"],
  data: function () {
    return {
      quantity: this.item.quantityExpected,
      error: "",
      sharedState: purchaseRequestStore.state,
      price: 0,
      tooltipContentHtml: "",
    };
  },
  methods: {
    selectOffer: function () {
      this.$emit("selectOffer", this.offerFormat());
    },
    onQuantityChange(newQuantity) {
      this.quantity = newQuantity.qty;
      this.error = newQuantity.error;
      this.$emit("quantityChange", this.offerFormat());
      if (this.isSelected) {
        this.$emit("selectOffer", this.offerFormat());
      }
      this.unitPrice();
    },
    offerFormat: function () {
      var offer = JSON.parse(JSON.stringify(this.offer));
      offer.quantity = this.quantity;
      offer.error = this.error;
      return offer;
    },
    unitPrice: function () {
      const that = this;
      let price = this.offer.prices[this.offer.currency].toFixed(2);
      $.each(this.offer.thresholds, function (quantity, newPrice) {
        that.quantity >= quantity ? (price = newPrice.toFixed(2)) : null;
      });
      this.price = price;
    },
    tooltipContent: function () {
      let first = true;
      const that = this;
      $.each(this.offer.thresholds, function (quantity, newPrice) {
        if (first) {
          first = false;
          that.tooltipContentHtml +=
            that.sharedState.translation.offer.quantity +
            " < " +
            quantity +
            " = " +
            that.offer.prices[that.offer.currency].toFixed(2) +
            that.currency() +
            "<br>";
        }
        that.tooltipContentHtml +=
          that.sharedState.translation.offer.quantity +
          " >= " +
          quantity +
          " = " +
          newPrice.toFixed(2) +
          that.currency() +
          "<br>";
        that.quantity >= quantity ? (that.price = newPrice.toFixed(2)) : null;
      });
    },
    currency: function () {
      if (this.offer.currency === "EUR") {
        return "€";
      }
      if (this.offer.currency === "USD") {
        return "$";
      }
      return this.offer.currency;
    },
  },
  mounted: function () {
    this.tooltipContent();
  },
  computed: {
    offerCurrency: function () {
      return this.currency();
    },
    isSelected: function () {
      return this.selected ? true : false;
    },
    isLimited: function () {
      return this.offer.limited;
    },
    isBafv: function () {
      return this.offer.bafv && !this.offer.bafvAuthorized;
    },
    isBusinessEverywhere: function () {
      return this.offer.businessEverywhere;
    },
    isNoPrice: function () {
      return this.offer.noPrice;
    },
    productName: function () {
      return "product-" + this.id;
    },
    image: function () {
      let image = this.offer.offerPictures[0];
      let imageSubPath = 'images/'

      if (image.substring(0, imageSubPath.length) === imageSubPath) {
        image = "/" + image;
      }

      return image;
    },
    href: function () {
      return this.sharedState.offerDetailUrl.replace(
        "--offerId--",
        this.offer.id
      );
    },
  },
};
</script>

<template>
  <div
    class="product"
    v-bind:class="{ selected: isSelected }"
    style="display: flex;flex-direction: column"
  >
    <div style="flex: 1">
      <div class="img" v-bind:style="{ 'background-image': 'url(' + image + ')' }"></div>
      <div class="product-ref">{{ offer.sellerRef }}</div>
      <div class="tooltip" style="width: 100%;">
        <a
          style="max-width: 1010px"
          target="_blank"
          :href="href"
          class="product-title"
        >{{ offer.name }}</a>
        <div class="tooltiptext">
          <span class="info" style="color: #fff">{{ offer.name }}</span>
        </div>
      </div>
      <div class="product-info">
        <span>{{ sharedState.translation.offer.merchant }} :</span>
        {{offer.merchant.name}}
      </div>
      <div class="product-info">
        <span>{{ sharedState.translation.offer.manufacturer }} :</span>
        {{offer.manufacturerName}}
      </div>
      <div class="product-info" v-if="offer.stockClearance && offer.stockAvailability">
        <span>{{ sharedState.translation.offer.quantity }} :</span>
        {{offer.quantity}}
      </div>
      <div class="product-info" v-if="!isLimited">
        <span>{{ sharedState.translation.offer.unit_price}} :</span>
        {{price}} {{offerCurrency}}
        <div
          class="tooltip"
          style="display: inline-block; margin-left: 10px"
          v-if="tooltipContentHtml != ''"
        >
          <span>
            <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 16 16">
              <path
                fill="#9600FF"
                d="M8 0C3.579 0 0 3.579 0 8c0 4.421 3.579 8 8 8 4.421 0 8-3.579 8-8 0-4.421-3.579-8-8-8zM6.81 3.046c.153-.023.314-.047.479-.058.172-.011.33-.018.468-.018.148 0 .314.005.492.018.172.01.338.035.486.***************.302.**************.011.302.011.45 0 .143-.005.29-.01.444-.012.154-.03.302-.054.458-.148.023-.309.042-.479.053-.172.005-.331.01-.48.01-.144 0-.303-.005-.475-.01-.177-.011-.338-.03-.491-.053-.053-.302-.077-.606-.077-.902-.002-.283.022-.585.077-.9zm2.986 9.984H6.204c-.053-.23-.076-.468-.076-.71 0-.114.005-.239.018-.368.01-.13.035-.255.058-.368h.878V7.461h-.878c-.023-.114-.047-.238-.058-.368-.011-.13-.018-.249-.018-.368 0-.243.023-.479.076-.71h2.714v5.569h.878c.***************.058.368.**************.018.367 0 .243-.023.481-.076.711z"
              />
            </svg>
          </span>
          <div class="tooltiptext" v-html="tooltipContentHtml"></div>
        </div>
      </div>
      <div class="product-info" v-if="!isLimited">
        <span>{{ sharedState.translation.offer.sku_price}} {{offer.quantityPerSku}} {{offer.skuUnit}}</span>
      </div>
      <div class="product-info" style="display: flex">
        <span style="white-space: nowrap">{{ sharedState.translation.offer.quantity }} :</span>
        <SpinInput
          :min="offer.moq"
          :quantity="offer.userQuantity"
          :max="offer.quantity"
          :batchsize="offer.batchSize"
          @quantityChange="onQuantityChange"
        />
      </div>
      <div style="display: flex; margin-top: 10px">
        <span class="product-ref" style="flex: 1">
          {{offer.incoterm}} ({{offer.incotermCountry}})
          <img
            style="width: 24px;"
            v-if="offer.shippable"
            :src="sharedState.shippingIcon"
          />
          <img
              style="width: 20px;"
              src="/images/sign.svg"
              v-if="offer.frameContract !== null && offer.frameContract !== ''"
          />
        </span>
      </div>
    </div>
    <div style="margin: 10px -10px -10px;">
      <div class="tooltip" style="display: block; width: 100%;">
        <button
          v-if="!isSelected"
          :disabled="isLimited"
          @click="selectOffer"
          class="btn btn-primary"
          style="margin: 0;width: 100%;padding: 5px;"
        >{{ sharedState.translation.offer.select }}</button>
        <div
          v-if="isSelected"
          style="font-weight: bold;padding: 0 5px; text-align: center;line-height: 35px;color: #9600FF;"
        >{{ sharedState.translation.offer.selected }}</div>
        <div class="tooltiptext" v-if="isLimited">
          <div v-if="!isBusinessEverywhere">{{sharedState.translation.offer.error.businesseverywhere}}</div>
          <div v-if="isBusinessEverywhere && isBafv">{{sharedState.translation.offer.error.bafv}}</div>
          <div v-if="isBusinessEverywhere && !isBafv && isNoPrice">{{sharedState.translation.offer.error.noprice}}</div>
        </div>
      </div>
    </div>
  </div>
</template>
