const purchaseRequestStore = {
  state: {
    items: [],
    importUrl: null,
    clearUrl: null,
    clearCsrf: null,
    searchOfferUrl: null,
    addToCartUrl: null,
    addToCartCsrf: null,
    removeFromCartUrl: null,
    removeFromCartCsrf: null,
    removeItemUrl: null,
    removeItemCsrf: null,
    sendCsv: null,
    noProductItems: [],
    finishCount: 0,
    translation: {
      // translation are initialized in
      // webapplication\src\Open\FrontBundle\Resources\views\purchaseRequest\purchase_request_details.html.twig
    }
  },

  initAction(state) {
    this.state = Object.assign(this.state, state);
  },

  removeItemAction(itemId) {
    let store = this;
    let removeItemUrl = store.state.removeItemUrl.replace('--purchaseRequestItemId--', itemId);
    let token = store.state.removeItemCsrf;

    this.state.items = this.state.items.filter(function (item) {
      return !(item.id === itemId);
    });

    this.state.noProductItems = this.state.noProductItems.filter(function (item) {
      return !(item.id === itemId);
    });

    $.ajax({
      type: "POST",
      url: removeItemUrl,
      data: {
        token: token
      },
      success: function () {
      },
      error: function error(msg) {
      }
    });
  },

  selectOfferItemAction(itemId, offerId) {
    this.state.items.map(function (item) {
      if (item.id === itemId) {
        item.offerId = offerId;
      }

      return item;
    });
  },

  clearAction() {
    this.state.items = [];
    const clearUrl = this.state.clearUrl;
    const token = this.state.clearCsrf;

    this.state.noProductItems = [];
    this.state.finishCount = 0;

    $.ajax({
      type: "POST",
      url: clearUrl,
      data: {
        token: token
      },
      success: function () {

      },
      error: function(msg) {
      },
      complete: function() {}
    });
  },

  importAction(purchaseRequest) {
    var max = 3;
    purchaseRequest.items = purchaseRequest.items.map(function (v) {
      v.launchSearch = false;
      if (max > 0) v.launchSearch = true;
      max--;
      return v;
    });
    this.state.items = purchaseRequest.items;
  },

  getSelectedOffer(id) {
    var offer = null;
    this.state.items.forEach(function (v) {
      if (v.id == id) {
        offer = v.offer;
      }
    });
    return offer;
  },

  removeSelectedOffer(id) {
    this.state.items.forEach(function (v) {
      if (v.id == id) {
        v.offer = null;
      }
    });
  },

  nextSearch() {
    var items = JSON.parse(JSON.stringify(this.state.items));
    var next = false;
    items.map(function (v) {
      if (!next && !v.launchSearch) {
        next = true;
        v.launchSearch = true;
      }
    });
    this.state.items = items;
  },

  finishedSearch() {
    this.state.finishCount = this.state.finishCount + 1;
    const state = this.state;
    if (this.state.items.length === this.state.finishCount) {
      csvExport(this.state.noProductItems, { rowDelimiter: ';', headers: Object.keys(csvMapping), rename: Object.values(csvMapping) }, function (err, csv) {
        if (err) return console.error(err);
        $.ajax({
          type: "POST",
          url: state.sendCsv,
          data: { csv: csv, totalRef: state.items.length, totalNotFoundRef: state.noProductItems.length }
        });
      });
    }
  },

  addNoProductsItem(item) {
    this.state.noProductItems.push(item);
  },
  addProductsItem(offers, id) {
    this.state.items.map((item) => {
      item.offers === undefined ? item.offers = [] : null;
      item.id === id ? item.offers.push(...offers): null;
    });
  }
};

export default purchaseRequestStore;
