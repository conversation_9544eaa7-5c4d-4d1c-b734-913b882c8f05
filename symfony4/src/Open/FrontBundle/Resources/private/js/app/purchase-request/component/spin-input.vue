<script>
import purchaseRequestStore from "../stores/app-store";
/**
 * Spin input for set quantity
 */
export default {
  data: function () {
    return {
      error: "",
      componentQty: 0,
    };
  },
  props: {
    quantity: {
      type: Number,
      default: 0,
    },
    min: {
      type: Number,
      default: 0,
    },
    max: {
      type: Number,
      default: null,
    },
    batchsize: {
      type: Number,
      default: 1,
    },
  },
  computed: {
    canDown: function () {
      return this.componentQty > this.min;
    },
    canUp: function () {
      return this.max === null || this.componentQty < this.max;
    },
    hasError: function () {
      return this.error !== "";
    },
  },
  mounted: function () {
    this.componentQty = this.quantity;
    this.updateQuantity();
  },
  methods: {
    up: function () {
      this.componentQty = parseInt(this.componentQty) + this.batchsize;
      this.updateQuantity();
    },
    down: function () {
      this.componentQty = parseInt(this.componentQty) - this.batchsize;
      this.updateQuantity();
    },
    updateQuantity() {
      this.checkError();
      this.$emit("quantityChange", {
        qty: parseInt(this.componentQty),
        error: this.error,
      });
    },
    checkError() {
      this.error = "";
      if (this.max !== null && this.componentQty > this.max)
        this.error = purchaseRequestStore.state.translation.offer.quantity_errors.too_much.replace(
          "_MAX_",
          this.max
        );
      if (this.componentQty < this.min)
        this.error = this.error = purchaseRequestStore.state.translation.offer.quantity_errors.too_small.replace(
          "_MIN_",
          this.min
        );
      if (this.componentQty % this.batchsize > 0)
        this.error = purchaseRequestStore.state.translation.offer.quantity_errors.not_batch_size_multiple.replace(
          "_BATCH_SIZE_",
          this.batchsize
        );
    },
  },
};
</script>

<template>
  <div style="display: flex">
    <div style="display: flex">
      <button class="btn btn-sm btn-primary" @click="down" v-bind:disabled="!canDown">-</button>
      <div style="padding: 0 5px">
        <input
          :class="{error: hasError}"
          style="height: 24px;display:block;margin: 0;padding: 0 5px;font-size: 10px;line-height: 12px"
          type="number"
          @change="updateQuantity"
          v-bind:min="min"
          v-model="componentQty"
        />
      </div>
      <button class="btn btn-sm btn-primary" @click="up" v-bind:disabled="!canUp">+</button>
    </div>
    <div style="margin-left: 10px" class="tooltip" v-if="hasError">
      <span>
        <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 16 16">
          <path
            fill="#9600FF"
            d="M8 0C3.579 0 0 3.579 0 8c0 4.421 3.579 8 8 8 4.421 0 8-3.579 8-8 0-4.421-3.579-8-8-8zM6.81 3.046c.153-.023.314-.047.479-.058.172-.011.33-.018.468-.018.148 0 .314.005.492.018.172.01.338.035.486.***************.302.**************.011.302.011.45 0 .143-.005.29-.01.444-.012.154-.03.302-.054.458-.148.023-.309.042-.479.053-.172.005-.331.01-.48.01-.144 0-.303-.005-.475-.01-.177-.011-.338-.03-.491-.053-.053-.302-.077-.606-.077-.902-.002-.283.022-.585.077-.9zm2.986 9.984H6.204c-.053-.23-.076-.468-.076-.71 0-.114.005-.239.018-.368.01-.13.035-.255.058-.368h.878V7.461h-.878c-.023-.114-.047-.238-.058-.368-.011-.13-.018-.249-.018-.368 0-.243.023-.479.076-.71h2.714v5.569h.878c.***************.058.368.**************.018.367 0 .243-.023.481-.076.711z"
          />
        </svg>
      </span>
      <div class="tooltiptext">
        <span class="info" style="color: #fff">{{error}}</span>
      </div>
    </div>
  </div>
</template>

