<script>
import purchaseRequestStore from "../stores/app-store";
import Modal from "./modal";
/**
 * <PERSON>ton to import purchase request from CSV file
 */
export default {
  name: "PurchaseRequestImport",
  components: {
    Modal
  },
  data: () => {
    return {
      sharedState: purchaseRequestStore.state,
      loading: false,
      showModal: false,
      modalError: "",
      validators: [
        (vueComponent) => {
          // check if uploaded file is a CSV file
          let csvTypes = ["text/csv", "application/vnd.ms-excel"];
          let file = vueComponent.$refs.fileInput.files[0];
          if (!csvTypes.includes(file.type)) {
            vueComponent.error(
              purchaseRequestStore.state.translation.import.error.csv_format
            );
            return false;
          }

          return true;
        },
        (vueComponent) => {
          // check if the CSV file size is < 2M
          let file = vueComponent.$refs.fileInput.files[0];
          let filesize = (file / 1024 / 1024).toFixed(4);

          if (filesize > 2) {
            vueComponent.error(
              purchaseRequestStore.state.translation.import.error.csv_size
            );
            return false;
          }

          return true;
        },
      ],
    };
  },
  methods: {
    error(error) {
      this.modalError = error;
      this.showModal = true;
    },
    hideModal() {
      this.showModal = false;
    },
    isValid() {
      let vueComponent = this;
      let isValid = true;

      this.validators.forEach((validator) => {
        if (!validator(vueComponent)) isValid = false;
      });

      return isValid;
    },

    importCSV(event) {
      if (!this.isValid()) return;
      this.ajaxImport(event.target.files);
    },

    ajaxImport(files) {
      let vueComponent = this;
      vueComponent.loading = true;

      var formData = new FormData();
      if (files && files.length > 0) {
        formData.append("files[]", files[0], files[0].name);
      }

      $.ajax({
        type: "POST",
        url: purchaseRequestStore.state.importUrl,
        data: formData,
        processData: false,
        contentType: false,
        dataType: "json",
        success: (purchaseRequest) => {
          vueComponent.$emit("importPurchaseRequest", purchaseRequest);
        },
        error: (error) => {
          var errorText = "";
          if (error.status === 500) {
            errorText = vueComponent.sharedState.translation.import.error.internal_error;
          } else {
            errorText = error.responseJSON.message;
          }
          vueComponent.error(errorText);
        },
        complete: () => {
          vueComponent.sharedState.noProductItems = [];
          vueComponent.sharedState.finish = false;
          vueComponent.sharedState.finishCount = 0;
          vueComponent.loading = false;
        },
      });
    },
  },
};
</script>

<template>
  <form method="post" enctype="multipart/form-data">
    <Modal v-if="showModal" @closeModal="hideModal">
      <p style="padding: 20px">{{modalError}}</p>
    </Modal>
    <label
      for="purchase-request-import"
      class="btn import btn-primary"
      v-bind:class="{ loading: loading }"
    >
      <span>
        <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 16 22">
          <g id="Group" transform="translate(1089.000000, 227.000000)">
            <path
              id="_xF574_"
              class="st0"
              d="M-1074.1-205.4c0.3,0,0.5-0.1,0.7-0.3s0.3-0.4,0.3-0.7v-13.5h-5.6c-0.3,0-0.5-0.1-0.7-0.3s-0.3-0.4-0.3-0.7v-5.6h-8.2c-0.3,0-0.5,0.1-0.7,0.3s-0.3,0.4-0.3,0.7v19c0,0.3,0.1,0.5,0.3,0.7s0.4,0.3,0.7,0.3H-1074.1zM-1073.1-221.1v-0.2c0-0.3-0.1-0.5-0.3-0.7l-4-4c-0.2-0.2-0.4-0.3-0.7-0.3h-0.2v5.2H-1073.1z M-1080.3-208h-1.3c-0.2,0-0.3-0.1-0.5-0.2s-0.2-0.3-0.2-0.5v-3.3h-2.7c-0.3,0-0.5-0.1-0.6-0.4s-0.1-0.5,0.2-0.7l3.9-3.9c0.1-0.1,0.3-0.2,0.5-0.2s0.4,0.1,0.5,0.2l3.9,3.9c0.2,0.2,0.3,0.4,0.2,0.7s-0.3,0.4-0.6,0.4h-2.7v3.3c0,0.2-0.1,0.3-0.2,0.5S-1080.2-208-1080.3-208z"
            />
          </g>
        </svg>
        {{ sharedState.translation.import.title }}
      </span>
    </label>
    <!-- todo on change is not the best way as we cannot reload the same file twice -->
    <input
      style="display: none"
      type="file"
      id="purchase-request-import"
      ref="fileInput"
      name="csv"
      accept=".csv"
      @change="importCSV"
    />
  </form>
</template>
