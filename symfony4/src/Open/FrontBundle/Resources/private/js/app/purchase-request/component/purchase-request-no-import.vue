<script>
import purchaseRequestStore from "../stores/app-store";
/**
 * Component that represents a purchase request
 * a purchase request is compound of purchase request items
 */
export default {
  data: function () {
    return { sharedState: purchaseRequestStore.state };
  },
};
</script>

<template>
  <div>
    <div class v-if="sharedState.items.length === 0">
      <div class="no-import">
        <h1 v-html="sharedState.translation.import_waiting"></h1>
      </div>
    </div>
  </div>
</template>
