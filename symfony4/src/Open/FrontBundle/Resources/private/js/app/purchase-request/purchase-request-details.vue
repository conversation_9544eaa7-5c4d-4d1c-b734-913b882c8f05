<script>
import PurchaseRequestStore from "./stores/app-store";

import PurchaseRequestImport from "./component/purchase-request-import";
import PurchaseRequestNoImport from "./component/purchase-request-no-import";
import PurchaseRequestProgress from "./component/purchase-request-progress";
import PurchaseRequestClear from "./component/purchase-request-clear";
import PurchaseRequest from "./component/purchase-request";

export default {
  data() {
    return {
      sharedState: PurchaseRequestStore.state,
    };
  },
  components: {
    PurchaseRequestImport,
    PurchaseRequestNoImport,
    PurchaseRequestProgress,
    PurchaseRequestClear,
    PurchaseRequest,
  },
  methods: {
    removeItem(itemId) {
      PurchaseRequestStore.removeItemAction(itemId);
    },
    selectOffer(itemId, offerId) {
      PurchaseRequestStore.selectOfferItemAction(itemId, offerId);
    },
    clear() {
      PurchaseRequestStore.clearAction();
    },
    importPurchaseRequest(purchaseRequest) {
      PurchaseRequestStore.importAction(purchaseRequest);
    },
    exportCsvNoProduct() {
      csvExport(
        this.sharedState.noProductItems,
        {
          rowDelimiter: ";",
          headers: Object.keys(csvMapping),
          rename: Object.values(csvMapping),
        },
        (err, csv) => {
          if (err) return console.error(err);
          const date = new Date();
          forceDownload(
            csv,
            "no-product-purchase-request-" + date.toISOString() + ".csv",
            "text/csv;charset=utf-8;"
          );
        }
      );
    },
    exportCsvProduct() {
      const foundProduct = [];

      this.sharedState.items.map(item => {
        if (item.offers.length > 0) {
          item.offers.map((offer) => {
            if (offer.limited) {
              foundProduct.push({
                'buyerReference': item.buyerReference,
                'manufacturerReference': item.manufacturerReference,
                'quantityExpected': item.quantityExpected,
                'sellerRef': offer.sellerRef,
                'price': '',
                'quantityPerSku': '',
                'vendorName': offer.merchant.name,
                'productName': offer.name,
                'incoterm': offer.incoterm + '(' + offer.incotermCountry + ')',
                'moq': '',
                'deliveryTime': '',
                'shippable': '',
                'frameContract': ''
              });
            } else {
              foundProduct.push({
                'buyerReference': item.buyerReference,
                'manufacturerReference': item.manufacturerReference,
                'quantityExpected': item.quantityExpected,
                'sellerRef': offer.sellerRef,
                'price': offer.prices[offer.currency].toFixed(2) + ' ' + offer.currency,
                'quantityPerSku': offer.quantityPerSku,
                'vendorName': offer.merchant.name,
                'productName': offer.name,
                'incoterm': offer.incoterm + '(' + offer.incotermCountry + ')',
                'moq': offer.moq,
                'deliveryTime': offer.deliveryTime,
                'shippable': offer.shippable ? 'yes' : 'no',
                'frameContract': offer.frameContract ? 'yes' : 'no'
              });
            }
          });
        }
      });

      csvExport(
        foundProduct,
        {
          rowDelimiter: ";",
          headers: Object.keys(foundCsvMapping),
          rename: Object.values(foundCsvMapping),
        },
        (err, csv) => {
          if (err) return console.error(err);
          const date = new Date();
          forceDownload(
            csv,
            "found-product-purchase-request-" + date.toISOString() + ".csv",
            "text/csv;charset=utf-8;"
          );
        }
      );
    }
  },
  mounted() {
    PurchaseRequestStore.importAction(this.sharedState);
  },
};
</script>

<template>
  <div>
    <div class="stickyHeader">
      <div class="purchase-header">
        <h1 style="display: flex;align-items: center;">
          {{ sharedState.translation.title }}
          <PurchaseRequestProgress v-if="sharedState.items.length > 0 && !sharedState.finish" />
        </h1>
        <div class="btn-container" style="display: flex;align-items: center;">
          <PurchaseRequestImport
            v-if="sharedState.items.length === 0"
            @importPurchaseRequest="importPurchaseRequest"
          />
          <PurchaseRequestClear v-if="sharedState.items.length > 0" @clear="clear" />
        </div>
      </div>
      <div class="btn-container d-flex" style="margin-left: 15px; flex: 1; justify-content: flex-end">
        <button
          style="padding: 5px 15px"
          class="btn import btn-primary"
          @click="exportCsvNoProduct"
          v-if="sharedState.finishCount >= sharedState.items.length && sharedState.noProductItems.length > 0"
        >{{ sharedState.translation.export }}</button>
        <button
          style="padding: 5px 15px"
          class="btn import btn-primary ml-2"
          @click="exportCsvProduct"
          v-if="sharedState.finishCount >= sharedState.items.length && sharedState.noProductItems.length < sharedState.items.length"
        >{{ sharedState.translation.exportFound }}</button>
      </div>
      <div style="margin: 10px 0;" v-html="sharedState.translation.instruction"></div>
    </div>
    <PurchaseRequestNoImport />
    <PurchaseRequest
      :items="sharedState.items"
      @removeItem="removeItem"
      @selectOffer="selectOffer"
    />
  </div>
</template>

<style>
.stickyHeader {
  position: sticky;
  top: 162px;
  z-index: 1;
  background: #f4f5fa;
  padding: 10px;
}
</style>
