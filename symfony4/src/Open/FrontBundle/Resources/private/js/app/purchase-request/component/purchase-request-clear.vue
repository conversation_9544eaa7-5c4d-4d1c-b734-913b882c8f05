<script>
import purchaseRequestStore from "../stores/app-store";
import Modal from "./modal";

/**
 * <PERSON><PERSON> to clear purchase request
 */
export default {
  components: {
    Modal,
  },
  data() {
    return {
      sharedState: purchaseRequestStore.state,
      modal: false,
      confirmation: "confirmation",
    };
  },
  methods: {
    hideModal() {
      this.modal = false;
    },
    showModal() {
      this.modal = true;
    },
    clear() {
      this.$emit("clear");
    },
  },
};
</script>

<template>
  <div>
    <Modal v-if="modal" @closeModal="hideModal">
      <div style="padding: 20px">
        <div class="pb-2">{{confirmation}}</div>
        <div style="display: flex">
          <button class="btn btn-primary" @click="clear">{{ sharedState.translation.modal.confirm }}</button>
          <button
            class="btn btn-dark"
            @click="hideModal"
            style="margin-right: 15px;"
          >{{ sharedState.translation.modal.cancel }}</button>
        </div>
      </div>
    </Modal>
    <button class="btn btn-dark" @click="showModal">{{ sharedState.translation.clear }}</button>
  </div>
</template>
