<script>
/**
 * Component that represents a purchase request
 * a purchase request is compound of purchase request items
 */
export default {
  name: "Modal",
  props: ["title"],
  methods: {
    closeModal: function () {
      this.$emit("closeModal");
    },
  },
};
</script>

<template>
  <div class="Modal">
    <div class="Modal-overlay js-modal-overlay" style="z-index: 9999;"></div>
    <div class="Modal-wrapper">
      <div
        class="Modal-content js-modal-content"
        style="z-index: 10000; max-height: inherit; overflow-y: auto; top: 50%;"
      >
        <svg class="Icon" @click="closeModal">
          <use xlink:href="#icon-close" />
        </svg>
        <div class="Modal-inner">
          <div class="Modal-header">
            <h5 class="Modal-title">{{title}}</h5>
          </div>
          <div class="Modal-body">
            <slot></slot>
          </div>
          <div class="Modal-footer">
            <slot name="footer"></slot>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>
