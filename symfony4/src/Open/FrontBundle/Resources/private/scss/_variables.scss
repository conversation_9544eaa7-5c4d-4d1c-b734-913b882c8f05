$PRIMARY_COLOR: #9600FF;
$SECONDARY_COLOR: #2D7EA2;

$APPLI_PURPLE: $PRIMARY_COLOR;
$APPLI_PURPLE_DARK: #301B47;

$BLACK: #000;
$WHITE: #FFF;
$RED: #FF0000;
$ORANGE: orange;
$GRAY_LIGHTER: #FAFAFA;
$GRAY: #F2F2F2;
$GRAY_DARKER: #F4F4F4;
$GRAY_DARKER2: #ECECEC;
$GRAY_EVEN_DARKER: #DDD;
$GRAY_DARKEST: #797979;
$APPLI_GREEN: #A2C617;
$APPLI_BLUE: #030F40;
$APPLI_BLUE2: #3D466C;
$APPLI_GRAY: #F0F0F0;
$APPLI_GRAY1: #F6F6F6;
$APPLI_GRAY2: #E6E6E6;
$APPLI_GRAY3: #94959F;
$APPLI_YELLOW: #FEE35A;
$APPLI_GREY4: #bbbbbb;
$APPLI_GREY5: #eeeeee;
$APPLI_GREY6: #9B9B9B;
$APPLI_GREY7: #DCDCDC;
$APPLI_GREY8: #F9F9F9;
$APPLI_GREY9: #E4E3E3;
$APPLI_GREY10: #F1F1F1;
$APPLI_GREY11: #555555;

// These properties behind must be the only ones to be used

$ALSTOM_BLUE: $PRIMARY_COLOR;
$ALSTOM_MIDDLE_BLUE: $SECONDARY_COLOR;
$ALSTOM_DARK_PURPLE: #120133;
$ALSTOM_GREY_BACKGROUND: #F4F5FA;
$ALSTOM_GREY_BACK_PRODUCT: #E9EDF5;
$ALSTOM_LIGHT_GREY: #E4E5EC;
$ALSTOM_GREY: #A4A7B3;
$ALSTOM_DARK_GREY: #6B6F82;
$ALSTOM_GREEN: #1ECC8E;

// --------------------------------------------------------

$ALSTOM_GRAY1: #6d8fa5;
$ALSTOM_GREY_TEXT: $ALSTOM_GREY;
$ALSTOM_RED: $PRIMARY_COLOR;
$ALSTOM_BLUE_TEXT: $SECONDARY_COLOR;

$APPLI_DARK_YELLOW: $PRIMARY_COLOR;
$APPLI_YELLOW: $WHITE;
$PRIMARY_COLOR_DARK: $PRIMARY_COLOR;


$ERROR: #E8A7A7;
$ERROR_TRANSPARENT: #ff000026;

$OK: #c7eab0;
$PENDING: #f2e397;

$PRIMARY_COLOR_LIGHTER_THAN_DARK: #3B2750;




$TEXT_DEFAULT: #4E4F56;


$BREAKPOINT_SMALL: max-width 320px;

$BREAKPOINT_SMALLER_MOBILE: max-width 375px;

$BREAKPOINT_MOBILE: min-width 375px;
$BREAKPOINT_MOBILE_MAX: max-width 667px;
$BREAKPOINT_TABLET: min-width 667px;
$BREAKPOINT_DESKTOP: min-width 960px;
$BREAKPOINT_DESKTOP_WIDE: min-width 1200px;
$BREAKPOINT_MENU_MIN: min-width 800px;
$BREAKPOINT_MENU_MAX: max-width 800px;

$BREAKPOINT_NOT_DESKTOP: max-width 960px;

$SITE_WIDTH: 960px;
$SITE_WIDTH_WIDE: 1200px;


$NAV_USER_HEIGHT: 100px;
$NAV_SEARCH_HEIGHT: 50px;
$NAV_LINKS_HEIGHT: 60px;
$NAV_SEARCH_PADDING: 15px;
$NAV_LINKS_TOP: $NAV_USER_HEIGHT;
$NAV_HEIGHT: $NAV_USER_HEIGHT + $NAV_LINKS_HEIGHT;
$NAV_HEIGHT_WITH_SEARCHBAR: $NAV_LINKS_TOP + $NAV_LINKS_HEIGHT;

$NAV_USER_HEIGHT_MOBILE: 6em;
$NAV_SEARCH_HEIGHT_MOBILE: 5em;
$NAV_SEARCH_PADDING_MOBILE: 0.5em;
$NAV_HEIGHT_WITH_SEARCHBAR_MOBILE: $NAV_USER_HEIGHT_MOBILE;

$FOOTER_HEIGHT: 710px;

$FORM_WIDTH: 600px;
$FORM_LARGE_WIDTH: 80%;
$LIST_WIDTH: 700px;
