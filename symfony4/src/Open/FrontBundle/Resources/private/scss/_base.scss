@import "variables";

@import "components/Icon";
@import "components/Modal";
@import "components/Messages";
@import "components/Menu";
@import "components/Form";
@import "components/Checkbox";
@import "components/Table";
@import "components/Product";
@import "components/ProductList";
@import "components/Searchbar";
@import "components/Dropdown";
@import "components/Select";
@import "components/TabInfos";
@import "components/HeaderNavbar";
@import "components/Pagination";
@import "components/classic";
@import "components/classic.date";

@import "components/Header";
@import "modules/Footer";
@import "modules/Carte";

@import "modules/Faq";

@import "modules/Disclaimer";

@import "pages/Homepage";
@import "pages/Signin";
@import "pages/Register";
@import "pages/Profile";
@import "pages/Company";
@import "pages/User";
@import "pages/Site";
@import "pages/Document";
@import "pages/Site";
@import "pages/Ticket";
@import "pages/CGU";
@import "pages/Home";
@import "pages/Personae";
@import "pages/Offers";
@import "pages/OfferDetail.scss";
@import "pages/Search";
@import "pages/Cart";
@import "pages/CompanyCatalog";
@import "pages/Order";
@import "pages/Comparison";
@import "pages/Dispute";
@import "pages/Invoice";
@import "pages/PaymentMode";
@import "pages/Stats";
@import "pages/WishList";
@import "pages/PurchaseRequest";

@import url("https://fonts.googleapis.com/css?family=Poppins:400,400i");

.select-wrapper {
  background-image: url("../images/arrow-select.png");
  background-position: calc(100% - 20px) 29px;
  background-size: 13px;
}

@font-face {
  font-family: "HKNova";
  src: url("../fonts/HK Nova/HKNova-Light.otf") format("opentype");
  font-weight: 300;
}

@font-face {
  font-family: "HKNova";
  src: url("../fonts/HK Nova/HKNova-Regular.otf") format("opentype");
  font-weight: 400;
}

@font-face {
  font-family: "HKNova";
  src: url("../fonts/HK Nova/HKNova-Medium.otf") format("opentype");
  font-weight: 500;
}

@font-face {
  font-family: "HKNova";
  src: url("../fonts/HK Nova/HKNova-SemiBold.ttf") format("truetype");
  font-weight: 600;
}

@font-face {
  font-family: "HKNova";
  src: url("../fonts/HK Nova/HKNova-Bold.otf") format("opentype");
  font-weight: 700;
}

@font-face {
  font-family: "HKNova";
  src: url("../fonts/HK Nova/HKNova-Heavy.otf") format("opentype");
  font-weight: 900;
}

@font-face {
  font-family: "OpenSans";
  src: url("../fonts/Open_Sans/OpenSans-Light.ttf") format("truetype");
  font-weight: 300;
}

@font-face {
  font-family: "OpenSans";
  src: url("../fonts/Open_Sans/OpenSans-LightItalic.ttf") format("truetype");
  font-weight: 300;
  font-style: italic;
}

@font-face {
  font-family: "OpenSans";
  src: url("../fonts/Open_Sans/OpenSans-Regular.ttf") format("truetype");
  font-weight: 400;
}

@font-face {
  font-family: "OpenSans";
  src: url("../fonts/Open_Sans/OpenSans-Italic.ttf") format("truetype");
  font-weight: 400;
  font-style: italic;
}

@font-face {
  font-family: "OpenSans";
  src: url("../fonts/Open_Sans/OpenSans-SemiBold.ttf") format("truetype");
  font-weight: 600;
}

@font-face {
  font-family: "OpenSans";
  src: url("../fonts/Open_Sans/OpenSans-SemiBoldItalic.ttf") format("truetype");
  font-weight: 600;
  font-style: italic;
}

@font-face {
  font-family: "OpenSans";
  src: url("../fonts/Open_Sans/OpenSans-Bold.ttf") format("truetype");
  font-weight: 700;
}

@font-face {
  font-family: "OpenSans";
  src: url("../fonts/Open_Sans/OpenSans-BoldItalic.ttf") format("truetype");
  font-weight: 700;
  font-style: italic;
}

@font-face {
  font-family: "OpenSans";
  src: url("../fonts/Open_Sans/OpenSans-ExtraBold.ttf") format("truetype");
  font-weight: 800;
}

@font-face {
  font-family: "OpenSans";
  src: url("../fonts/Open_Sans/OpenSans-ExtraBoldItalic.ttf") format("truetype");
  font-weight: 800;
  font-style: italic;
}

* {
  margin: 0;
}
html, body {
  height: 100%;
}

body {
  font-size: 14px;
  margin: 0;
  font-family: "OpenSans", "Poppins", sans-serif;
  font-weight: 400;
  line-height: 1.5;
  color: #212529;
  text-align: left;
  background-color: #fff;
}

h1, h2, h3, h4 {
  font-family: "HKNova", "Poppins", sans-serif;
  font-weight: 600;

}

.page-wrap {
  min-height: 100%;

  @include  breakpoint($BREAKPOINT_DESKTOP) {
    margin-bottom: -$FOOTER_HEIGHT; /* equal to footer height */
  }
}

.page-wrap:after {
  content: "";
  display: block;
}

h2 {
  color: $PRIMARY_COLOR;
  font-weight: bold;
  font-size: 1.5rem;
}

h4 {
  color: $APPLI_GREY6;
  font-size: 1rem;
}

main.container {
  padding: 60px 0 0 0;
  margin: 0 auto;

  .container-inner {
    margin-top: 20px;
  }

  @include breakpoint($BREAKPOINT_DESKTOP) {
    max-width: 1200px;
    padding: $NAV_HEIGHT_WITH_SEARCHBAR 15px 0 15px;
  }

  @include breakpoint($BREAKPOINT_NOT_DESKTOP) {
    padding: $NAV_HEIGHT_WITH_SEARCHBAR_MOBILE 0 0 0;
  }

  @include breakpoint($BREAKPOINT_MOBILE_MAX) {
    .Messages {
      margin: 0;
    }
  }
}

html.menu--isActive {
  overflow: hidden;
}

/** #13605 - Fix IE11 issue when removing images **/ 
  /** https://stackoverflow.com/questions/29932780/what-would-cause-click-events-to-stop-working-in-internet-explorer-11 **/
use {
  pointer-events: none;
}

.breadcrumb-container + main.container {
  padding-top: 0;
}

.hide {
  display: none !important;
}

.h-line {
  background-color: $ALSTOM_LIGHT_GREY;
  width: 100%;
  height: 1px;
}

.v-line {
  background-color: $GRAY_DARKEST;
  width: 1px;
}

.arrow-up,
.arrow-down {
  width: 0;
  height: 0;
  border-left: 10px solid transparent;
  border-right: 10px solid transparent;
  border-bottom: 10px solid $WHITE;
}

.arrow {
  border-width: 0 2px 2px 0;
  border-color: $ALSTOM_DARK_GREY;
  &.right {
    transform: rotate(-45deg);
    -webkit-transform: rotate(-45deg);
  }

  &.left {
    transform: rotate(135deg);
    -webkit-transform: rotate(135deg);
  }

  &.up {
    transform: rotate(-135deg);
    -webkit-transform: rotate(-135deg);
  }
}

.btn-primary {
  background-color: $PRIMARY_COLOR;
  color: $WHITE;
  border: solid 2px $PRIMARY_COLOR;

  &:hover {
    background-color: $WHITE;
    color: $PRIMARY_COLOR;
    border: solid 2px $PRIMARY_COLOR;
  }
}

.btn-secondary {
  background-color: $SECONDARY_COLOR;
  color: $WHITE;
  border: solid 2px $SECONDARY_COLOR;

  &:hover {
    background-color: $WHITE;
    color: $SECONDARY_COLOR;
    border: solid 2px $SECONDARY_COLOR;
  }
}

.btn-primary.inverse {
  background-color: $WHITE;
  color: $PRIMARY_COLOR;
  border: solid 2px $PRIMARY_COLOR;

  &:hover {
    background-color: $PRIMARY_COLOR;
    color: $WHITE;
    border: solid 2px $PRIMARY_COLOR;
  }
}

.btn-grey {
  background-color: $ALSTOM_GREY;
  color: $WHITE;
  border: solid 2px $ALSTOM_GREY;

  &:hover {
    background-color: $WHITE;
    color: $ALSTOM_GREY;
    border: solid 2px $ALSTOM_GREY;
  }
}

.btn-disabled {
  background-color: $ALSTOM_GREY;
  color: $WHITE;
  border: solid 2px $ALSTOM_GREY;
  cursor: default !important;
  &:hover {
    background-color: $ALSTOM_GREY;
    color: $WHITE;
    border: solid 2px $ALSTOM_GREY;
  }
}

.desktop-only {
  @include breakpoint($BREAKPOINT_NOT_DESKTOP) {
    display: none !important;
  }
}

.desktop-tablet-only {
  @include breakpoint($BREAKPOINT_MOBILE_MAX) {
    display: none !important;
  }
}

.tablet-only {
  @include breakpoint($BREAKPOINT_NOT_DESKTOP) {
    @include breakpoint($BREAKPOINT_MOBILE_MAX) {
      display: none !important;
    }
  }
  @include breakpoint($BREAKPOINT_DESKTOP) {
    display: none !important;
  }
}

.mobile-only {
  @include breakpoint($BREAKPOINT_DESKTOP) {
    display: none !important;
  }
}

.mobile-max-only {
  @include breakpoint($BREAKPOINT_TABLET) {
    display: none !important;
  }
}


.background-primary {
  background-color: $PRIMARY_COLOR;
}

.background-secondary {
  background-color: $SECONDARY_COLOR;
}

.color-primary {
  color: $PRIMARY_COLOR;
}

.pointer {
  cursor: pointer;
}

/* Tooltip text */
.tooltip {
  position: relative;
  display: inline-block;
  cursor: default;
  width: fit-content;
  &:hover {
    .tooltiptext {
      visibility: visible;
      opacity: 1;
    }
  }
  .tooltiptext {
    visibility: hidden;
    width: 150px;
    word-wrap: break-word;
    background-color: $PRIMARY_COLOR;
    color: #fff;
    text-align: center;
    padding: 5px;
    border-radius: 6px;
    font-size: 10px;
    line-height: normal;

    position: absolute;
    z-index: 1;
    top: 135%;
    left: 50%;
    transform: translateX(-50%);

    /* Fade in tooltip */
    opacity: 0;
    transition: opacity 0.3s;

    .info {
      font-weight: 700;
    }

    &::after {
      content: "";
      position: absolute;
      bottom: 100%;
      left: 50%;
      margin-left: -5px;
      border-width: 5px;
      border-style: solid;
      border-color: transparent transparent $PRIMARY_COLOR transparent;
    }
  }
}

/* Facet Tooltip text */
.facetTooltip {
  position: relative;
  display: inline-block;
  cursor: default;
  &:hover {
    .tooltiptext {
      visibility: visible;
      opacity: 1;
    }
  }
  .tooltiptext {
    visibility: hidden;
    width: 150px;
    word-wrap: break-word;
    background-color: $PRIMARY_COLOR;
    color: #fff;
    text-align: center;
    padding: 5px;
    border-radius: 6px;
    font-size: 10px;
    line-height: normal;

    position: absolute;
    z-index: 1;
    top: 135%;
    left: 50%;
    margin-left: -60px;

    /* Fade in tooltip */
    opacity: 0;
    transition: opacity 0.3s;

    .info {
      font-weight: 700;
    }

    &::after {
      content: "";
      position: absolute;
      bottom: 100%;
      left: 50%;
      margin-left: -5px;
      border-width: 5px;
      border-style: solid;
      border-color: transparent transparent $PRIMARY_COLOR transparent;
    }
  }
}

.pb-50 {
  padding-bottom: 50px;
}

.mt-negative-50 {
  margin-top: -50px;
}

/*
 offset anchor for the fixed header
 */
:target:before {
  content:"";
  display:block;
  height:$NAV_HEIGHT_WITH_SEARCHBAR;
  margin:-$NAV_HEIGHT_WITH_SEARCHBAR 0 0;
}

use {
  pointer-events: none;
}

/* For Firefox */
input[type="number"] {
  -moz-appearance:textfield;
}
/* Webkit browsers like Safari and Chrome */
input[type=number]::-webkit-inner-spin-button,
input[type=number]::-webkit-outer-spin-button {
  -webkit-appearance: none;
  margin: 0;
}

.counter-bubble-container {
  display: flex !important;
  justify-content: space-between;
  align-items: center;
  padding-right: 20px;

  .counter-bubble {
    width: 30px;
    height: 30px;
    font-size: 1rem;
    border-radius: 50%;
    background-color: $PRIMARY_COLOR;
    border: 3px solid $WHITE;
    -webkit-box-shadow: 0px 0px 8px $ALSTOM_DARK_GREY;
    -moz-box-shadow: 0px 0px 8px $ALSTOM_DARK_GREY;
    box-shadow: 0px 0px 8px $ALSTOM_DARK_GREY;
    display: flex;
    justify-content: center;
    align-items: center;
    color: $WHITE;
    font-weight: 400;
  }
}

.cookiebarcontainer {
  position: fixed;
  transition: all 0.3s ease;
  width: 100%;
  border-top: none;
  line-height: 20px;
  font-family: "OpenSans", "Poppins", sans-serif;
  letter-spacing: 1px;
  background-color: rgba(77, 43, 116, 0.9);
  color: #fff;
  font-size: 12px;
  -webkit-box-sizing: border-box;
  box-sizing: border-box;
  z-index: 2147483647;
  bottom: 0;
  padding: 10px 0;
  opacity: 0;
  transform: translateY(100%);

  &.is-visible {
    transform: translateY(0);
    opacity: 1;
  }

  .cbmessage {
    width: 100%;
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0 20px;

    p {
      margin-bottom: 0;
    }

    @include breakpoint($BREAKPOINT_MOBILE_MAX) {
      flex-direction: column;

      p {
        margin-bottom: 10px;
      }
    }
  }

  .cbhide {
    width: 100px;
    border: 1px solid $WHITE;
    font-size: 1.2em;
    font-weight: 400;
    line-height: 1em;
    color: $WHITE;
    display: block;
    cursor: pointer;
    padding: 10px;
    margin: 0;

    &:hover {
      color: $PRIMARY_COLOR !important;
    }
  }
}

/* FLEX */
.flex {
  display: flex;
}

.jc-sb {
  justify-content: space-between;
}

.jc-fe {
  justify-content: flex-end
}

.ai-c {
  align-items: center;
}

.no-margin {
  margin: 0 !important;
}

.margin-1 {
  margin: 1em;
}

.text-underline {
  text-decoration: underline !important;
}

.not-allowed {
  cursor: not-allowed !important;
}

body:not(.user-is-tabbing) button:focus,
body:not(.user-is-tabbing) input:focus,
body:not(.user-is-tabbing) select:focus,
body:not(.user-is-tabbing) textarea:focus {
  outline: none;
}

.no_hover{
  &:hover{
    text-decoration: none !important;
  }
}

.shipping-indicator {
  display: inline-block;
  width: 10px;
  height: 14px;
  border: 1px solid $PRIMARY_COLOR;
  position: relative;
  vertical-align: middle;
  &::after {
    display: block;
    position: absolute;
    content: "";
    top: 1px;
    right: 1px;
    left: 1px;
    bottom: 1px;
    background: $PRIMARY_COLOR;
  }
  &::before {
    display: block;
    position: absolute;
    content: "";
    width: 0;
    height: 0;
    border-style: solid;
    border-width: 3px 0 3px 3px;
    border-color: transparent transparent transparent $PRIMARY_COLOR;
    top: -2px;
    left: -5px;
  }
  &.shipping-50 {
    &::after {
      top: 50%;
    }
    &::before {
      top: calc(50% - 2px);
    }
  }
}

.shipping-order-lists {
  .show-detail[type="checkbox"] {
    display: none;
    visibility: hidden;
    + label {
      color: $PRIMARY_COLOR !important;
      text-decoration: underline;
      padding-left: 10px;
      position: relative;
      padding: 5px 25px;
      &::before {
        display: block;
        content: "";
        position: absolute;
        height: 2px;
        width: 14px;
        top: 50%;
        left: 4px;
        background: $PRIMARY_COLOR;
        border: none;
      }
      &::after {
        display: block;
        content: "";
        position: absolute;
        height: 14px;
        width: 2px;
        top: 8px;
        left: 10px;
        background: $PRIMARY_COLOR;
        border: none;
      }
    }
    &:not(:checked) {
      ~ .shipping-order-details {
        display: none;
      }
      + label:after {
        transform: scale(1);
        opacity: 1;
      }
    }
    &:checked {
      + label:after {
        transform: scale(0);
        opacity: 0;
      }
    }
  }



  .shipping-order-list-title {
    display: flex;
    justify-content: space-between;
    padding: 10px 20px;
    background: #eff1ff;
    span {
      padding-right: 15px;
      &.shipping-name {
        font-weight: bold;
        text-decoration: underline;
      }
    }
    .shipping-status {
      font-weight: bold;
    }
  }
  .shipping-order-list-item {
    display: flex;
    padding: 5px;
    > div {
      flex: 1;
    }
    .product {
      display: flex;
      .img-container {
        width: 18px;
        height: 18px;
        margin: 0 5px 0 15px;
        img {
          width: 100%;
        }
      }
      .name {
        font-weight: bold;
      }
      .sellerRef {
        font-weight: bold;
        color: #a4a7b3;
        margin-top: 5px;
      }
    }
  }
}

.Modal-body .LoginForm {
  max-width: 430px;
}
