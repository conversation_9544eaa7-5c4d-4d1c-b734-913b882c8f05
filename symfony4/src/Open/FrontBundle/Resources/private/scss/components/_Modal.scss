@import "../variables";

$MODAL_ZINDEX: 9999;

.Modal {
  &-overlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: #000;
    opacity: .5;
    z-index: $MODAL_ZINDEX;
  }

  &-wrapper {
    //position: relative;
  }

  &-content {
    //text-align: center;
    //text-align: center;
    position: fixed;
    //min-height: 30%;
    font-size: 1rem;
    background: #FAFAFA;
    color: $TEXT_DEFAULT;
    margin: 0 auto;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    z-index: $MODAL_ZINDEX + 1;
    border-top: $ALSTOM_BLUE 4px solid;

    @include breakpoint($BREAKPOINT_DESKTOP) {
      width: 100%;
    }
    @include breakpoint($BREAKPOINT_NOT_DESKTOP) {
      width: 95%;
      max-width: 600px;
    }

    form {
      input {
        background: transparent;
      }
    }

    .Modal-header {
      .Modal-title {
        padding: 20px 20px 5px 20px;
        font-size: 1rem;
      }
    }

    .Modal-body {
      button {
        text-transform: uppercase;
        padding: 10px 30px;

      }

      label {
        color: $BLACK;
      }

      textarea {

        width: 100%;
        border: 1px solid $ALSTOM_GREY;
        resize: none;
        font-size: 14px;
        height: 10rem;
      }
    }

    .Modal-footer {
      display: flex;
      background-color: $ALSTOM_DARK_PURPLE;

      @include breakpoint($BREAKPOINT_MOBILE_MAX) {
        flex-direction: column;
      }

      .modal-link {
        color: $WHITE;
        width: 100%;

        &:hover {
          text-decoration: none;
        }
      }

      .buttonModal, button {
        background-color: transparent;
        border: none;
        color: $WHITE;
        padding: 10px 30px;
        width: 100%;
        white-space: nowrap;
        text-align: center;

        a {
          color: $WHITE;
        }

        &:focus {
          border: none;
        }
      }
    }

    &.tiny-height {
      transform: translate(-50%, 0);
      bottom: 0;
      top: 0;
      .Modal-head {
        margin-bottom: 0;
      }
      .Modal-body {
        form {
          padding-top: 0;
        }
        label {
          margin-bottom: 0;
        }
        textarea {
          min-height: 3rem !important;
          height: 3rem !important;
        }

        button {
          margin: 0.5rem auto !important;
          padding: 1rem;
        }
      }
    }

    .Icon {
      position: absolute;
      right: 10px;
      top: 18px;
      width:12px;
      height: 12px;
    }

    .Modal-buttonArea {
      margin-top: 20px;
    }

    .Modal-button {
      background: $APPLI_GREEN;
      border: 1px solid $APPLI_GREEN;
      border-radius: 3px;
      text-transform: uppercase;
      color: $WHITE;
      padding: 10px 30px;

      &:hover {
        color: $APPLI_GREEN;
        background-color: $WHITE;
        border: 1px solid $APPLI_GREEN;
      }
    }
  }

  @include breakpoint($BREAKPOINT_DESKTOP) {
    &-content {
      width: auto;
    }
  }

}

.Modal.Modal--homepageVideo {
  .Modal-content {
    padding: 50px 0 0 0;

    @include breakpoint($BREAKPOINT_DESKTOP) {
      padding: 50px 5px 5px 5px;
    }
  }
}

.Modal.Modal--expiredOffer {
  .Modal-content {
    padding: 50px;
  }
}

.Modal.Modal--loading {
  .Modal-content {
    background: transparent;
    min-width: 0;
    text-align: center;
    border-top: none;
  }
}

.Modal.Modal--alert {

  .Modal-content {
    p {
      text-align: center;
      padding: 0 10px;
    }

    @include breakpoint($BREAKPOINT_DESKTOP) {
      max-width: 30%;
    }
  }


  .close {
    display: none;
  }
}

// Styles for the buy confirm modal
.Modal.Modal--confirmBuy,
.Modal.Modal--confirm,
.Modal.Modal--assign{
  .Modal-content {

    .Modal-header{
      min-height: 10px;


      button{
        margin-top:0;
        color: $PRIMARY_COLOR;
        opacity: 1;
        background-color: transparent;
        border:none;

        &.close {
          position: absolute;
          right: 10px;
          top: 0;
          font-size: 1.5rem;
          font-weight: 700;
          padding: 0;
        }
      }
    }

    .Modal-head {
      margin-top: 40px;
      font-size: 18px;
      padding-left: 5px;
    }

    .Modal-body {
      p{
        text-align: center;
        margin: 5%;
      }
    }

    .Modal-footer {
      text-align: center;

      .buttonModal {
        display: inline;
      }
    }

    .Modal-button {
      padding-left: 30px;
      padding-right: 30px;
    }

    .Modal-buttonArea {
      background: $APPLI_GRAY;
      padding: 20px 0;
      text-align: center;
      margin-bottom: 15px;

      .Modal-buttonTextLeft {
        font-size: 48px;
        line-height: 1.3;

         p {
           margin-bottom: 0;
           font-size: 24px
         }
      }
    }

    .Modal-productDetailLeftCol ul,
    .Modal-productDetailRightCol ul {
      list-style-type: none;
      padding-left: 0;
      font-size: 18px;

      li span {
        padding-left: 10px;
      }
    }

    .Modal-productDetailLeftCol ,
    .Modal-productDetailRightCol {
      padding-left: 5px;
    }

    @include breakpoint($BREAKPOINT_SMALL) {

      .Modal-head {
        margin-bottom: 5px;
      }

      .Modal-buttonArea {
        padding-top: 5px;
        padding-bottom: 10px;
        margin-bottom: 5px;
      }

      .Modal-productDetailLeftCol ul {
        margin-bottom: 0;
      }


    }

    @include breakpoint($BREAKPOINT_DESKTOP) {
      min-width: 35%;

      .Modal-buttonArea {
        display: flex;
        justify-content: space-around;
        padding: 20px 10%;

        .Modal-buttonTextLeft {
          line-height: 1;
        }
      }

      .Modal-productDetailLeftCol {
        float: left;
      }

      .Modal-productDetailRightCol {
        float: right;
        margin-right: 5px;
        padding-left: 0;
      }
    }

  }

}

.Modal-content {


}

.Modal--login {
  .Modal-content {
    padding: 0;
    border-top: 4px solid $ALSTOM_BLUE;

    @include breakpoint($BREAKPOINT_DESKTOP) {
      min-width: 400px;
    }

    @include breakpoint($BREAKPOINT_NOT_DESKTOP) {
      height: auto;
    }
  }
}

#js-add-reference-to-mycatalog-modal {
  .Modal-body {
    label {
      margin-top: 20px;
    }

    @include breakpoint($BREAKPOINT_MOBILE_MAX) {
      padding: 10px 20px;
    }
  }

  .Modal-footer button {
    @include breakpoint($BREAKPOINT_MOBILE_MAX) {
      border-bottom: 0.03rem solid $WHITE;
    }
  }

  .Modal-content form input {
    background: $WHITE;
    margin-top: 0;
    box-shadow: none;
  }
}

#js-save-in-wishlist-modal {
  .Modal-content {
    overflow-y: visible !important;
  }

  .Modal-body {
    padding: 10px 20px 40px;
    width: 370px;

    @include breakpoint($BREAKPOINT_NOT_DESKTOP) {
      margin: auto;
    }

    @include breakpoint($BREAKPOINT_MOBILE_MAX) {
      width: 100%;
    }
  }

  .select-wrapper {
    width: 100%;
    box-shadow: 0px 0px 4px rgba(164, 167, 179, 0.2);
  }

  .select-wishlist {
    display: flex;
    flex-direction: column;
  }

  .select-wrapper__placeholder {
    margin: 0;
    padding: 17px 33px 0 20px;
    box-shadow: none;
    border: none;
    display: block;
    width: 100%;
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;
  }

  .select-wrapper__box {
    height: auto;
  }

  .add-new-wishlist {
    input {
      margin: 0;
      background: $WHITE;
    }
  }
}

#js-add-wishlist-modal {
  .Modal-body {
    padding: 20px;
  }
}
