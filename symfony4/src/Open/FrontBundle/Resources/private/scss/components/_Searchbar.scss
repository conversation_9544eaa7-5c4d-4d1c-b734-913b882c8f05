
#Header-SearchBar {

    z-index: 110;
    margin-bottom: 10px;

    @include breakpoint($BREAKPOINT_MOBILE_MAX) {
      padding-bottom: 1.5em;
    }

    #Search-bar {
        margin: 0 auto;
        height: $NAV_SEARCH_HEIGHT;
        border: 2px $PRIMARY_COLOR solid;
        background-color: $WHITE;
        width: 100%;

        form {
          display: flex;
          justify-content: space-between;
          padding: 0;
          align-items: center;
          background-color: transparent;
          height: 100%;
          font-weight: 300;

          .select-wrapper__placeholder {
            box-shadow: none;
            height: 30px;
          }

          .select-wrapper {
            min-width: 10.6em;
            border-bottom: none;
            background-position: calc(100% - 5px) 13px;
            height: 30px;

            .js-select-placeholder {
              display: flex;
              justify-content: space-between;
              align-items: center;
              font-size: 1em;
              border: none;
              padding: 0 15px 0 5px;
              max-width: 160px;

              .arrow {
                width: 10px;
                height: 10px;
              }

              @include breakpoint($BREAKPOINT_MOBILE_MAX) {
                margin-right: 0.375em;
                max-width: 8.5em;
                overflow: hidden;
                text-overflow: ellipsis;
              }

              @include breakpoint(max-width 533px) {
                font-size: 0.55em;
              }
            }

            .js-select-wrapper-box {
              min-width: 215px;
              height: 85px;
            }

            @include breakpoint($BREAKPOINT_MOBILE_MAX) {
              padding: 0;
              min-width: 0;
              background-size: 0.625em;
              background-position: calc(100% - 5px) calc(90%);
            }

            &::before {
              width: auto;
              height: auto;
            }
          }

          :nth-child(2) {
            flex-grow: 1;
          }

          input {
            all: unset;

            &[type="text"]{
              width: 100%;
              height: 32px;
              padding-left: 1.25em;
              border: none;
              border-left: 1px solid $ALSTOM_LIGHT_GREY;
              margin: 0;

              &::-webkit-input-placeholder
              {
                color: $ALSTOM_GREY;
              }
              &::-moz-placeholder {
                color: $ALSTOM_GREY;
              }
              &:-ms-input-placeholder {
                color: $ALSTOM_GREY;
              }
              &:-moz-placeholder {
                color: $ALSTOM_GREY;
              }

              @include breakpoint(max-width 1350px) {
                width: 90%;
                overflow: hidden;
                white-space: nowrap;
                text-overflow: ellipsis;
              }

              @include breakpoint($BREAKPOINT_MOBILE_MAX) {
                padding-left: 0.5em;
                font-size: 0.8em;
                width: 100%;
              }
            }
          }

          .element {
            margin-left: 0.625em;
            margin-right: 0.625em;

            @include breakpoint($BREAKPOINT_MOBILE_MAX) {
              margin: 0;
            }
          }

          .input-text {
            margin-top: 0.5rem;
            margin-right: 0;
            align-self: flex-start;

            #autocomplete-list {
              background-color: $WHITE;
              border: 1px solid $ALSTOM_LIGHT_GREY;
              border-top: 0;
              padding: 10px 15px;
              margin-top: 6px;
              max-height: 300px;
              overflow-y: scroll;
              .option {
                cursor: pointer;
                font-size: 16px;
                margin: 4px 0;
                padding: 5px 0;

                a {
                  color: $PRIMARY_COLOR;
                }

                &:hover {
                  font-weight: bold;
                }

                &.category {
                  font-size: 12px;
                  font-weight: 600;
                  padding-left: 20px;
                }
              }

              .active {
                font-weight: bold;

                a {
                  text-decoration: underline;
                }
              }

              @include breakpoint($BREAKPOINT_NOT_DESKTOP) {
                width: 100%;
                position: absolute;
                left: 0;
                box-shadow: 0 15px 20px $ALSTOM_GREY;
                z-index: 1;
                .option {
                  font-size: 1rem;
                  margin: 1rem 0.5rem;
                }
              }
            }
          }



          .element-button {
            width: $NAV_SEARCH_HEIGHT;
            height: $NAV_SEARCH_HEIGHT;
            background-color: $PRIMARY_COLOR;
          }

          .select-department {
            .select-wrapper {
              min-width: 10rem;
            }

            @include breakpoint($BREAKPOINT_MOBILE_MAX) {
              width: 28%;
            }
          }

          button {
            display: flex;
            align-items: center;
            color: $WHITE;
            height: $NAV_SEARCH_HEIGHT;
            text-align: center;
            background-color: $PRIMARY_COLOR;
            border-radius: 0 5px 5px 0;
            border: 1px solid $PRIMARY_COLOR;
            margin-top: 0 !important;

            @include  breakpoint($BREAKPOINT_DESKTOP) {
              .Icon {
                stroke: $WHITE;
                width: 20px;
                height: 20px;
              }
            }
            @include breakpoint($BREAKPOINT_MOBILE_MAX) {
              .Icon {
                width: 1.125em;
                height: 1.125em;
                margin: 0.375em 0 0;
              }
            }
          }
        }
      }
  &.anonymous-search #Search-bar {
    .input-text {
      width: 90%;
    }

    form input[type="text"] {
      border-left: 0;
    }
  }

  .advanced-search, .custom-search {
    display: flex;
    font-size: 0.75rem;
    line-height: 0.75rem;
    margin-bottom: 3px;
    height: auto;
    min-width: 0;
    a {
      text-decoration: none;
      color: $ALSTOM_GREY;
      padding-bottom: 1px;
      border-bottom: 1px solid $ALSTOM_GREY;
    }

    @include breakpoint($BREAKPOINT_NOT_DESKTOP) {
      position: absolute;
      top: 75px;
      right: 30px;
    }
  }

  @include breakpoint($BREAKPOINT_DESKTOP) {
    top: $NAV_USER_HEIGHT;
    width: 450px;

    @include breakpoint(min-width 1350px) {
      width: 665px;
    }
  }
}

.Modal--custom-search {
  form {
    display: flex;
    flex-direction: column;
    min-height: 150px;

    .input-text {
      display: flex;
    }

    input[type="text"] {
      border: 1px solid $ALSTOM_LIGHT_GREY;
      background-color: $ALSTOM_GREY_BACKGROUND;
      height: 60px;
      width: 90%;
      padding: 0 10px;
      box-shadow: none;
      margin: 10px auto !important;
    }

    button[type="submit"] {
      padding: 15px 20px;
      margin-top: 10px;
    }
  }

  .Modal-header {
    padding: 10px 0 0 20px;
  }

  .Modal-content {
    padding-bottom: 20px;
  }
}

.sticky-comparator {
  display: none;
}

body[class*="Page--search"],
body[class*="Page--offer_detail"] {

  #Header-SearchBar #Search-bar form .select-wrapper .js-select-placeholder {
    line-height: 1.125rem;
  }

  .sticky-comparator {

    &:hover {
      text-decoration: none;
    }

    @include breakpoint($BREAKPOINT_DESKTOP) {
      display: flex;
      position: fixed;
      left: 0;
      top: 74%;
      background-color: $PRIMARY_COLOR;
      flex-direction: row;
      padding: 10px 30px;
      transform-origin: top left;
      transform: rotate(-90deg);

      .sticky-label {
        margin-right: 10px;
        color: $WHITE;
        letter-spacing: 0.1rem;
        text-transform: uppercase;
        font-size: 1rem;
        font-weight: 700;
      }

      .sticky-number {
        display: flex;
        align-items: center;
        justify-content: center;
        width: 25px;
        height: 25px;
        color: $PRIMARY_COLOR;
        background-color: $WHITE;
        border-radius: 50%;
        font-weight: 600;
        transform-origin: center center;
        transform: rotate(90deg);
      }

    }
    @include breakpoint($BREAKPOINT_NOT_DESKTOP) {
      display: flex;
      flex-direction: row;
      align-items: center;
      justify-content: space-between;
      position: fixed;
      padding: 5px 15px 5px 10px;
      bottom: 0;
      left: 0;
      background-color: $PRIMARY_COLOR;

      .sticky-label {
        background-color: transparent;
        color: $WHITE;
        letter-spacing: 0.1rem;
        font-size: 0.9rem;
        margin: 10px;
      }

      .sticky-number {
        display: flex;
        font-size: 1.3rem;
        align-items: center;
        justify-content: center;
        width: 2rem;
        height: 2rem;
        padding: 1rem;
        color: $PRIMARY_COLOR;
        background-color: $WHITE;
        border-radius: 50%;
        font-weight: 600;
      }
    }
  }
}
