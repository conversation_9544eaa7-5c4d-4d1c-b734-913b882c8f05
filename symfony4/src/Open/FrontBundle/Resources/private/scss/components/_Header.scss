@import "../variables";

.Header {
    position: fixed;
    background: $WHITE;

    top: 0;
    width: 100%;
    background: $WHITE;

    min-height: $NAV_USER_HEIGHT;
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0 10px;

    z-index: 120;


  .Header-lang {
    display: none;

    button {
      margin-top: 0;
    }

    ul {
      display: inline;
      height: 35px;
    }

    ul, li {
      padding-left: 0;
      list-style-type: none;
    }

    li {
      float: left;
    }

    button {
      color: $PRIMARY_COLOR;

      &:hover {
        color: $APPLI_DARK_YELLOW;
      }
    }

    li.is-active {
      button {
        pointer-events: none;
        //color: $APPLI_DARK_YELLOW;
        font-weight: bold;
      }
    }

    .Header-langSeparator {
      position: relative;

      span {
        position: absolute;
        width: 3px;
        height: 15px;
        background: $PRIMARY_COLOR;
        top: 10px;
        left: -3px;
      }
    }
  }


  .Header-hamburgerIcon {

    button {
      margin-top: 0;
    }

    .Header-lang {
      display: block;
    }
  }

    .Header-loginLink {
      font-size: 15px;
      font-weight: bold;

      a {
        text-transform: uppercase;
        color: $PRIMARY_COLOR;
      }
    }

    button {
      background: transparent;
      border-radius: 0;
      width: 32px;
      height: 32px;
      border: 0;
      padding: 0;

      svg {
      }
    }

    .logo--desktop {
      display: none;
      margin-right: 40px;

      img {
        max-width: 100%;
      }

      &:hover a:after {
        display: none;
      }
    }


    .Header-menu {
      position: fixed;
      overflow: auto;
      top: 0;
      bottom: 0;
      background: rgba(247, 247, 247, .97);
      width: 90%;
      max-width: 600px;
      height: 100%;
      transform: translateX(-110%);
      opacity: 0;

      -webkit-box-shadow: 4px 4px 48px -11px rgba(0,0,0,0.53);
      -moz-box-shadow: 4px 4px 48px -11px rgba(0,0,0,0.53);
      box-shadow: 4px 4px 48px -11px rgba(0,0,0,0.53);

      transition: transform 60ms ease-out;

      &.is-active {
        transform: translateX(-10px);
        opacity: 1;
        transition: transform 60ms ease-in, opacity 10ms linear;
      }
    }

    .Header-menu-search {
      display: block;
      transform: translateX(150%);
      right: 0;

      &.is-active {
        transform: translateX(0px);
      }

      @include breakpoint($BREAKPOINT_DESKTOP) {
        display: none;
      }
    }

    .Header-menuItems {
      list-style-type: none;
      padding-left: 0;
      //font-size: font-size: 32px;
      margin: 20px 40px 0 40px;

      > .Menu-item {
        border-top: 1px solid transparent;
      }


      // add border to items stqrting from the third
      > .Menu-item:nth-child(1n+4) {
        border-top: 1px solid $APPLI_DARK_YELLOW;
      }

      #cart-1 a{
        color: #770031 !important;
      }

      #cart-2 a{
        color: #6D8FA5 !important;
      }



      .Menu-item {
        display: flex;
        justify-content: space-between;
        align-items: center;
        list-style-type: none;
        font-size: 1em;
        padding: 20px 3px;

        a {
          color: $WHITE;
          text-transform: uppercase;
          text-decoration: none;
          font-weight: bold;
        }

        &:not(.has-submenu):hover {
          a {
            color: $APPLI_GREY11;

            .Icon {
              fill: $APPLI_GREY11;
            }
          }
        }

        i {
          border-color: $BLACK;
        }

        .arrow {
          border-color: $ALSTOM_GREY !important;

          &.up {
            margin-top: 7px !important;
          }
          &.bottom {
            margin-bottom: 7px !important;
          }
        }


        /*&.menu--sell {
          background: $APPLI_DARK_YELLOW;

          &:hover {
            a {
              color: $PRIMARY_COLOR_DARK;
            }
          }
        }*/

        &.menu--signin {
          display: none;
        }

        &.is-active {
          .Submenu {
            display: block;

            .Submenu-items {
              padding-left: 0;
            }

            .Menu-item {
              font-size: 12px;
              padding: 5px 10px 5px 5px;

              a {
                text-transform: none;
              }
            }
          }
        }

        &.menu--logout {
          display: block;

          span {
            display: flex;

            .Icon {
              display: inline;
              width: 24px;
              height: 24px;
              fill: $WHITE;
              margin-right: 10px;
              transform: translateY(2px);
            }
          }
        }

        .Submenu .Submenu-items .Menu-item.menu--logout {
          display: none !important;
        }

        &.has-submenu > a:before {
          position:absolute;
          display:block;
          content: "X";
          color: transparent;
          background-image: url(../../images/chevron-down.svg);
          background-repeat: no-repeat ;
          background-size: contain;
          width: 24px;
          height: 24px;
          left: -35px;
          top: 2px;
          transform: rotate(180deg);
        }

        &.has-submenu {
          &.is-active,
          &:target {
            > a:before {
              transform: rotate(0);
            }
          }
        }

        &.item--spacer {
          flex-grow: 1;
          display: none;
        }
      }

    }

    .Menu-item.has-submenu:target {
      .Submenu {
        display: block;
      }
    }


    .Submenu {
      display: none;

      .Submenu-items {
        padding-left: 0;
      }

      .Menu-item {
        font-size: 12px;
        font-weight: normal;
        padding: 5px 0;

        a {
          text-transform: none;
        }

        &.is-active {
          a {
            color: $APPLI_DARK_YELLOW;
          }
        }
      }
    }

    /*.Submenu .Menu-item .menu--sell {
      display: none
    }*/
  @include breakpoint($BREAKPOINT_DESKTOP) {

      @include breakpoint(max-width 1110px) {
        .first-level,
        .second-level {
          display: none;
        }
      }

      max-height: $NAV_USER_HEIGHT;
      display: flex;
      padding: 0;

      #js-hamburger-button {
        display: none;
      }

      .logo--home {
        display: flex;
        height: 100%;
        align-items: center;
        margin-left: 30px;
      }

      .logo--desktop {
        display: block;
        //flex-grow: 1;
      }

      &-loginLink,
      &-closeMenu {
        display: none !important;
        pointer-events: none !important;
      }



      .Header-lang {
        margin-left: 40px;
        display: block;
      }


      .Header-menu {
        position: relative;
        overflow: visible;
        width: 100%;
        float: right;
        transform: translateX(0);
        box-shadow: none;
        background: $WHITE;
        opacity: 1;
        margin-right: 20px;
      }

      .Header-menuItems {
        display: flex;
        justify-content: flex-end;
        margin: 0;
        align-items: center;
        height: $NAV_USER_HEIGHT;
        max-width: $SITE_WIDTH_WIDE;

        .Item-cart {
          .quantity {
            display: flex;
            align-items: center;
            position: absolute;
            top: 2.5em;
            right: 0;
            min-width: 1.8em;
            height: 1.8em;
            padding: 0.2rem;
            font-size: 0.9em;
            background-color: $ALSTOM_DARK_PURPLE;
            color: $WHITE;
            border-radius: 1em;

            p {
              text-align: center;
              flex-grow: 1;
              margin-bottom: 0;
            }
          }
        }

        > li {
          margin: 0 10px;
        }

        > .Menu-item {
          color: $APPLI_GREY6;
          display: flex;
          height: $NAV_USER_HEIGHT;
          align-items: center;

          &:hover {
            background: transparent !important;

            > a {
              color: $APPLI_GREY6 !important;
              transition: color 100ms linear;
            }
          }
        }



        .Menu-item {
          position: relative;
          padding: 0px;
          font-size: 12px;
          font-weight: normal;
          border-top: none !important;
          margin: 0 5px;

          a {
            display: flex;
            color: $ALSTOM_GREY;
            position: initial !important;
          }

          a:before {
            display: none;
          }

          a:after {
            display: none;
          }

          &.has-icon a {
            display: flex;
            font-weight: 300;
            align-items: center;

            span {
              display: flex;
            }
            .arrow {
              margin-top: 2px;
            }
          }

          &:hover  {
            background: transparent;
          }

          &:hover ,
          &.is-active {
            a:after {
              opacity: 1;
              transition: opacity 80ms linear;
            }
          }

          &.menu--signin {
            display: flex; // remove display none from mobile

            background: $APPLI_DARK_YELLOW;
            border-radius: 20px;
            padding: 5px 10px 5px 10px;
            height: auto;

            &:hover {
              > a {
                color: $APPLI_GREY11 !important;
              }
              background: $APPLI_DARK_YELLOW !important; // needed do not remove
            }

            a {
              color: $WHITE !important;

              &:after {
                display: none;
              }
            }

          }

          &:hover {
            .Submenu {
              display: block !important;
            }
          }

          &.menu--logout {
            //display: none;
            opacity: 0;
            pointer-events: none;
          }

          &.is-active {
            .Submenu {
              display: none;
            }
          }

          .Submenu {

            .Submenu-items {
              .Menu-item {
                padding: 10px 20px;
              }

              .Menu-item.menu--logout {
                display: block !important;
                opacity: 1 !important;
                pointer-events: all;

                .Icon {
                  display: inline;
                  width: 16px;
                  height: 16px;
                  transform: translateY(0);
                  fill: $PRIMARY_COLOR_DARK
                }

                &:hover {
                  .Icon {
                    fill: $WHITE;
                  }
                }
              }
            }
          }

          &.has-submenu {
            padding-left: 5px;
          }

          &.item--spacer {
            display: inline;
          }
        }
      }


    .Submenu {
      position: absolute;
      top: $NAV_USER_HEIGHT;
      background: $WHITE;
      left: 50%;
      transform: translateX(-50%);
      border: 1px solid $APPLI_GREY5;

      -webkit-box-shadow: 5px 5px 5px 0 rgba(0,0,0,0.08);
      -moz-box-shadow: 5px 5px 5px 0 rgba(0,0,0,0.08);
      box-shadow: 5px 5px 5px 0 rgba(0,0,0,0.08);

      ul {
        padding-left: 0;
      }


      .Menu-item {
        text-align: center;
        padding: 10px 20px;
        border-top: 1px solid $APPLI_GREY7;
        cursor: pointer;
        white-space: nowrap;
        font-size: 12px;
        font-weight: normal;


        &:hover {
          a {
            color: $WHITE !important;
            transition: none !important;
          }

          background: $PRIMARY_COLOR !important;
        }


        a {
          text-transform: uppercase;
          display: block;
          color: $APPLI_GREY6 !important;
          transition: none !important;

          &:after {
            display: none;
          }
        }

        span {
          position: relative;
        }

        .Icon {
          display: inline;
          position: absolute;
          margin: 0 5px;
          left: -5px;
          width: 16px;
          height: 16px;
          transform: translate(0);
          fill: $PRIMARY_COLOR;
        }

        &.has-icon {
          background: $APPLI_GREY10;
          a {
            color: $PRIMARY_COLOR !important;
          }
          span {
            padding-left: 20px;
          }

          &:hover {
            .Icon {
              fill: $WHITE;
            }
            a {
              color: $WHITE !important;
            }
          }
        }

        &:first-of-type {
          border-top: 0;
        }

        &.is-active {
          //border-bottom: 2px solid $SUEZ_DARK_YELLOW
          background: $APPLI_GREY11;
          a {
            color: $WHITE !important;
          }
        }


      }
    }

  }
  @include breakpoint($BREAKPOINT_NOT_DESKTOP) {
    height: 70px;
    min-height: 0;

    .logo--home {
      position: absolute;
      left: 50%;
      transform: translateX(-50%);
      margin-left: 0;

      a {
        display: flex;
        justify-content: center;

        img {
          width: 90%;
          height: 90% !important;
        }
      }
    }

    .Header-closeMenu {
      display: block;
      position: relative;
      text-align: right;
      margin: 20px;

      .Icon {
        width: 32px;
        height: 32px;
        fill: $WHITE;
      }

      .Icon-close {
        width: 1rem;
        height: 1rem;
      }

      svg {
        fill: $PRIMARY_COLOR !important;
      }
    }

    .Header-hamburgerIcon {
      width: 100%;
      margin-right: 0;
      .icon-mobile {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin: 0 1.5em;

        #js-hamburger-button {
          margin: 0;
        }

        .Icon {
          stroke: $PRIMARY_COLOR;
          width: 2em;
          height: 2em;
          margin: 0;
        }

        .all-carts {
          display: flex;
        }

        .Item-cart {
          padding: 0 3em;
        }
      }

    }

    .Header-h-line {
      display: none;
    }

    .Header-menu {

      background-color: $ALSTOM_GREY_BACKGROUND;
      width: 100%;

      .Item-cart {
        .quantity {
          display: flex;
          align-items: center;
          position: absolute;
          left: 5rem;
          height: 1rem;
          min-width: 1rem;
          padding: 0.2rem;
          font-size: 0.7rem;
          background-color: $ALSTOM_DARK_PURPLE;
          color: $WHITE;
          border-radius: 1rem;

          p {
            text-align: center;
            flex-grow: 1;
            margin-bottom: 0;
          }
        }
      }

      .Header-menuItems {
        margin: 3rem 0 0 0;
        /*padding: 0 40px 0 40px;*/
        color: $WHITE;
        .Menu-item {
          padding: 0 0.3em;

          a {
            display: flex;
            color: $WHITE;
            font-size: 1.2rem;

            span {
              display: flex;
            }

            .Icon-button-text {
              display: flex;
              justify-content: center;
              padding-left: 1rem;
              padding-right: 1rem;
              .second-level {
                font-size: 1rem;
                font-weight: bold;
                color: $ALSTOM_GREY;
                letter-spacing: 0.1rem;
              }
            }
          }
        }



        li {
          width: 100%;
          padding: 0 3em;

          &.dropdown-display {
            margin: 2rem 0;
          }

          &.hamburger-link {
            margin: 1.2rem 0;
            .link {
              color: $ALSTOM_DARK_GREY;
              font-size: 1rem;
              font-weight: 600;
              margin-left: 1rem;
            }
          }
          /*border-bottom: solid 1px $PRIMARY_COLOR;*/
        }

        .simple-links {
          margin: 2rem 0;
        }

        .h-line {
          background-color: $PRIMARY_COLOR;
          margin: 0 5%;
          width: 90%;
        }

        .Icon-flag {
          width: 2em;
          height: 2em;
          fill: $PRIMARY_COLOR !important;

          &.padding {
            padding: 0.2em;
          }
        }

        .dropdown {

          display: block;

          #Dropdown-Account {
            width: 100%;
          }

          .dropdown-display {
            font-size: 1.2rem;
            margin: 0.5rem 0;
            a {
              color: $WHITE;
              display: flex;
              align-items: center;
            }

            i.arrow {
              width: 15px;
              height: 15px;
              margin: 0 15px 0 0;
              border-color: white;
            }

            &:not(.active) {
              .cross {
                display: flex;
                justify-content: center;
                align-items: center;
                height: 100%;
                margin: 8px;
                &::after {
                  content: "";
                  height: 2px;
                  width: 20px;
                  background-color: $ALSTOM_GREY;
                }
                &::before {
                  content: "";
                  position: absolute;
                  height: 20px;
                  width: 2px;
                  background-color: $ALSTOM_GREY;
                }
              }
            }

            &.active {
              .cross {
                display: flex;
                justify-content: center;
                align-items: center;
                height: 100%;
                margin: 8px;
                &::after {
                  content: "";
                  height: 2px;
                  width: 20px;
                  background-color: $ALSTOM_GREY;
                }

              }
            }
          }

          .dropdown-content {
            position: relative;
            box-shadow: none;
            padding: 0 40px;

            &.show {
              display: block;
            }

            .arrow-up {
              display: none;
            }

            .dropdown-list {
              background-color: transparent;
              margin-left: 30px;
              /*margin-bottom: 20px;*/
              a {
                color: $ALSTOM_GREY;
                font-size: 1.2rem;


              }
            }
          }
        }
      }
    }
  }

  @media print {
    display: none;
  }
}



#Dropdown-Account {
  width: 200px;
}


.Icon {
  display: inline;
  margin: 0 5px;
}

.Icon-flag {
    width: 36px;
    height: 36px;
}

.Icon-button-text {
  display: flex;
  flex-direction: column;
  padding-left: 10px;
  padding-right: 10px;

  .first-level {
    font-size: 0.75rem;
    font-weight: 600;
    white-space: nowrap;
    letter-spacing: 1px;
  }

  .second-level {
    font-size: 1rem;
    line-height: 1rem;
    font-weight: 600;
    white-space: nowrap;
    text-transform: none;
    color: $ALSTOM_DARK_GREY;
    max-width: 200px;
    overflow: hidden;
    text-overflow: ellipsis;
  }
}

.Header-shadow {
  z-index: 150;
  position: fixed;
  width: 100%;
  top: 40px;
  height: 10px;

  -webkit-box-shadow: 0 5px 5px 0 rgba(222,222,222,1);
  -moz-box-shadow: 0 5px 5px 0 rgba(222,222,222,1);
  box-shadow: 0 5px 5px 0 rgba(222,222,222,1);

  @include breakpoint($BREAKPOINT_DESKTOP) {
    top: 75px;
  }
}

.Header-h-line {
  z-index: 150;
  position: fixed;
  top: $NAV_USER_HEIGHT;
}
