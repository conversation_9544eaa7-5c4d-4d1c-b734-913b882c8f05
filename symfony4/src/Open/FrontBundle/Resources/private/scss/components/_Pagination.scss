
.pagination {
  display: flex;
  justify-content: space-between;
  align-items: center;
  background-color: $WHITE;
  height: 60px;
  padding: 0 20px;
  margin-top: 2rem;
  color: $ALSTOM_DARK_GREY;
  margin-bottom: 142px;
  list-style-type: none;
  font-size: 0.9rem;

  .block-previous, .block-next {
    a {
      text-decoration: none;
      color: $ALSTOM_DARK_GREY;

      i {
        margin: 0 20px;
      }
    }
    &:hover {
      a {
        color: $PRIMARY_COLOR;
      }
      i {
        border-color: $PRIMARY_COLOR;
      }
    }
  }

  .page-numbers {
    width: 60px;
    height: 60px;
    display: flex !important;
    align-items: center;
    justify-content: center;
    padding-top: 2px;
    font-weight: 600;
    background-color: transparent !important;


    &:hover {
      color: $ALSTOM_BLUE;
      text-decoration: none;
    }
  }

  .block-number {
    display: flex;

    li {
      font-size: 0.9rem;
      border-top: 2px solid transparent;
      &:not(.active) {
        a {
          color: $ALSTOM_GREY;
        }
      }
      &.active {
        color: $PRIMARY_COLOR;
      }
    }
  }

  li.active {
    border-top: 2px solid $ALSTOM_BLUE;
    padding-top: 0px;
    color: $PRIMARY_COLOR;
  }

  &-mobile-view {
    display: flex;
    align-items: center;
    justify-content: center;
    color: $ALSTOM_GREY;

    .page-numbers.active {
      width: 50px;
      height: 50px;
      color: $ALSTOM_BLUE;
      border: 1px solid $ALSTOM_LIGHT_GREY;
      margin-right: 10px;
    }
  }

  .disabled {
    opacity: 0;
  }

  @include breakpoint($BREAKPOINT_NOT_DESKTOP) {
    display: flex;
    justify-content: center;
  }
}
