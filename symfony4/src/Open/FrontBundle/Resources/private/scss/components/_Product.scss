@import "../variables";

.Product {
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  width: 31%;
  min-height: 300px;
  background-color: $WHITE;
  padding: 20px 30px;
  margin: 0 10px 30px;
  color: $ALSTOM_GREY;
  border: 1px solid $ALSTOM_LIGHT_GREY;

  &-compare a {
    display: flex;
    align-items: center;
    font-weight: 300;
    color: $ALSTOM_GREY;

    .Icon {
      fill: $ALSTOM_MIDDLE_BLUE;
      margin: 0 10px 0 0;
      width: 17px;
      height: 17px;
    }
  }

  &-reference, &-incoterm {
    text-transform: uppercase;
    letter-spacing: 1px;
    font-size: 0.75rem;
    font-weight: 700;
  }



  &-image {
    width: 100%;
    height: 132px;
    margin: 15px 0;
    background-repeat: no-repeat;
    background-size: contain;
    background-position: center;
  }

  .tooltip-product-name {
    max-width: 100%;

    .tooltiptext {
      width: 100%;
      left: 27%;
      top: 40px;
    }
  }

  &-name {
    margin: 8px 0;
    overflow: hidden;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;

    a {
      color: $BLACK;
      width: 200px;
      font-size: 1.0625rem;
      font-weight: 600;

      &:hover {
        color: $ALSTOM_BLUE;
        text-decoration: none;
      }
    }
  }

  &-info {
    .details-value {
      color: $ALSTOM_DARK_GREY;
      font-weight: 600;
    }
  }

  &-price {
    font-size: 1.875rem;
    font-weight: 600;
    line-height: 32px;
    display: flex;
    flex-direction: column;
    margin: 13px 0;
    color: $ALSTOM_BLUE;

    .eur-info {
      color: $ALSTOM_DARK_GREY;
      font-size: 0.75rem;
      line-height: 0.75rem;
    }

    .sku-info{
      color: $ALSTOM_DARK_GREY;
      font-size: 0.75rem;
      line-height: 0.75rem;
      margin-top: 5px;
      text-transform: uppercase;
    }

  }

  @include breakpoint($BREAKPOINT_MOBILE_MAX) {
    margin-bottom: 0;
    padding: 15px 20px;

    .display-mobile {
      display: flex;
      align-items: center;
      justify-content: space-between;

      .Product-price {
        margin-bottom: 0;
      }
    }
  }
}
