#account > span .Icon {
  height: 15px;
  position: absolute;
  top: 33px;
  fill: $APPLI_DARK_YELLOW;

  @include breakpoint($BREAKPOINT_NOT_DESKTOP) {
    top: 117px;
    left: 185px;
  }
}

.onboarding-step {
  color: $ALSTOM_DARK_GREY;
  font-size: 0.75rem;
  text-transform: uppercase;
  letter-spacing: 1px;
  font-weight: 700;
  margin-bottom: 20px;
}

.onboarding-point {
  width: 38px;
  height: 38px;
  border-radius: 50%;
  background-color: $WHITE;
  border: 6px solid $ALSTOM_LIGHT_GREY;
  display: flex;
  align-items: center;
  justify-content: center;

  .Icon {
    width: 16px;
    height: 12px;
  }

  .icon-white {
    display: none;
  }

  .icon-grey {
    display: block;
  }
}

.background-line-progress {
  height: 6px;
  width: 100%;
  background-color: $ALSTOM_LIGHT_GREY;
  border-radius: 3px;
  margin-top: -22px;
  z-index: 2;

  @include breakpoint($BREAKPOINT_MOBILE_MAX) {
    height: 4px;
  }
}

.btn-onboarding-breadcrumb:not(:disabled):not(.disabled):active, .btn-onboarding-breadcrumb:not(:disabled):not(.disabled).active, .show > .btn-onboarding-breadcrumb.dropdown-toggle {
  .onboarding-step {
    color: $PRIMARY_COLOR;
  }

  .onboarding-point {
    width: 32px;
    height: 32px;
    margin: 3px 0;
    background-color: $PRIMARY_COLOR;
    border: 0;

    .icon-white {
      display: block;
    }

    .icon-grey {
      display: none;
    }
  }
}

.onboarding_breadcrumb .onboarding-menu-item a.btn-onboarding-breadcrumb.disabled {
  color: $BLACK;
}

.btn:not(:disabled):not(.disabled) {
  cursor: pointer;
}

.btn.disabled {
  cursor: default;
}

.btn:focus {
  box-shadow: none;
}

.desktop_breadcrumb {
  display: flex;
  align-items: center;
  height: 160px;
  background-color: $WHITE;

  .logo--home {
    margin: 0 63px 0 30px;
  }
}

.breadcrumb-part {
  width: 100%;
  margin-right: 90px;
}

.onboarding_breadcrumb {

  display: flex;
  overflow: hidden;
  justify-content: space-between;
  font-size: 18px;
}

.onboarding_breadcrumb.hide, .mobile_breadcrumb.hide {
  display: none;

  + .container .Messages .Message-item.Message--success{
    margin-top: 110px;
  }
}

.onboarding-menu-item {
  display: flex;

  @include breakpoint($BREAKPOINT_NOT_DESKTOP) {
    width: 38px;
  }

  &:first-child {
    margin-left: 35px;

    @include breakpoint($BREAKPOINT_NOT_DESKTOP) {
      margin-left: 0;
    }
  }
}

.onboarding_breadcrumb .onboarding-menu-item a {
  color: $PRIMARY_COLOR;
  text-decoration: none;
  outline: none;
  display: flex;
  flex-direction: column;
  align-items: center;
  z-index: 100;

  &:hover {
    text-decoration: none;
  }
}

.mobile_breadcrumb {
  margin: 100px auto 30px;
  width: 90%;
}

.side-menu-container {
  display: flex;
  flex-direction: column;
  align-self: flex-start;
  margin: 30px 0 60px;

  &:hover {
    a {
      max-width: 100%;
      overflow: visible;
    }
  }

  .side-menu-item {
    border-left: 4px solid $WHITE;
    text-transform: uppercase;
    font-size: 0.8125rem;
    letter-spacing: 1px;
    font-weight: 700;
    display: flex;
    align-items: center;
    height: 70px;
    position: relative;

    .Icon {
      width: 16px;
      height: 16px;
      fill: $ALSTOM_DARK_GREY;
      margin: 0 20px 0 30px;

      &.stroke {
        stroke: $ALSTOM_DARK_GREY;
      }
    }

    .item-title {
      display: flex;
      align-items: center;
    }

    .arrow {
      display: none;
    }

    .counter-bubble-container {
      position: absolute;
      top: 9px;
      left: 34px;
      transition: left 0.1s linear;
    }

    @include breakpoint($BREAKPOINT_NOT_DESKTOP) {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding-right: 20px;
    }
  }

  a {
    color: $ALSTOM_DARK_GREY;
    background-color: $WHITE;

    &:hover, &.active {
      color: $PRIMARY_COLOR;
      text-decoration: none;

      .side-menu-item {
        border-color: $PRIMARY_COLOR;
      }

      .Icon {
        fill: $PRIMARY_COLOR;

        &.stroke {
          stroke: $PRIMARY_COLOR;
        }
      }
    }

    @include breakpoint($BREAKPOINT_DESKTOP) {
      max-width: 70px;
      overflow: hidden;
    }

    @include breakpoint($BREAKPOINT_NOT_DESKTOP) {
      display: none;
      background-color: $WHITE;
      box-shadow: 0px 0px 4px rgba($ALSTOM_GREY, 0.2);

      &.active {
        display: block;

        .arrow {
          display: block;
        }
      }
    }
  }

  @include breakpoint($BREAKPOINT_DESKTOP) {
    transition: width 0.5s ease;
    width: 70px;
    margin-right: 30px;
    background-color: $WHITE;

    .item-label {
      opacity: 0;
      transition: opacity 0.1s ease-out;
      white-space: nowrap;
    }

    .side-menu-item .Icon {
      margin-left: 24px;
    }

    .side-menu-item {
      width: 200px;
      overflow: hidden;
    }

    &:hover {
      width: 270px;

      .item-label {
        opacity: 1;
        transition: opacity 0.7s ease-in;
      }

      .side-menu-item {
        width: 100%;
      }
    }
  }

  @include breakpoint($BREAKPOINT_NOT_DESKTOP) {
    width: 100%;
    height: auto;
    padding: 0 20px;
    background-color: transparent;
    box-shadow: none;
    margin: 15px 0 30px;

    &.show-all-menu {
      a {
        display: block;
      }

      .arrow {
        display: none;
      }

      .hideSideMenu {
        display: block;
      }
    }
  }
}

.breadcrumb-logout{
  margin-right:20px;
  height: 65px;

  .logout-link{
    text-decoration: none;

    &:hover{
      text-decoration: none;
    }
  }
}
