.select-search {
  display: flex;
  align-items: center;
  height: 30px;
  padding: 0px;
  background-color: $WHITE;
  min-width: 150px;
  border-radius: 5px;
  border: 1px solid $GRAY_DARKEST;

  .select-wrapper {
    padding: 5px 0 0 5px;
    white-space: nowrap;

    label {
      display: none;
    }
  }

  .js-select-placeholder {
    width: 100%;
    border-bottom: none !important;
    font-size: 14px;
    color: $GRAY_DARKEST;
    padding: 0 0 0 10px;
  }
}

.custom-search {
  min-width: 200px;
  border-color: #E4E5EC;
  margin: 0 10px;
  height: 40px;

  .select-wrapper {
    background: none;
    padding-top: 2px;
    height: 100%;
    margin-bottom: 0;

    .arrow {
      position: absolute;
      top: 0.8em;
      right: 0.9em;
      border-color: #A4A7B3;
      border-width: 0 2px 2px 0;
    }
  }

  .select-search .select-wrapper__placeholder {
    color: #A4A7B3;
    line-height: 0.875rem;
    border: 0;
    box-shadow: none;
    margin: 0;
  }

  .select-search .select-wrapper__box {
    height: auto;
    padding-bottom: 5px;
    width: auto;
    min-width: 100%;
    overflow: hidden;
  }
}
