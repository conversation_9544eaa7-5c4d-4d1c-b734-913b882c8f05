@import "../variables";

@mixin transition($transition...) {
  -webkit-transition: $transition;
  -moz-transition: $transition;
  -ms-transition: $transition;
  -o-transition: $transition;
  transition: $transition;
}

@mixin transform($transform) {
  -webkit-transform: $transform;
  -moz-transform: $transform;
  -ms-transform: $transform;
  -o-transform: $transform;
  transform: $transform;
}

.Form {
  max-width: $FORM_WIDTH;
  margin: 0 auto;
  padding: 0 20px;

  @include breakpoint($BREAKPOINT_DESKTOP) {
    padding-left: 0;
    padding-right: 0;
  }
}

form {
  width: 100%;
  margin: 0 auto;
  padding-top: 20px;
}

.form-title {
  h3 {
    color: $PRIMARY_COLOR;
    font-size: 12px;
    transform: translateY(-5px);
    font-style: italic;
  }
}

.custom-text-area-container, .custom-text-area-container > div {
  width: 100% !important;
  max-width: 100% !important;
}

.custom-text-area {
  width: 100%;
  height: 100px;
  padding: 10px;
}

button, .button {
  background-color: $PRIMARY_COLOR;
  border: 1px solid $PRIMARY_COLOR;
  color: white;
  padding: 20px 45px;
  display: block;
  text-align: center;
  font-size: 14px;
  font-weight: 700;
  letter-spacing: 1px;
  position: relative;
  margin: 0 auto;

  &.button_margin {
    margin-top: 30px;
    margin-bottom: 30px;
  }

  cursor: pointer;
  text-decoration: none;

  &:hover {
    background-color: white;
    border: 1px solid $PRIMARY_COLOR;
    color: $PRIMARY_COLOR;
    text-decoration: none;
  }
}

.button {
  text-decoration: none;
}

button[type=submit],
.button {
  text-transform: uppercase;
}

.form-title {
  width: 100%;
  margin: 10px 0 30px;
}

.form-row {
  position: relative;
  min-height: 80px;
  margin-bottom: 0;

  @include breakpoint($BREAKPOINT_NOT_DESKTOP) {
    width: 100%;
  }


  &.form--captcha {
    display: flex;
    justify-content: space-around;
    flex-direction: column;
    align-items: center;
    overflow: hidden;

    .form-captchaField {
      text-align: center;
    }
  }


  @include breakpoint($BREAKPOINT_DESKTOP) {
    width: 48%;

    &.form--captcha {
      width: 100% !important;

      .form-captchaField {
        width: 100% !important;
      }
    }
  }

  @include breakpoint($BREAKPOINT_MOBILE_MAX) {
    margin-top: 10px;
  }

}

.row-2-1 {
  width: 100%;
  display: flex;
  justify-content: space-between;

  .form-row {
    flex-basis: 66%;
    max-width: 66%;

    @include breakpoint($BREAKPOINT_NOT_DESKTOP) {
      flex-basis: 100%;
      max-width: 100%;
    }
  }

  @include breakpoint($BREAKPOINT_NOT_DESKTOP) {
    display: block;
  }
}

.row-1-1-1 {
  width: 100%;
  display: flex;
  justify-content: space-between;

  .form-row {
    flex-basis: 33%;
    max-width: 33%;
    margin-left: 30px;

    &:first-child {
      margin-left: 0;
    }

    @include breakpoint($BREAKPOINT_NOT_DESKTOP) {
      flex-basis: 100%;
      max-width: 100%;
      margin-left: 0;
    }
  }

  @include breakpoint($BREAKPOINT_NOT_DESKTOP) {
    display: block;
  }
}


.form-row,
.form--cb {

  small.error {
    color: $ERROR;
    display: block;
    margin-top: -25px;
    margin-bottom: 25px;

    ul {
      list-style-type: none;
      padding: 0;

      li {
        border: 0;
        list-style-type: none;
        background: transparent;
      }
    }
  }


}

.form--cb {
  label {
    display: block;
    margin-bottom: 0;
  }

  .form-row {
    min-height: 30px;
    margin-left: 0 !important;

    small.error {
      display: none;
    }
  }
}


.form-check-row {
  .form-row {
    width: 95%;
    margin-left: 2.5%;
    padding-left: 5%;
    padding-top: 0;
    min-height: 25px;

    label {
      line-height: 1.2em;
    }

    label:after,
    label::before {
      transform: translateY(5px) !important;
    }

  }
}

@include breakpoint($BREAKPOINT_DESKTOP) {
  .form-row {
    label {
      white-space: nowrap;
    }

    label:after,
    label::before {
      transform: translateY(-4px) !important;
    }

  }
}

.form-row > input[type="checkbox"] {
  width: 10px;
  height: 43px;
}

.Page--company_document .form-row,
.Page--site_document .form-row {
  display: inline-block;
  width: 100%;
  padding-top: 10px;
  border-top: 1px solid $APPLI_GREY7;
  margin-left: 0;
  margin-bottom: 0;
}

.Page--company_document input,
.Page--site_document input {
  + label {
    float: left;
    font-weight: 600;
    color: #000;
    position: relative;
    top: 0;
    @include breakpoint($BREAKPOINT_MOBILE_MAX) {
      width: 100%;
    }
  }
}

.form-group {
  width: 100%;
}

.select-wrapper__box::-webkit-scrollbar {
  width: 5px;
  height: 13px;
}

.select-wrapper {
  position: relative;
  width: 100%;
  cursor: pointer;
  height: 60px;
  padding: 0 0 5px 0;
  font-size: 16px;
  color: $BLACK;
  margin: 30px 0;

  background-color: $WHITE;
  background-repeat: no-repeat;

  small + label {
    margin-top: -15px;
  }

  small + label,
  + label {
    position: absolute;
    //top: 0;
    pointer-events: none;
    color: $APPLI_GREY4;
  }
}

.select-wrapper.has-text {
  .select-wrapper__placeholder {
    display: flex;
    align-items: center;
    height: 60px;
    -webkit-box-shadow: 0px 0px 4px rgba($ALSTOM_GREY, 0.2);
    -moz-box-shadow: 0px 0px 4px rgba($ALSTOM_GREY, 0.2);
    box-shadow: 0px 0px 4px rgba($ALSTOM_GREY, 0.2);
  }
}

.select-wrapper.disabled {
  background: none;

  .select-wrapper__placeholder {
    border-bottom: none;
    color: $ALSTOM_DARK_GREY;
    font-weight: normal;
    box-shadow: none;
    width: 100%;
    height: 30px;
  }

  &:hover {
    cursor: default;
  }
}

.select-wrapper.has-text select + small + label,
.select-wrapper.has-text select + label {
  color: $ALSTOM_DARK_GREY;
  font-size: 0.75rem;
  text-transform: uppercase;
  letter-spacing: 1px;
  white-space: nowrap;
  position: absolute;
  top: -30px;
  left: 0;
}

.select-wrapper.has-text select:disabled + small + label,
.select-wrapper.has-text select:disabled + label {
  color: $ALSTOM_GREY;
  top: -30px;
  left: 0;
}

.select-wrapper.disabled.has-text select + label {
  color: $ALSTOM_GREY;
}

.select-wrapper.disabled {
  cursor: text;
  display: flex;
}

.select-wrapper::before {
  width: 0px;
  height: 0px;
  position: absolute;
  content: "\f063";
  font-size: 2em;
  right: 20px;
  color: transparent;
  top: 0;
}


.select-wrapper.active::before {
  transform: rotateX(-180deg);
}

.select-wrapper__placeholder {
  display: block;
  border: 1px solid $ALSTOM_LIGHT_GREY;
  color: $ALSTOM_DARK_GREY;
  user-select: none;
  font-weight: 300;
  padding-left: 20px;
  margin: 0;
}

.select-wrapper__placeholder::before {
  position: absolute;
  top: 0;
  bottom: 0;
  left: 0;
  right: 0;
  padding: 0.2em 0.5em;
  content: attr(data-placeholder);
  visibility: hidden;
}

.select-wrapper__box {
  position: absolute;
  top: 35px;
  display: none;
  list-style-type: none;
  padding: 0;
  text-align: left;
  font-size: 1em;
  background-color: #fff;
  width: 100%;
  box-sizing: border-box;
  height: auto;
  max-height: 300px;
  overflow-y: scroll;
  z-index: 2;
  -webkit-box-shadow: 0px 0px 8px 3px #cccccc;
  -moz-box-shadow: 0px 0px 8px 3px #cccccc;
  filter: progid:DXImageTransform.Microsoft.Glow(Color=#cccccc, Strength=11);
  zoom: 1;
  box-shadow: 0px 0px 8px 3px #cccccc;
}

.select-wrapper.active .select-wrapper__box {
  display: block;
  animation: fadeInUp 500ms;
}

.select-wrapper__box__options {
  display: list-item;
  color: #000;
  padding: 0.5em 0.5em;
  user-select: none;
}

.select-wrapper__box__optionsGroup {
  display: list-item;
  background-color: $PRIMARY_COLOR;
  color: $APPLI_GREY5;
  padding: 0.5em 0.5em;
  user-select: none;
}

.select-wrapper__box__options::after {
  content: "\2713";
  font-size: 1em;
  margin-left: 5px;
  display: none;
}

.select-wrapper__box__options.selected::after {
  display: inline;
}

.select-wrapper__box__options:hover {
  background-color: $APPLI_GREY5;
}

select {
  visibility: hidden;
  opacity: 0;
  position: absolute;
  pointer-events: none;
}

small {
  position: relative;
}

/*select.error + small + label ,
input.error + small + label {
  color: $ERROR !important;
}*/

textarea, input:not([type="file"]) {
  width: 100%;
  color: $ALSTOM_DARK_GREY;
  border: 1px solid $ALSTOM_LIGHT_GREY;
  -webkit-box-shadow: 0px 0px 4px rgba($ALSTOM_GREY, 0.2);
  -moz-box-shadow: 0px 0px 4px rgba($ALSTOM_GREY, 0.2);
  box-shadow: 0px 0px 4px rgba($ALSTOM_GREY, 0.2);
  outline: none;
  line-height: 1;
  height: 60px;
  font-size: 16px;
  font-weight: 300;
  padding-left: 20px;
  margin: 30px 0;

  + label,
  + small + label {
    text-transform: uppercase;
    letter-spacing: 1px;
    color: $ALSTOM_DARK_GREY;
    white-space: nowrap;
    position: absolute;
    top: 0;
    left: 0;
  }

  &:focus {
    box-shadow: 0 1px 0 0 white;
  }

  &.error {
    border-bottom: 1px solid $ERROR;
  }

  &[readonly] {
    pointer-events: none;

    &:hover + small + label:before,
    &:hover + label:before {
      display: none !important;
    }
  }

  &::-webkit-input-placeholder {
    color: $ALSTOM_GREY;
    font-weight: 300;
    font-style: italic;
  }

  &::-moz-placeholder {
    color: $ALSTOM_GREY;
    font-weight: 300;
    font-style: italic;
  }

  &:-ms-input-placeholder {
    color: $ALSTOM_GREY;
    font-weight: 300;
    font-style: italic;
  }

  &:-moz-placeholder {
    color: $ALSTOM_GREY;
    font-weight: 300;
    font-style: italic;
  }
}

.ticket--message > label:after,
input:required + label:after,
input:required + small.error + label:after { /* IF errored a small element comes between*/
  content: "*";
  display: block;
  position: absolute;
  top: 0;
  right: -8px;
}

input:disabled {
  background-color: $WHITE;
  border-bottom: none;

  + label {
    color: $ALSTOM_GREY;
  }

  &.has-text {
    background-color: white;
    border-bottom: none;
    -webkit-text-fill-color: $ALSTOM_GREY; /* Override iOS / Android font color change */
    -webkit-opacity: 1; /* Override iOS opacity change affecting text & background color */
    color: $ALSTOM_GREY;

    + label {
      color: $ALSTOM_GREY;
    }
  }
}

input[type="file"].error + small + label {
  transform: translateY(-40px);
  color: $BLACK;
  transition: none;
  font-weight: 600;
  font-size: 12px;
}

input[type="file"].error + small {
  margin-top: 15px;
  display: block;
}

.Button--upload:hover {
  cursor: pointer;
  background: $PRIMARY_COLOR;
  color: $WHITE;
}

.is-disabled {
  .Button--upload {
    display: none;
  }

  .DocumentForm-uploadField label {
    top: 30px;
    color: $ALSTOM_GREY;
  }
}

.large-row {
  width: 100%;
  display: inline-grid;
}

.large-row .form-row:only-child {
  width: 100%;
  margin-left: 0;
}

#site_form_address {
  width: 100%;
}

#site-form-id {
  @include breakpoint($BREAKPOINT_TABLET) {
    margin-right: 50%;
  }
}

.DocumentForm-uploadField .form-row:only-child {
  width: 100%;
  margin-left: 0;
}

.add-another-collection-widget {
  float: left;
}

.flex-container {
  margin-left: 0;

  @include breakpoint($BREAKPOINT_DESKTOP) {
    margin-left: 5%;
    display: flex;
    flex-wrap: wrap;
    justify-content: space-between;
  }
}

.flex-around-container {
  width: 100%;
  flex-wrap: wrap;
  display: flex;
  justify-content: space-around;
}
