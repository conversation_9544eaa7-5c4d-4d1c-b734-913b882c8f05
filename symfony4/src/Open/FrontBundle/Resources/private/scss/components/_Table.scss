@import "../_variables";

.TableList {

  margin: auto;

  table {

    margin-top: 30px;
    width: 100%;

    tr{
      height: 50px;

      td{
        padding-left: 20px;
        word-wrap: break-word;

        a {
          color: $PRIMARY_COLOR;
        }

        .status{
          width:16px;
          height: 16px;
          float: left;
          border-radius: 50%;

          &.draft{
            background-color: $ERROR;
          }

          &.pending {
            background-color: $PENDING;
          }

          &.valid {
            background-color: $OK;
          }
        }

        .text {
          color : $PRIMARY_COLOR;
          text-align: left;
          margin-left: 25px;
        }

        .Icon {
          float: right;
          pointer-events: all;
          width: 16px;
          height: 16px;
          margin-right: 10px;
          fill: $PRIMARY_COLOR;
        }
      }
    }
    tr:nth-child(2n+1) {
      background-color: $APPLI_GREY5;
    }
    tr:nth-child(2n+0) {
      background-color: $WHITE;
    }
  }
}
