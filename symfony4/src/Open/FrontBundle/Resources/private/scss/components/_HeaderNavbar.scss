#Header-navigation {

  @include breakpoint($BREAKPOINT_DESKTOP) {
    z-index: 110;
    position: fixed;
    width: 100%;
    min-height: $NAV_LINKS_HEIGHT;
    top: $NAV_LINKS_TOP;
    background-color: $ALSTOM_GREY_BACKGROUND;
    border-top: 1px solid $ALSTOM_LIGHT_GREY;
    border-bottom: 1px solid $ALSTOM_LIGHT_GREY;
    display: flex;
    justify-content: space-between;
    align-items: center;

    ul {
      display: inline;
    }

    ul, li {
      height: $NAV_LINKS_HEIGHT;
      padding-left: 0;
      list-style-type: none;
    }

    .Header-navItems {
      display: flex;
      justify-content: space-around;
      margin: 0;
      align-items: center;
      text-align: center;
      width: 70%;
      max-width: 1000px;

      .categories {
        flex-grow: 1;

        .dropdown-content {
          position: fixed !important;
          left: 0px;
          width: 100vw;

          .arrow-up {
            display: none;
          }

          .dropdown-list {

            & > *:hover {
              background-color: $WHITE;
            }

            .Menu-item {
              max-height: 65vh;
              overflow-y: scroll;
            }

            .category {
              display: flex;
              flex-direction: column;
              flex-wrap: wrap;
              padding: 20px;
              align-content: flex-start;

              .subcategory {
                flex-shrink: 0;
                display: flex;
                flex-direction: row;
                width: 20%;

                .content {
                  display: flex;
                  flex-direction: row;
                  width: 100%;
                  .subcategory-icon {
                    width: 48px;
                    height: 48px;
                    margin-right: 20px;
                  }

                  .subcategory-content {
                    margin-bottom: 20px;
                    text-align: left;
                    padding-left: 10px;
                    width: 80%;
                  }

                  p {
                    margin-bottom: 0;
                  }

                  a {
                    color: $APPLI_GREY6;
                    margin-bottom: 0;
                    padding: 0;

                    &:hover {
                      color: $APPLI_GREY11;
                    }
                  }

                  .subcategory-title {
                    font-weight: bold;
                    color: $APPLI_GREY11;

                    &:hover {
                      color: $BLACK;
                    }
                  }
                }
              }
            }
          }

          .dropdown-cache {
            width: 100vw;
            height: 100vh;
            position: fixed;
            left: 0px;
            background: rgba($ALSTOM_DARK_PURPLE, 0.4);
          }
        }
      }

      .Nav-item {
        display: flex;
        flex-grow: 1;
        font-size: 0.9375rem;
        font-weight: 600;
        height: $NAV_LINKS_HEIGHT;
        align-items: center;
        justify-content: center;
        color: $ALSTOM_DARK_GREY;

        @include breakpoint(max-width 1050px) {
          font-size: 0.875rem;
        }

        &:not(.Header-hamburger):hover {
          text-decoration: none;
        }

        &:hover {
          color: $PRIMARY_COLOR;
          border-bottom: $PRIMARY_COLOR 2px solid;
        }

        &.active {
          border-bottom: $PRIMARY_COLOR 2px solid;
          a {
            color: $PRIMARY_COLOR;
          }
        }

        a {
          color: $BLACK;
          flex-grow: 1;
          font-size: 15px;
          font-weight: bold;
          &:hover {
            /*color: $WHITE;*/
            text-decoration: none;
          }
          &:active {
            /*color: $WHITE;*/
            background-color: $PRIMARY_COLOR;
          }
        }
      }
      .Header-hamburgerSeparator {
        flex-grow: 2.5;
      }
    }

    .right-content {
      display: flex;
      top: 0px;
      margin: 0 30px;
      align-items: center;
      width: 30%;

      .Menu-Item {
        font-size: 0.9375rem;
        font-weight: 600;
        flex-grow: 1;
        text-align: center;
        padding: 0 5px;

        @include breakpoint(max-width 1050px) {
          font-size: 0.875rem;
        }

        a:not(.color-primary) {
          color: $ALSTOM_DARK_GREY;
        }

        a:hover {
          text-decoration: none;
        }
      }

      &:hover {
        background-color: transparent;
      }

      .dropdown {
        padding-left: 15px;
        flex-grow: 1;
        .dropdown-display {
          margin-top: 15px;

          .Icon-flag {
            width: 30px;
            height: 30px;
          }
        }
      }

      .dropdown-content {
        min-width: auto;

        .has-icon {
          a {
            padding: 6px 6px;
          }
          .Icon-flag {
            width: 20px;
            height: 20px;
          }
        }
      }

      .arrow {
        display: none;
      }
    }

    #Dropdown-Hamburger {
      right: 10%;
      .arrow-up {
        margin-left: 200px;
      }
    }

  }
  @include breakpoint($BREAKPOINT_NOT_DESKTOP) {
    display: none;
  }

  .Header-hamburger {
    .Nav-item:hover {
      background-color: transparent;
    }
    .Icon {
      height: 20px;
    }
  }
}
