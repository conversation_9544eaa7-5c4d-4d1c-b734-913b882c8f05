@import "../variables";

.Product-List {
  .list-head {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
  }

  &-title {
    color: $BLACK;
    font-size: 1.8rem;
    font-weight: 600;
    margin-bottom: 35px;
  }

  .list-pagination {
    display: flex;
    align-items: center;
    margin-top: 10px;

    .Icon {
      width: 16px;
      height: 13px;
      fill: $ALSTOM_GREY;

      &.prev {
        margin-right: 30px;
      }

      &.next {
        margin-left: 30px;
      }
    }

    a, a:hover {
      pointer-events: none;
      cursor: default;
    }

    a.active {
      cursor: pointer;
      pointer-events: auto;
      .Icon {
        fill: $PRIMARY_COLOR;
      }

      &:hover {
        pointer-events: auto;
        cursor: pointer;
      }
    }

    .v-line {
      width: 1px;
      height: 30px;
      background-color: $ALSTOM_GREY;
    }
  }

  &-products {
    display: flex;

    @include breakpoint($BREAKPOINT_NOT_DESKTOP) {
      flex-direction: column;
    }

    .Product {
      max-width: 270px;
    }

    &.hide {
      display: none;
    }
  }


}
