.dropdown-display {
  color: black;
  font-size: 14px;
  border: none;
  cursor: pointer;
}

.dropdown {
  position: relative;
  display: inline-block;
}

.dropdown-content {
  display: none;
  position: absolute;
  background-color: transparent;
  min-width: 160px;
  box-shadow: 0px 8px 16px 0px rgba(0,0,0,0.2);
  z-index: 1;

  .arrow-up {
    width: 0;
    height: 0;
    border-left: 10px solid transparent;
    border-right: 10px solid transparent;
    border-bottom: 10px solid $WHITE;
  }

  .dropdown-list {
    background-color: $WHITE;
    a {
      color: black;
      padding: 12px 16px;
      text-decoration: none;
      display: block;

      .Icon {
        width: 18px;
        height: 18px;
        margin: 0;
      }
    }
    & > *:hover {
      background-color: rgba($BLACK, 0.1);
    }

  }
}

i {
  border: solid black;
  border-width: 0 3px 3px 0;
  display: inline-block;
  padding: 3px;
}

.down {
  transform: rotate(45deg);
  -webkit-transform: rotate(45deg);
}

.show {display:block;}
