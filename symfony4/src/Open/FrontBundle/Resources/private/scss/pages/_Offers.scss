body[class*=" Page--offers"] {

  .Homepage--products {
    color: $BLACK;
    display: flex;
    flex-wrap: wrap;
    flex-direction: row;
    max-width: 1200px;
    justify-content: space-between;
    padding: 60px 0;
    margin: 0 auto;

    h2 {
      margin-bottom: 25px;
      color: $BLACK;
      font-weight: normal;
      width: 100%;
      font-size: 28px;
      padding: 0 10px 0 10px;
    }

    .underlined-title {
      color: $ALSTOM_RED;
      padding-bottom: 5px;
      border-bottom: 1px solid $BLACK;
    }

    p {
      color: $BLACK;
      margin-bottom: 20px;
      padding: 0 10px 0 10px;
    }

    &--allOffers {
      width: 100%;
      display: flex;
      justify-content: space-around;
    }

    .see-all {
      color: $ALSTOM_RED;
      text-transform: uppercase;
      letter-spacing: 2px;
      border-bottom: 1px solid $ALSTOM_RED;

      &:hover {
        text-decoration: none;
      }
    }
  }

}
