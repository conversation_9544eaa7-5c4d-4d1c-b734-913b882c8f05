
@import "../variables";

.CompanyForm, .SiteForm, .UserForm {
  @include breakpoint($BREAKPOINT_DESKTOP) {
    margin-bottom: 60px;
    max-width: $FORM_LARGE_WIDTH;
  }

  .requestDocs .form-row {
    width: 100% !important;
  }

  .form-row-container {
    @include breakpoint($BREAKPOINT_DESKTOP) {
      margin-left: 5%;
    }
  }

  .Buttons {
    margin: auto;
  }

  .select-wrapper.disabled .select-wrapper__placeholder {
    border: 0;
  }

  &.user-active-background {
    background-color: $WHITE;
    margin: 30px auto 60px;
    padding-left: 30px;

    h1 {
      padding-bottom: 20px;
      margin-right: 30px;
      border-bottom: 1px solid $ALSTOM_LIGHT_GREY;
    }

    @include breakpoint($BREAKPOINT_NOT_DESKTOP) {
      margin: 0;
      padding-left: 20px;

      h1 {
        margin-right: 0;
      }
    }
  }
}

.PaymentModeForm {
  max-width: 100%;
  width: 80%;

  p {
    color: $ALSTOM_DARK_GREY;
    font-weight: 300;
    font-size: 1rem;
    margin-bottom: 30px;
  }

  .form-row-container {
    margin: 0;
  }

  .form-row {
    margin-bottom: 60px;
  }

  h2 {
    font-size: 1.2rem;
    font-weight: 600;
  }

  input:disabled.has-text + label {
    color: $ALSTOM_DARK_GREY;
    letter-spacing: 1px;
    padding-left: 40px;
  }

  [type="checkbox"]:not(:checked) + label:before, [type="checkbox"]:checked + label:before {
    background: $WHITE;
  }

  [type="checkbox"]:disabled:not(:checked) + label:before, [type="checkbox"]:disabled:checked + label:before {
    background: $ALSTOM_GREY_BACKGROUND;
  }

  .Buttons {
    width: 100%;

    .button_margin {
      margin: 0;
    }
  }

  @include breakpoint($BREAKPOINT_NOT_DESKTOP) {
    width: 100%;

    input:not([type="file"]) + label, input:not([type="file"]) + small + label {
      white-space: normal;
    }

    .Buttons {
      padding-bottom: 80px;
    }
  }
}

.ChangePasswordForm {
  button {
    margin: 30px 0 60px;
  }
}

body[class*=" Page--company"],
body[class*=" Page--profile_change-password"] {
  background-color: $ALSTOM_GREY_BACKGROUND;

  .flex-container {
    margin-left: 0;
  }

  h1 {
    margin-bottom: 35px;
  }

  h4 {
    color: $BLACK;
    margin-bottom: 0;
  }

  .Buttons-group {
    display: flex;
    justify-content: space-between;

    .button {
      margin: 30px 0;
      border: 1px solid $PRIMARY_COLOR;
    }

    .grey-button {
      background-color: $ALSTOM_GREY;
      color: $WHITE;
      border-color: $ALSTOM_GREY;

      &:hover {
        background-color: $WHITE;
        color: $ALSTOM_GREY;
      }
    }

    @include breakpoint($BREAKPOINT_NOT_DESKTOP) {
      .button, .button_margin {
        width: auto;
        padding: 16px 25px;
        display: flex;
        align-items: center;
      }

      button {
        width: 100%;
      }
    }
  }

  @include breakpoint($BREAKPOINT_DESKTOP) {
    .side-container {
      display: flex;

      .Page-inner {
        width: 80%;
        margin: 0 auto 60px;
      }
    }
  }

  @include breakpoint($BREAKPOINT_NOT_DESKTOP) {
    .mobile-padding {
      padding: 0 20px;
    }
  }
}

body[class*=" Page--company_info_edit"] {
  .CompanyForm {
    h2 {
    }
  }
}

body[class*=" Page--company_info"] {
  .CompanyForm {
    [type="checkbox"]:not(:checked) + label, [type="checkbox"]:checked + label {
      margin-bottom: 30px;

      @include breakpoint($BREAKPOINT_NOT_DESKTOP) {
        white-space: pre-wrap;
      }
    }

    [type="checkbox"]:not(:checked) + label:before, [type="checkbox"]:checked + label:before {
      background: $WHITE;
    }

    .select-wrapper {
      height: auto;
    }
  }

  .ui-autocomplete {
    max-height: 200px;
    overflow-y: scroll;
    overflow-x: hidden;
  }

  .form-check-row .form-row {
    width: 100%;
    margin: 0;
    padding: 0;

    label {
      text-transform: initial;
      font-weight: 600;
      font-size: 0.875rem;
    }
  }

  .form-check-row.optional .form-row {
      label {
        color: $ALSTOM_GREY;
      }
  }

  .desktop_breadcrumb {
    height: auto;
    .breadcrumb-logout {
      height: auto;
      a {
        display: flex;
        div {
          margin-bottom: 0;
          svg {
            height: 14px;
            width: 14px;
          }
        }
      }
    }
  }
}

body[class*=" Page--company_info_edit"] {

  .CompanyContactForm .form-control-label::before {
    content: attr(title);
    position: absolute;
    right: 0;
    top: -15px;
    transform: translateX(130%);
    //display: none;
    opacity: 0;
    border: 2px solid black;
    background: white;
    border-radius: 5px;
    padding: 10px;
    white-space: nowrap;
    z-index: 100;

    transition: opacity 60ms;
    transition-delay: 0ms;

    box-shadow: 4px 4px 10px 0 rgba(161,158,161,0.6);
  }

  .CompanyIdentification input:hover + small + .form-control-label::before ,
  .CompanyIdentification input:hover + .form-control-label::before {
    opacity: 1;
    transition-delay: 900ms;
  }
}


body[class*=" Page--company_profile"] {

  .CompanyProfileForm {
    @include breakpoint($BREAKPOINT_DESKTOP) {
      max-width: $FORM_LARGE_WIDTH;
      margin-bottom: 60px;
    }

    button {
      margin: 0;

      @include breakpoint($BREAKPOINT_NOT_DESKTOP) {
        margin: 0 0 60px;
      }
    }

    @include breakpoint($BREAKPOINT_NOT_DESKTOP) {
      .padding-lang {
        padding-top: 1px;
      }
    }
  }

  .CompanyProfilePasswordChange {
    margin-top: 20px;
    min-height: 50px;

    .CompanyProfilePasswordLink {
      font-weight: bold;
      color: $PRIMARY_COLOR;
      text-decoration: underline;
    }

    @include breakpoint($BREAKPOINT_SMALLER_MOBILE) {
      margin-left: 0;
      text-align: center;
    }
  }
}

body[class*=" Page--company_users"] {
  h1 {
    margin-bottom: 0;
  }

  .TableList .sites-list .site-row .site-label {
    border: 0;
    padding-bottom: 30px;
  }
}


body[class*=" Page--company_payment_modes"] {
  .form-row {
    min-height: 0;

    &:first-child {
      margin-top: 20px;
    }
  }

  .form-control-label {
    padding: 0 30px;
  }

  .invisible-checkboxes {
    display: none;
  }
}
