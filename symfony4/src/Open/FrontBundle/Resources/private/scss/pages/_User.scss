@import "../_variables";

body[class*=" Page--company_users"]{

  .Page-inner {
    margin: 0 auto;
    padding: 0;
    text-align: center;

    @include breakpoint($BREAKPOINT_DESKTOP) {
      max-width: $FORM_LARGE_WIDTH;
    }

    table {
      margin: 20px auto 30px;
      text-transform: capitalize;

      @include breakpoint($BREAKPOINT_DESKTOP) {
        width: $LIST_WIDTH;
      }
    }

    .UserList li{
      list-style-type: none;
      border-bottom: 1px solid;
      padding: 5px 0px 5px 0px;
    }

    .UserList li .Icon {
      float: right;
      pointer-events: all;
      width: 16px;
      height: 16px;
      margin-right: 10px;
    }

    .UserForm-sites {
      position: relative;
      height: 70%;
      float: left;
      width: 40%;
    }

    .TableList {
      text-align: left;
      margin-bottom: 0;

      th {
        padding-left: 20px;
        color: $ALSTOM_DARK_GREY;
      }

      table tr td .text {
        margin-left: 0;
      }

      @include breakpoint($BREAKPOINT_DESKTOP) {
        max-width: 100%;
      }

      @include breakpoint($BREAKPOINT_NOT_DESKTOP) {
        margin-top: 0;
      }
    }

    @include breakpoint($BREAKPOINT_NOT_DESKTOP) {
      .TableList .sites-list .site-row {
        padding: 0 30px;

        .site-label {
          padding: 20px 0;
          justify-content: space-between;
          h1 {
            margin-left: 0;
          }
        }
      }
    }

    .action-icons {
      a.active .Icon.stroke {
        stroke: $PRIMARY_COLOR;
      }
    }

    [type="checkbox"]:not(:checked) + label:before, [type="checkbox"]:checked + label:before {
      top: 1px;
    }

    .add-user {
      width: 100%;
      height: 100px;
      color: $ALSTOM_GREY;
      line-height: 25px;
      display: flex;
      flex-direction: column;
      margin: 30px 0;
      justify-content: center;
      text-align: center;
      border: 2px dashed $ALSTOM_GREY;
      text-transform: uppercase;
      font-weight: 700;
      letter-spacing: 1px;
      font-size: 0.83rem;

      .plus-symbol {
        font-size: 25px;
        margin-bottom: 5px;
        color: $ALSTOM_DARK_GREY;
        font-weight: 300;
      }

      &:hover {
        text-decoration: none;
        background-color: $WHITE;
      }

      @include breakpoint($BREAKPOINT_NOT_DESKTOP) {
        height: 131px;
      }
    }

    .user-definition {
      text-align: left;
      color: $ALSTOM_DARK_GREY;
      line-height: 30px;
      font-size: 1rem;
      font-weight: 300;

      .role {
        font-weight: 700;
      }

      @include breakpoint($BREAKPOINT_NOT_DESKTOP) {
        margin-bottom: 60px;
      }
    }

    .button {
      width: auto;
      display: inline-block;
      margin-bottom: 60px;
    }

    .sites-list .edit-user {
      display: none;
      margin-top: -30px;

      &.show {
        display: block;
      }

      .site-row {
        padding-top: 15px;
        background-color: $ALSTOM_LIGHT_GREY;

        @include breakpoint($BREAKPOINT_NOT_DESKTOP) {
          padding: 0;

          .select-wrapper__placeholder {
            margin-top: 60px;
          }
        }
      }

      .Page-inner {
        width: 100%;
        padding: 0;
      }

      .UserForm,
      .Page-inner {
        max-width: 100%;
        margin-bottom: 0;
      }

      .UserForm-sites {
        float: none;
        label {
          margin: 30px 0;
          text-align: left;
        }
      }

      button.button_margin {
        border: 1px solid $PRIMARY_COLOR;
        background-color: $PRIMARY_COLOR;
        color: $WHITE;
        padding: 0 25px;
        margin: 30px 0;
        height: 60px;

        &:hover {
          color: $PRIMARY_COLOR;
          background-color: $WHITE;
        }
      }

      @include breakpoint($BREAKPOINT_DESKTOP) {
        .flex-container {
          justify-content: flex-start;
        }

        .form-row {
          width: 35%;
          margin-right: 30px;
        }
      }
    }
  }

  .Page-inner .UserForm-sites {
    margin: 20px 0 0;
    width: 100%;

    .UserForm-sitesError {
      margin-top: 30px;
      text-align: left;
    }

    @include breakpoint($BREAKPOINT_NOT_DESKTOP) {
      margin: 0;
    }

    label {
      display: none;
    }
  }

  .Page-inner .sites-list .edit-user .UserForm-checkboxes {
    display: flex;
    justify-content: flex-start;
    flex-wrap: wrap;

    input[type=checkbox] + label {
      display: block;
      padding-left: 40px;
      margin: 0;
    }

    input[type=checkbox] + label:before {
      transform: translateY(7px) !important;
      background-color: $WHITE;
    }

    input[type=checkbox] + label:after {
      transform: translateY(6px) !important;
    }

    .form-row {
      width: auto;
      margin-right: 30px;

      @include breakpoint($BREAKPOINT_NOT_DESKTOP) {
        width: 50%;
        margin-right: 0;
      }
    }
  }
}

body[class*=" Page--company_user_"] {
  .form-title {
    border-bottom: 1px solid $ALSTOM_LIGHT_GREY;

    h1 {
      margin-bottom: 20px;
    }
  }

  .form-row .select-wrapper .select-wrapper__box {
    height: auto;
  }

  .UserForm-sites {
    margin: 20px 0 0;
    width: 100%;

    small.checkbox-errors {
      margin: 0;
    }

    label {
      display: none;
    }
  }

  .UserForm-checkboxes {
    margin-top: 0px;
    display: flex;
    justify-content: flex-start;
    flex-wrap: wrap;

    input[type=checkbox] + label {
      display: block;
      padding-left: 40px;
      white-space: initial;
    }

    input[type=checkbox] + label:before {
      transform: translateY(7px) !important;
      background-color: $WHITE;
    }

    input[type=checkbox] + label:after {
      transform: translateY(6px) !important;
    }

    .form-row {
      width: auto;
      margin-right: 30px;

      @include breakpoint($BREAKPOINT_NOT_DESKTOP) {
        width: 50%;
        margin-right: 0;
      }
    }
  }

  [type="checkbox"]:not(:checked) + label:before, [type="checkbox"]:checked + label:before {
    top: 1px;
  }

  @include breakpoint($BREAKPOINT_NOT_DESKTOP) {
    .select-wrapper__placeholder {
      margin-top: 60px;
    }
  }

  .user-actions {
    display: flex;
    justify-content: flex-start;
    align-items: flex-end;
    padding-bottom: 30px;

    button {
      margin-bottom: 0;

      @include breakpoint($BREAKPOINT_NOT_DESKTOP) {
        padding: 20px 60px !important;
      }
    }

    a.cancel-edit {
      color: $ALSTOM_GREY;
      font-size: 0.8125rem;
      line-height: 0.8125rem;
      font-weight: 700;
      letter-spacing: 1px;
      margin-left: 10px;

      &:hover {
        color: $PRIMARY_COLOR;
        cursor: pointer;
      }
    }
  }

  .user-definition {
    color: $ALSTOM_DARK_GREY;
    line-height: 20px;
    font-size: 0.8125rem;
    font-weight: 300;

    .role {
      font-weight: 700;
    }
  }

}

