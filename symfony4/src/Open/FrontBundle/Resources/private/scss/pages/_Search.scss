body[class*='Page--search'] {
  main.container {
    max-width: 100%;
    background-color: $ALSTOM_GREY_BACKGROUND;

    @include breakpoint($BREAKPOINT_NOT_DESKTOP) {
      padding-top: 70px;
    }

    .Messages {
      margin: 0 14%;
    }
  }
}

#Search-Content {
  display: flex;
  max-width: 1200px;
  min-height: 600px;
  margin: 0 auto;

  form {
    display: flex;

    @include breakpoint($BREAKPOINT_MOBILE_MAX) {
      flex-direction: column;
    }
  }

  .toolbar-list,
  .toolbar-list-mobile {
    display: flex;
    background-color: $WHITE;
    height: 60px;
    padding: 5px 20px;
    align-items: center;
    justify-content: space-between;
    font-size: 0.875rem;
    color: $ALSTOM_GREY;

    .list-nb-offers {
      p {
        margin-bottom: 0 !important;
        white-space: nowrap;

        @include breakpoint($BREAKPOINT_MOBILE_MAX) {
          white-space: initial;
          padding-bottom: 30px;
        }
      }

      a {
        color: $PRIMARY_COLOR;

        &.active {
          text-decoration: underline;
        }
      }
    }

    .list-sort {
      display: flex;
      margin: 5px;
      align-items: center;

      .sort-element {
        margin: 0 10px;
      }

      .sort-label {
        margin-right: 0;
        margin-bottom: 0;
        white-space: nowrap;
      }

      form {
        padding: 0;
      }

      .offers-aspect {
        display: flex;
        height: 15px;
        margin: 0;

        .display-choice .Icon {
          height: 15px;
          width: 15px;
          margin: 0 0 0 25px;
          fill: $ALSTOM_GREY;
        }

        .display-choice.active .Icon {
          fill: $ALSTOM_BLUE;
        }
      }

      .select-search {
        min-width: 200px;
        border-color: $ALSTOM_LIGHT_GREY;
        margin-right: 8px;
        height: 40px;

        .select-wrapper {
          background: none;
          padding-top: 2px;
          height: 100%;
          margin-bottom: 0;

          &__placeholder {
            color: $ALSTOM_GREY;
            line-height: 0.875rem;
            border: 0;
            box-shadow: none;
            margin: 0;
            height: 35px;
          }

          &__box {
            height: auto;
            padding-bottom: 5px;
            width: auto;
            min-width: 100%;
            overflow: hidden;
          }

          .arrow {
            position: absolute;
            top: 0.8em;
            right: 0.9em;
            border-color: $ALSTOM_GREY;
            border-width: 0 2px 2px 0;
          }
        }
      }
    }
  }

  .help-button {
    margin-top: 30px;
    width: 300px;

    @include breakpoint($BREAKPOINT_MOBILE_MAX) {
      margin: auto;
    }
  }

  .toolbar-list-mobile {
    display: none;
  }

  .compatible-product {
    display: block;
    width: 100%;
    margin-bottom: 25px;

    a {
      color: $PRIMARY_COLOR;
      font-size: 1.125rem;
    }

    @include breakpoint($BREAKPOINT_NOT_DESKTOP) {
      text-align: center;
      margin: 20px auto;
    }
  }

  #Search-List {
    flex-grow: 1;
    margin-left: 30px;

    .selected-filters {
      padding: 0;
      display: flex;
      flex-wrap: wrap;
      margin-bottom: 0;

      li {
        list-style: none;
        background-color: $WHITE;
        display: flex;
        padding: 10px 15px;
        justify-content: space-between;
        align-items: center;
        margin: 0 10px 10px 0;
        color: $ALSTOM_DARK_GREY;

        a {
          height: 14px;
          margin: -4px 0 0 10px;
        }

        .Icon {
          fill: $PRIMARY_COLOR;
          width: 14px;
          height: 14px;
          margin: 0;
        }
      }
    }

    .pagination > a {
      display: none;
    }

    .offers-list {
      display: flex;
      flex-wrap: wrap;
      flex-direction: row;
      max-width: 900px;
      padding: 20px 0 0;

      @include breakpoint($BREAKPOINT_MOBILE_MAX) {
        padding: 0 20px 10px;
      }

      .Product {
        margin: 0 3.5% 30px 0;

        &:nth-child(3n+3) {
          margin-right: 0px;
        }

        @include breakpoint($BREAKPOINT_MOBILE_MAX) {
          margin: 0 0 7px;
        }
      }

      &.text-display {
        .Product {
          margin-bottom: 5px;
          &-compare {
            display: none;
          }

          &-image {
            width: 50px;
            height: 50px;
            margin: 0 20px 0 0;
          }
        }

        @include breakpoint($BREAKPOINT_MOBILE_MAX) {
          .Product {
            position: relative;
            min-height: 100px;

            &-image {
              position: absolute;
              left: 20px;
              top: 15px;
            }

            &-reference,
            &-name,
            &-info {
              padding-left: 70px;
            }

            &-price {
              margin-top: 0;
            }

            .display-mobile {
              margin-top: 15px;
            }
          }
        }

        @include breakpoint($BREAKPOINT_TABLET) {
          .Product {
            width: 100%;
            flex-direction: row;
            align-items: center;
            min-height: 0;
            margin-right: 0;
            padding: 5px 15px;

            .tooltip {
              vertical-align: bottom;
            }

            &-body {
              flex: 1;
              min-height: 90px;
              display: flex;
              flex-direction: row;
              flex-wrap: nowrap;
              justify-content: stretch;
              align-items: center;
              max-width: 100%;
            }

            &-name {
              width: auto;
              min-width: 100%;
              max-width: 100%;
              overflow: hidden;
              //white-space: nowrap;
              text-overflow: ellipsis;
              margin: 0;
            }

            .overflow {
              width: auto;
              min-width: 100%;
              max-width: 100%;
              overflow: hidden;
              //white-space: nowrap;
              text-overflow: ellipsis;
              margin: 0;
            }

            .tooltip-product-name {
              .tooltiptext {
                width: auto;
                top: 35px;
                left: 20%;
              }
            }

            &-info {
              width: calc(50% - 220px);
              flex: 1;
              padding-right: 10px;
              .details:first-child {
                margin-bottom: 10px;
              }
            }

            &-incoterm {
              text-align: right;
              margin: 0;
            }

            &-price {
              display: flex;
              align-items: flex-end;
              margin: 0;
            }
          }
        }
      }
    }

    .pagination {
      display: flex;
      justify-content: space-between;
      align-items: center;
      background-color: $WHITE;
      height: 60px;
      padding: 0 20px;
      color: $ALSTOM_DARK_GREY;
      margin-bottom: 142px;

      .pagination-pages {
        list-style: none;
        margin-bottom: 0;
        padding: 0;

        .page-numbers {
          width: 60px;
          height: 60px;
          display: flex;
          align-items: center;
          justify-content: center;
          padding-top: 2px;

          a {
            color: $ALSTOM_GREY;

            &:hover {
              color: $ALSTOM_BLUE;
              text-decoration: none;
            }
          }
        }

        li.active {
          border-top: 2px solid $ALSTOM_BLUE;
          padding-top: 0px;

          a {
            color: $ALSTOM_BLUE;
          }
        }
      }

      &-desktop-view {
        display: flex;
      }

      &-mobile-view {
        display: none;
      }

      &-nav a{
        display: flex;
        align-items: center;
        color: $ALSTOM_DARK_GREY;

        .left {
          margin-right: 16px;
        }

        .right {
          margin-left: 16px;
        }

        &:hover {
          color: $ALSTOM_BLUE;
          text-decoration: none;

          .arrow {
            border-color: $ALSTOM_BLUE;
          }
        }
      }

      .nav-hidden {
        visibility: hidden;
      }
    }
  }

  @include breakpoint($BREAKPOINT_MOBILE_MAX) {
      display: block;
      min-height: 800px;

      .toolbar-list-mobile {
        display: flex;
        border-top: 1px solid $ALSTOM_LIGHT_GREY;
        margin-bottom: 0;

        .show-filter-mobile {
          color: $ALSTOM_BLUE;
          text-transform: uppercase;
          display: flex;
          align-items: center;
          font-size: 0.8125rem;
          font-weight: 700;
          letter-spacing: 1px;

          .arrow {
            border-color: $ALSTOM_BLUE;
            margin-left: 10px;
          }
        }
      }

      .Sidebar-Criteria {
        display: none;
        width: 100%;
        padding: 0 20px;
        margin-top: 20px;

        h2 {
          margin-top: 5px;
        }

        .h-line {
          margin-bottom: 8px;
        }

        .sidebar-filters {
          margin: 10px 5px 20px;
        }

        .dropdown-mobile:not(.show) {
          display: none;
        }

        .dropdown-mobile.show {
          display: block;
        }

        &.show-mobile {
          display: block;
        }
      }

      #Search-List {
        width: 100%;
        margin: 0;
        padding: 0 0 40px;

        .Product {
          width: 100%;
          margin: 0;
        }

        .toolbar-list {
          background-color: inherit;
          align-items: flex-start;
          height: auto;
          padding: 10px 20px 0;

          .list-sort {
            display: none;
          }
        }

        .pagination {
          justify-content: space-around;
          margin: 76px 0 0;
          &-nav div {
            display: none;
          }

          &-desktop-view {
            display: none;
          }

          &-mobile-view {
            display: flex;
            align-items: center;
            color: $ALSTOM_GREY;

            .page-numbers.active {
              width: 50px;
              height: 50px;
              color: $ALSTOM_BLUE;
              border: 1px solid $ALSTOM_LIGHT_GREY;
              margin-right: 10px;
            }
          }
        }
      }
    }

  @include breakpoint($BREAKPOINT_SMALLER_MOBILE) {
    #Search-List .toolbar-list {
      flex-direction: column;

      .sort-label {
        margin-left: 0;
      }

      .list-sort .sort-element {
        margin: 0 10px 0 0;
      }

      .list-sort .offers-aspect .display-choice .Icon {
        margin-left: 10px;
      }
    }
  }
}

.Sidebar-Criteria {
  width: 270px;
  display: block;

  #criteria-block {
    display: none;

    &.show {
      display: block;
    }
  }

  .sidebar-block {
    color: $ALSTOM_GREY;
    background-color: $WHITE;
    border: 1px solid $ALSTOM_LIGHT_GREY;
    box-shadow: 1px 1px 8px rgba(0, 0, 0, 0.1);
    margin-bottom: 30px;
    padding: 32px 30px;

    h2 {
      font-family: 'OpenSans', 'Poppins', sans-serif;
      color: $ALSTOM_DARK_GREY;
      font-size: 0.75rem;
      font-weight: 700;
      letter-spacing: 0.4px;
      line-height: 0.8125rem;
      text-transform: uppercase;
      margin-bottom: 0;
    }

    .department-link i {
      border: none;
      font-style: normal;
    }

    .department-link-bold i {
      border: none;
      font-style: normal;
      font-weight: bold;
      color: $ALSTOM_DARK_GREY;
    }

    &-title {
      display: flex;
      justify-content: space-between;
      align-items: center;
    }

    .Icon {
      width: 17px;
      height: 17px;
      margin-right: 0;
    }

    .search-icon {
      height: 16px;
    }

    .arrow {
      border-color: $ALSTOM_DARK_GREY;
      border-width: 0 2px 2px 0;
    }

    .criteria-block {
      margin-bottom: 20px;

      .show-more {
        margin: 10px 30px;
        font-weight: 600;
        color: $ALSTOM_DARK_GREY;

        &:hover {
          text-decoration: underline;
        }
      }

      .criteria-control {

        overflow-y: hidden;

        label {
          font-size: 0.875rem;
          font-weight: normal;
          color: $ALSTOM_DARK_GREY;
          text-overflow: ellipsis;
          max-width: 100%;
          overflow: hidden;
          word-break: break-word;
        }

        &.partial {
          .criteria-checkbox {
            max-height: 190px;
          }
        }

        .select-search {
          border-color: $ALSTOM_LIGHT_GREY;
          border-radius: 0;
          background-color: $ALSTOM_GREY_BACKGROUND;
          padding: 10px;
          height: 35px;

          .select-wrapper {
            padding: 0;
            background: none;

            .arrow {
              position: absolute;
              top: 0.3em;
              right: 0.3em;
              border-color: $ALSTOM_GREY;
            }
          }

          .js-select-placeholder {
            color: $ALSTOM_GREY;
            padding: 0;
          }
        }
      }
    }

    .criteria-checkbox {
      input[type="checkbox"]:focus + label {
        transform: translateY(0);
      }

      label {
        color: $ALSTOM_GREY;
        line-height: 1.4rem;
        padding: 0 0 0 30px;
        font-weight: normal;
        margin-bottom: 0;

        &:last-child {
          margin-bottom: 0;
        }
      }

      div[id*='form_'], div[id*='facetFilters_'], div[id*='commonFacetFilters_'], div[id*='specificFacetFilters_'], div.facet {
        display: flex;
        flex-direction: column;
      }
    }
  }

  .department-list, .criteria-list {
    display: none;
    color: $ALSTOM_GREY;

    a {
      color: $ALSTOM_GREY;
      line-height: 25px;

      &:hover {
        color: $ALSTOM_BLUE;
        text-decoration: none;
      }
    }

    .see-more-dept {
      color: $ALSTOM_DARK_GREY;
      text-decoration: underline;

      &.hide {
        display: none;
      }

      &:hover {
        color: $ALSTOM_DARK_GREY;
      }
    }

    .h-line {
      margin-bottom: 10px;
    }

    &.show {
      display: block;
      margin-top: 32px;
    }
  }

  .more-depts {
    display: none;

    &.show {
      display: block;
    }
  }
}

#Advanced-Search-mobile {

  margin-top: -70px;
  position: relative;
  z-index: 200;
  background-color: $ALSTOM_GREY_BACKGROUND;

  .sticky-comparator {
    display: none;
  }

  .top-search-input {
    background-color: $WHITE;
    border-bottom: 1px solid $ALSTOM_LIGHT_GREY;
    display: flex;
    align-items: center;
    justify-content: space-between;
    height: 70px;
    padding: 0 20px 0 30px;

    .arrow {
      border-color: $ALSTOM_BLUE;
    }

    #Header-SearchBar {
      padding: 0;
      margin: 0;
      width: 90%;

      #Search-bar {
        border: 0;

        .hidden-element {
          display: none;
        }

        form input[type="text"] {
          font-size: 1rem;
          font-weight: 300;
          padding-bottom: 0;
          border: 0;
        }

        form .element-button {
          background-color: $WHITE;
          display: flex;
          justify-content: flex-end;
        }

        form button {
          border: 0;
          background-color: $WHITE;
          padding: 0;
          .Icon {
            width: 20px;
            height: 20px;
            stroke: $ALSTOM_BLUE;
          }
        }
      }
    }
  }

  h4 {
    font-size: 0.875rem;
    font-weight: 400;
    letter-spacing: 0.5px;
    color: $ALSTOM_GREY;
    width: 270px;
    margin: 35px auto 3px;
    opacity: 0.8;
  }

  .Sidebar-Criteria {
    margin: auto;
    width: 270px;
    padding: 0;
    display: block;
  }

  .button-submit {
    background-color: $ALSTOM_BLUE;
    width: 270px;
    margin: 0 auto 50px;
    padding: 20px;
    height: auto;

    &:hover {
      background-color: $WHITE;
    }
  }

  #Search-List,
  .toolbar-list-mobile,
  .pagination {
    display: none;
  }

  @include breakpoint($BREAKPOINT_TABLET) {
    #Search-Content form {
      margin-left: 80px;
    }

    .Sidebar-Criteria {
      margin: 0 30px 0 0;
    }

    h4 {
      margin-left: 80px;
    }
  }
}
