@import "../variables";

body[class*="Page--merchantOrder_dispute"] {
  background-color: $ALSTOM_GREY_BACKGROUND;

  textarea.full_width {
    width: 100%;
    font-size: 1rem;
    min-height: 135px;
    color: $ALSTOM_DARK_GREY;
  }

  @include breakpoint($BREAKPOINT_DESKTOP) {
    form {
        padding-top: 30px;
    }

    .side-menu-container:hover {
      width: 370px;
    }

    .side-container {
        display: flex;
    }
  }

  .subject-create {
    color: $ALSTOM_DARK_GREY;
    display: flex;
    font-size: 0.8125rem;
    margin-bottom: 20px;
  }

  .part-title {
    color: $ALSTOM_DARK_GREY;
    text-transform: uppercase;
    font-size: 0.75rem;
    letter-spacing: 1px;
    font-weight: 700;
    margin-right: 5px;

    @include breakpoint($BREAKPOINT_NOT_DESKTOP) {
      padding-left: 20px;
    }
  }

  .dispute-product-table {
    background-color: $WHITE;
    width: 100%;
    margin-top: 10px;

    th:first-child {
      padding-left: 30px
    }

    th {
      font-size: 0.9375rem;
      letter-spacing: 1px;
      color: $ALSTOM_GREY;
    }

    td {
      font-size: 1rem;
      color: $ALSTOM_DARK_GREY;

      p {
        margin-bottom: 0;
      }
    }

    th, td {
      padding: 30px 30px 30px 0;
      font-weight: 300;
      border-bottom: 1px solid $ALSTOM_LIGHT_GREY;
    }

    .dispute-checkbox {
      position: relative;

      [type="checkbox"]:not(:checked), [type="checkbox"]:checked {
        left: 0;
        top: 0;
        margin: 0;
        height: 100%;
        border: none;
        box-shadow: none;
        z-index: 10;
        opacity: 0;
      }

      [type="checkbox"]:not(:checked) + label, [type="checkbox"]:checked + label {
        padding-top: 5px;
      }
    }

    .all-checkboxes {
        [type="checkbox"]:not(:checked) + label, [type="checkbox"]:checked + label {
          padding-top: 0px;
          margin-bottom: 0;
          font-weight: 300;
          text-transform: capitalize;
          font-size: 0.9375rem;
          letter-spacing: 1px;
          color: $ALSTOM_GREY;
        }
    }

    .checkbox-td {
      padding-left: 30px;
      width: 3%;
    }

    .ref-td {
      width: 8%;
      text-transform: uppercase;
      font-weight: 700;
      font-size: 0.8125rem;
      color: $ALSTOM_GREY;
    }

    .name-td {
      color: $BLACK;
      font-weight: 600;
      width: 30%;
      max-width: 500px;
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
    }

    .date-td {
      width: 20%;
      text-align: center;
    }

    .unit-price-td,
    .quantity-td,
    .total-td {
      width: 13%;
      text-align: center;
    }
  }

  .dispute-comments {
    margin-top: 30px;

    label {
      color: $ALSTOM_DARK_GREY;
      text-transform: uppercase;
      font-size: 0.75rem;
      letter-spacing: 1px;
      font-weight: 700;
      margin-right: 5px;

      @include breakpoint($BREAKPOINT_NOT_DESKTOP) {
        padding-left: 20px;
      }
    }

    textarea {
      border: none;
      padding: 15px 25px;

      &::-webkit-input-placeholder {
        color: $ALSTOM_GREY;
        font-size: 1rem;
        font-weight: 300;
      }

      &:-moz-placeholder {
        color: $ALSTOM_GREY;
        font-size: 1rem;
        font-weight: 300;
      }

      &::-moz-placeholder {
        color: $ALSTOM_GREY;
        font-size: 1rem;
        font-weight: 300;
      }

      &:-ms-input-placeholder {
        color: $ALSTOM_GREY;
        font-size: 1rem;
        font-weight: 300;
      }
    }
  }

  .button_margin {
    margin: 25px 0 0;

    @include breakpoint($BREAKPOINT_NOT_DESKTOP) {
      margin-left: 20px;
    }
  }

  .actions-dispute {
    display: flex;
    align-items: flex-end;
    margin-bottom: 60px;

    .cancel {
      color: #A4A7B3;
      font-size: 0.875rem;
      line-height: 0.875rem;
      font-weight: 700;
      letter-spacing: 1px;
      margin-left: 20px;
    }
  }

  .dispute-list {
    width: 100%;
    margin-bottom: 60px;

    @include breakpoint($BREAKPOINT_NOT_DESKTOP) {
      padding: 0 20px;

      h4 {
        color: $ALSTOM_DARK_GREY;
        text-transform: uppercase;
        letter-spacing: 1px;
        font-weight: 700;
        font-size: 0.8125rem;
        margin-bottom: 0;
      }
    }
  }

  .tab-choice {
    margin-top: 30px;
    display: flex;

    .choice {
      padding: 20px 40px;
      color: $ALSTOM_GREY;
      background-color: $ALSTOM_LIGHT_GREY;
      text-transform: uppercase;
      font-size: 1rem;
      font-weight: 700;
      letter-spacing: 1px;

      &:hover {
        cursor: pointer;
      }
    }

    .active {
      .choice {
        background-color: $WHITE;
        color: $PRIMARY_COLOR;
      }
    }
  }

  .dispute-table {
    width: 100%;
    background-color: $WHITE;

    th {
      color: $ALSTOM_DARK_GREY;
      text-transform: uppercase;
      letter-spacing: 1px;
      font-weight: 700;
      padding: 20px 30px;
      font-size: 0.8125rem;
    }

    td {
      padding: 20px 30px;
      color: $ALSTOM_DARK_GREY;
      font-weight: 300;
      letter-spacing: 1px;
      border-top: 1px solid $ALSTOM_LIGHT_GREY;

      @include breakpoint($BREAKPOINT_NOT_DESKTOP) {
        display: block;
        border-top: 0;
        padding: 10px 30px;
      }
    }

    tr:hover td {
      color: $PRIMARY_COLOR;
      cursor: pointer;

      @include breakpoint($BREAKPOINT_DESKTOP) {
        &:first-child {
          padding-left: 28px;
          border-left: 3px solid $PRIMARY_COLOR;
        }
      }
    }

    .col--id {
      @include breakpoint($BREAKPOINT_DESKTOP) {
        width: 100px;
      }
    }

    .center {
      @include breakpoint($BREAKPOINT_DESKTOP) {
        text-align: center;
      }
    }

    .hide {
      display: none;
    }

    @include breakpoint($BREAKPOINT_NOT_DESKTOP) {
      tr td:first-child {
        padding-top: 30px;
        border-top: 1px solid $ALSTOM_LIGHT_GREY;
      }

      tr:first-child td:first-child {
        border-top: 0;
      }
    }
  }
}
