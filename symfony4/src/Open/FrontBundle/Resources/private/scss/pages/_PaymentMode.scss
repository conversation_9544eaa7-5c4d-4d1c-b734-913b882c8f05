body[class*=" Page--payment_mode"] {
  .form-row {
    display: flex;
    flex-direction: row;
  }
  input:not([type="file"]) {
    height: 15px;
    width: 15px;
    margin: 0 20px;
    & + label {
      position: relative;
    }
  }
}

.checkout-success {
  .actions {
    display:flex;
    flex-direction: row;
    margin-top: 25px;

    button.inverse {
      margin-right: 50px;
    }

    @include breakpoint($BREAKPOINT_MOBILE_MAX) {
      flex-direction: column;

      button.inverse {
        margin-right: 0px;
        margin-bottom: 20px;
      }

      button {
        width: 100%;
      }
    }
  }
}
