@import "../variables";

body[class*="Page--invoices"]{
  background-color: $ALSTOM_GREY_BACKGROUND;

  .header-container {
    display: flex;
    flex-direction: column-reverse;
  }

  .search {
    label {
      display: none;
    }

    form {
      display: flex;
      justify-content: flex-end;
      padding-top: 0;
      margin-bottom: 10px;

      input[type="text"] {
        margin: 0;
        font-size: 0.75rem;
        overflow: hidden;
        white-space: nowrap;
        text-overflow: ellipsis;
        padding-right: 10px;
        box-shadow: none;
      }

      div {
        width: 100%;
      }
    }

    .Icon {
      stroke: $WHITE;
      width: 20px;
      height: 20px;
    }

    button {
      height: 60px;
      width: 60px;
      padding: 0;
      margin: 0;

      &:hover .Icon {
        stroke: $PRIMARY_COLOR;
      }
    }
  }

  @include breakpoint($BREAKPOINT_DESKTOP) {
    .header-container {
      justify-content: space-between;
      flex-direction: row;

      .tab-header {
        width: 60%;
      }
      .search-form {
        width: 40%;

        .form-row {
          min-height: 0;
        }
      }
    }

    .search form div {
      max-width: 300px;
    }

    .side-container {
      display: flex;
    }
    .Page-inner {
      width: 100%;
      padding: 30px 0px;
    }

    .side-menu-container:hover {
      width: 370px;
    }

    .content-list {
      tr{
        height:50px;
        color: $ALSTOM_GREY_TEXT;

        &.credit_note{
          background-color: #f4f5fa;
        }
      }
    }

    .tab-infos.plain-tab .tab-title {
      h4 {
        padding: 20px 40px;
        display: flex;
        align-items: center;
      }

      a {
        text-decoration: none;

        &:hover {
          text-decoration: none;
        }
      }
    }
  }

  @include breakpoint($BREAKPOINT_NOT_DESKTOP) {
    .Page-inner {
      padding-top: 0px;
    }

    .header-container .tab-header {
      padding-top: 10px;
    }

    .search-form {
      padding: 0px;

      .form-row {
        margin-top: 0;
        min-height: 0;
      }
    }

    .tab-infos.plain-tab .tab-title {
      width: 50%;

      h4 {
        padding: 20px 15px;
        text-align: left;
      }
    }

    .tab-content table tr {
      display: flex;
      flex-direction: column;
      padding-bottom: 20px;
    }

    .tab-content table tr td {
      padding: 10px 15px;
      display: flex;
      font-size: 0.9375rem;
      font-weight: 300;
      color: $ALSTOM_DARK_GREY;

      .label-mobile {
        font-weight: 400;
        margin-right: 10px;
      }
    }
  }

  .additional-row {
    padding: 25px 35px;
    .invoice-block {
      justify-content: flex-end;
      color: $BLACK;

      @include breakpoint($BREAKPOINT_NOT_DESKTOP) {
        flex-direction: column;
        align-items: flex-start;
      }
    }

    .invoice-row {
      display: flex;
      justify-content: space-between;
    }

    .right-block {
      margin-left: 50px;

      @include breakpoint($BREAKPOINT_NOT_DESKTOP) {
        margin: 25px 0 0;
      }
    }

    @include breakpoint($BREAKPOINT_NOT_DESKTOP) {
      .right-block, .left-block {
        width: 100%;
      }
    }

    .title {
      font-weight: 400 !important;
    }
  }
}

body[class*="Page--merchantOrder_invoice"] {

  .page-wrap {
    background-color: $ALSTOM_GREY_BACKGROUND;

    @include breakpoint($BREAKPOINT_NOT_DESKTOP) {
      padding-bottom: 60px;
    }
  }
  .Page-invoice-inner {
    margin: 50px 0;

    @include breakpoint($BREAKPOINT_NOT_DESKTOP) {
      margin-top: 0;
    }
  }
  .previous-page {
    display: flex;
    align-items: center;
    color: $ALSTOM_GREY;
    font-size: 1rem;
    font-weight: 600;
    margin: 1rem 0;
    i {
      margin: 0 0.5rem;
    }

    @include breakpoint($BREAKPOINT_NOT_DESKTOP) {
      margin-left: 1rem;
    }
  }

  .invoice_table .invoice-item a {
    color: $PRIMARY_COLOR;
  }

  .border-invoice {
    border-top: 10px solid $ALSTOM_GREY_BACKGROUND;
  }

  @include breakpoint($BREAKPOINT_NOT_DESKTOP) {
    .invoice-block {
      flex-direction: column;

      .left-block,
      .middle-block,
      .right-block {
        width: 100%;
      }

      .invoice-row {
        display: flex;
        justify-content: space-between;
        text-align: right;
      }
    }

    .invoice_table {
      display: flex;
      flex-direction: column;
      border-top: 1px solid $ALSTOM_LIGHT_GREY;

      .invoice-item {
        display: flex;
        flex-direction: column;
        height: auto;
        border-color: $ALSTOM_LIGHT_GREY;
        padding: 10px 0;

        td {
          display: flex;
          justify-content: space-between;
          padding: 5px 35px;
          border: 0;
        }
      }

      tr.total_block,
      tr.remaining_block {
        display: flex;
        flex-wrap: wrap;
        padding: 10px 35px;

        td {
          width: 50%;
          padding: 0 !important;
        }

        .total,
        .remaining {
          text-align: right;
        }

        .right-label {
          text-align: left;
        }
      }
    }
  }

  .merchant_order_block{
    background-color: $ALSTOM_GREY_BACKGROUND;
    color: $ALSTOM_GREY_TEXT;
    font-size: 16px;
    padding: 20px;

    .merchant_order_line{
      display: flex;
      flex-direction: row;
      justify-content: space-between;
      margin: 10px;

      span {
        &.title{
          color: #6B6F82;
          font-size: 16px;
          font-weight: 600;

          &.invoice_bold{
            font-weight: 900 !important;
          }
        }

        &.value{
          &.invoice_bold{
            font-weight: 900 !important;
            color: #6B6F82;
          }
        }
      }
    }
  }

  .total-block {
    display: flex;
    justify-content: flex-end;
    background-color: $ALSTOM_LIGHT_GREY;
    margin-top: 0px;

    .invoice_header {
      width: 50%;
      padding: 20px 0;
      .invoice-total {
        display: flex;
        justify-content: space-between;
        padding-right: 25px;

        .title,
        .value {
          color: #6B6F82;
          font-size: 14px;
          font-weight: 600;
          margin: 5px 10px;
        }

        .value {
          text-align: right;
        }

        @include breakpoint($BREAKPOINT_DESKTOP) {
          margin: 0 10px;
        }
      }

      .total_remaining {
        .title,
        .value {
          color: $BLACK;
        }
      }
    }

    @include breakpoint($BREAKPOINT_NOT_DESKTOP) {
      margin-top: 0;

      .invoice_header {
        width: 100%;
        float: none;
        padding: 20px 0 40px 25px;

        div {
          margin: 0;
        }

        div span.value {
          text-align: initial;
          margin-right: 35px;
        }
      }
    }
  }

  .upperline {
    border-top : 1px $PRIMARY_COLOR solid;
  }

  .invoice_table{
    width:100%;
    background-color: $WHITE;
    .label-detail {
      padding: 10px 45px;
    }

    tr {
      height:50px;
      border-bottom: 1px solid  $ALSTOM_GREY_TEXT;
      th, td {
        text-align: left;
        border-bottom: 1px solid $ALSTOM_LIGHT_GREY;
        padding: 20px 10px;
      }

      th {
        color: $ALSTOM_GREY;
        font-size: 14px;
        font-weight: 400;
      }

      @include breakpoint($BREAKPOINT_DESKTOP) {
        .invoice-name {
          padding-left: 30px;
        }
      }

      &.remaining_block, &.total_block{
        border : none;
        td {
          border: none !important;
          padding: 10px;
        }
      }

      .right-label {
        color: $ALSTOM_DARK_GREY;
        text-align: right;
        font-weight: 600;
      }
      .red{
        color:$RED;
      }
    }

    .invoice-total {
      font-weight: 600;
      height: auto;
      vertical-align: top;

      .separator-top {
        height: 2px;
        background-color: $PRIMARY_COLOR;
        width: 100%;
      }

      td {
        padding: 0;
      }

      .total-invoice-val {
        width: 100%;
        padding: 18px 10px 20px;

        &.no-padding-left {
          padding-left: 0;
        }

        @include breakpoint($BREAKPOINT_NOT_DESKTOP) {
          text-align: right;
          padding: 0;
        }
      }

      @include breakpoint($BREAKPOINT_NOT_DESKTOP) {
        padding: 10px 35px;

        .mobile-only {
          width: 50%;
        }

        .invoice-name .total-invoice-val {
          text-align: center;
        }
      }
    }

    .invoice-remaining {
      font-weight: 600;
    }

    .invoice-sub-item .invoice-name {
      padding-left: 60px;

      @include breakpoint($BREAKPOINT_NOT_DESKTOP) {
        padding: 0;
        justify-content: center;
      }
    }
  }

  .not-yet-invoiced {
    padding: 20px 0 20px 2px;
    font-size: 1rem;
    color: $ALSTOM_DARK_GREY;
    font-weight: 600;

    .not-yet-text {
      display: flex;
      justify-content: space-between;

      .not-yet-title {
        margin-right: 20px;
      }
    }

    .not-yet-inline {
      display: flex;
      justify-content: space-between;
      align-items: flex-end;

      @include breakpoint($BREAKPOINT_NOT_DESKTOP) {
        flex-direction: column;
      }
    }

    @include breakpoint($BREAKPOINT_NOT_DESKTOP) {
      padding-left: 30px;
    }
  }
}

.invoice-block {
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  align-items: center;
  height: auto;
  background-color: $WHITE;
  color: $ALSTOM_DARK_GREY;
  font-size: 16px;
  &.invoice-detail {
    margin: 0 !important;
    padding: 20px 30px;
    height: auto;
    align-items: flex-start;

    @include breakpoint($BREAKPOINT_DESKTOP) {
      height: 120px;
    }
  }

  .left-block, .middle-block, .right-block {
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    height: 100%;

    .invoice-row {
      span {
        text-align: left;
        letter-spacing: 1px;
        margin-right: 25px;

        @include breakpoint($BREAKPOINT_NOT_DESKTOP) {
          margin-right: 0;

          &.value {
            white-space: nowrap;
            display: flex;
            align-self: flex-end;
          }
        }
      }
      .title {
        font-weight: 600;
      }
    }
  }

  .right-block {
    .invoice-row {
      display: flex;
      justify-content: space-between;
      .value {
        margin-left: 15px;
      }
    }
  }
}

.tab-content {
  table {
    width: 100%;

    tr {
      border-bottom: 1px solid $ALSTOM_LIGHT_GREY;
      th {
        padding: 15px;
        font-size: 14px;
        color: $ALSTOM_DARK_GREY;
      }

      th:not(:nth-child(1)),
      td:not(:nth-child(1)) {
        text-align: center;
      }

      td {
        font-size: 14px;
        color: $BLACK;
        padding: 30px 15px;

        a {
          color: $ALSTOM_GREY;
          font-weight: 600;
        }
      }
    }


  }
}

.due-date {
  color: red !important;
}
