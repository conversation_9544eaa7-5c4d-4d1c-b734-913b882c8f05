@import "../variables";
@import "../modules/Form";

.Page-login {

  .Page-outer {
    margin: 0 auto;
  }

  .LoginForm-form {
    h2 {
      margin: 0;
    }
  }
}

.LoginForm {
  background: #fff;
  .footer-login {
    margin-top: 20px;
  }
  &-form {
    display: flex;
    flex-direction: column;
    padding-top: 0 !important;

    .LoginForm-header {
      margin: 0 auto;
      color: #000;
      font-size: 26px;
      font-weight: 600;
      text-align: center;
      width: 100%;
      background: #ececec;
      padding: 20px 10px;
    }

    .LoginForm-section {
      background: none;
      max-height: none;
      padding: 40px 0 0 0;
      margin: 0;
    }

    .form-row {
      margin: 15px auto 0 auto;
      width: 80%;
      .label {
        margin-top: 0px;
        color: $BLACK;
        font-size: 12px;
        font-weight: bold;
      }

      input {
        margin-top: 10px;
        padding: 10px;
        background-color: $WHITE;
        box-shadow: 5px 5px 30px $ALSTOM_LIGHT_GREY;
        border: 1px $ALSTOM_LIGHT_GREY solid;
        margin-bottom: 0;
        &::-webkit-input-placeholder {
          font-style: italic;
        }

        &::-moz-placeholder {
          font-style: italic;
        }
      }
    }

    .login-submit {
      button {
        width: 100%;
      }
      .LoginForm-forgotPassword {
        margin-top: 6px;
        font-size: 0.875rem;
        line-height: 1rem;
        width: 100%;
        text-align: left;
        a {
          color: $ALSTOM_BLUE;
          text-decoration: underline;
        }
        &.footer-login {
          font-size: 1.2rem;
          line-height: 1.4;
          text-align: center;
          margin-top: 35px;
          border-top: 1px solid #cecece;
          padding-top: 3px;
          a {
            display: block;
            text-align: center;
          }
        }
      }
    }

    .LoginForm-footer {
      display: flex;
      margin-top: 10px;
      background-color: $ALSTOM_DARK_PURPLE;
      justify-content: center;
      .text, a {
        font-size: 0.8rem;
        margin: 1rem 0.5rem;
        text-align: center;
        color: $WHITE;
        white-space: nowrap;
      }

      a {
        font-weight: 600;
      }
    }

    @include breakpoint($BREAKPOINT_NOT_DESKTOP) {
      .LoginForm-section {
        padding: 0;
        .LoginForm-header {
          margin: 0 1rem;
        }
        .form-row {
          width: 90%;
        }
        .LoginForm-footer {
          margin: 0;
        }
      }
    }
  }
}
