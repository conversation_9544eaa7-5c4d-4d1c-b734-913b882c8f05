@import "../_variables";

body[class*="Page--tickets"],
body[class*="Page--ticket_create"],
body[class*="Page--ticket_edit"],
body[class*="Page--ticket_external_edit"],
body[class*="Page--illegal_content_create"],
body[class*="Page--search"] {
  .side-container {

    @include breakpoint($BREAKPOINT_DESKTOP) {
      display: flex;
    }

    @include breakpoint($BREAKPOINT_NOT_DESKTOP) {
      margin-bottom: 60px;
    }

    .Page-inner, .container-inner {
      flex-grow: 1;

      .buttons .card-body {
        margin-bottom: 10px;
      }

      @include breakpoint($BREAKPOINT_DESKTOP) {
        margin: 20px 0 50px;
      }
      @include breakpoint($BREAKPOINT_NOT_DESKTOP) {
        width: 100%;
        padding: 0 20px;

        .buttons .card-body a {
          width: 100%;
        }
      }
    }
  }

  .empty-div {
    padding: 25px;
    background-color: $WHITE;
  }
}



// List ?
body[class*=" Page--tickets"],
body[class*=" Page--merchant_tickets"] {

  background-color: $ALSTOM_GREY_BACKGROUND;

  table {
    width: 100%;
    border: 2px solid $WHITE;

    thead {
      background: $WHITE;
      border-bottom: 1px solid $ALSTOM_LIGHT_GREY;
      text-transform: uppercase;
      font-size: 0.8125rem;
      letter-spacing: 1px;
      th {
        color: $ALSTOM_DARK_GREY;
        padding: 15px;
        text-align: center;

        a {
          color: $ALSTOM_DARK_GREY;
          text-decoration: none;
        }
      }
    }

    tbody {
      background-color: $WHITE;
      tr {
        background: transparent;
        cursor: pointer;
        border-bottom: 1px solid $ALSTOM_LIGHT_GREY;
        font-weight: 300;
        font-size: 0.9375rem;
        padding-left: 2px;
        &:hover {
          box-shadow: 0px 3px 10px $ALSTOM_LIGHT_GREY;

          td , td a{
            color: $PRIMARY_COLOR;
          }

          td:first-child {
            border-left: 2px solid $PRIMARY_COLOR;
          }
        }
      }

      tr.ticket--new {
        font-weight: bold;
      }

      td {
        text-align: center;
        padding: 1rem;
        border-left: 2px solid $WHITE;
        a {
          color: $ALSTOM_DARK_GREY;
          text-decoration: none;
          &:hover {
            color: $PRIMARY_COLOR
          }
        }
      }
    }

    .col--left {
      text-align: left;
    }

    .col--icon {
      display: flex;
      align-items: center;

      .Icon {
        fill: $PRIMARY_COLOR;
        width: 1.5rem;
        height: 1.5rem;
      }
    }
  }

  .card {

    &-title {
      display: flex;
      text-transform: uppercase;
      margin: 0;

      .tab-a {
        color: $ALSTOM_GREY;
        background-color: $ALSTOM_LIGHT_GREY;
        padding: 20px 30px;
        margin-bottom: 0;
        font-size: 1rem;
        letter-spacing: 1px;
        font-weight: 700;
        &:hover {
          text-decoration: none;
        }
        &.active {
          color: $ALSTOM_BLUE;
          background-color: $WHITE;
          border-bottom: none;
        }
      }
    }
  }

  .tickets--mobile {
    a {
      color: $TEXT_DEFAULT;

      &:hover {
        text-decoration: none;
      }
    }
  }


  @include breakpoint($BREAKPOINT_DESKTOP) {

    .card {
      .card-body {
        display: flex;
        justify-content: space-between;
        align-items: center;

        .button {
          margin: 10px 0;
        }
      }

      &.buttons {
        margin: 0;
      }
    }

    .tickets--desktop {
      display: block;
      margin-bottom: 60px;
    }



  }

  @include breakpoint($BREAKPOINT_MOBILE_MAX) {

    table {
      thead {
        display: none;
      }

      tbody {
        tr {
          td {
            display: block;
            text-align: left;
            letter-spacing: 0.1rem;
            padding: 10px 28px;
            .label {
              margin-bottom: 0;
              font-size: 0.9rem;
              color: $ALSTOM_DARK_GREY;
              text-transform: uppercase;
            }

            &:first-child {
              padding-top: 20px;
            }
          }
        }
      }
    }

    .card-title {
      margin: 0 auto;
      a {
        padding: 1.5rem;
      }
    }
  }

  .TicketsList {
    margin: 0 10px;

    &-prevButton ,
    &-nextButton {
      text-align: center;
      //margin: 20px 0;

      a {
        position: relative;
        color: $APPLI_PURPLE;
        display: block;
        text-decoration: none;
        padding: 20px 0;
      }

      &:hover {
        a {
          color: $APPLI_DARK_YELLOW;
          &:before {
            color: $APPLI_DARK_YELLOW;
          }

          &:after {
            color: $APPLI_DARK_YELLOW;
          }
        }
      }

    }

    &-prevButton {
      margin-top: 10px;
    }

    &-nextButton {
      margin-bottom: 10px;
    }

    &-prevButton a:before {
      content: "\2303";
      position: absolute;
      top: 2px;
      left: 50%;
      transform: translateX(-50%);
      font-size: 20px;
      color: $APPLI_PURPLE;
    }

    &-nextButton a:after {
      content: "\2304";
      position: absolute;
      top: 50%;
      left: 50%;
      transform: translateX(-50%);
      font-size: 20px;
      color: $APPLI_PURPLE;
    }


    &-row {
      display: flex;
      justify-content: space-between;
      padding: 10px 0;

      border-top: 1px solid $APPLI_GREY11;

      &.ticket--new {
        font-weight: bold;
      }
    }

    &-col {
      flex-grow: 1;
      padding: 0 5px;
      align-self: center;

      .Icon {
        width: 24px;
        height: 24px;
        fill: $APPLI_DARK_YELLOW;
      }

      &:last-of-type ,
      &:first-of-type {
        flex-shrink: 1;
        flex-grow: 0;
      }

      p {
        margin: 0;
      }

      &.has-icon {
        align-self: flex-start;
      }
    }
  }

}


// Create / Edit
body[class*=" Page--ticket_create"],
body[class*=" Page--merchant_ticket_create"],
body[class*=" Page--merchant_ticket_edit"] ,
body[class*=" Page--ticket_edit"] ,
body[class*=" Page--ticket_external_edit"] ,
body[class*=" Page--contact"],
body[class*=" Page--what-are-you-looking-for"],
body[class*=" Page--illegal_content_create"],
body[class*=" Page--search"] {

  background-color: $ALSTOM_GREY_BACKGROUND;

  .LoginForm {
    margin-top: 40px;
  }

  textarea {
    border: 1px solid #252525;
    padding: 5px;
  }

  .card-header {
    color: $BLACK;
    font-size: 24px;
  }


  .col-md-12, .form-row, textarea{
    width: 100%;
  }

  .Button--upload{
    @include breakpoint($BREAKPOINT_NOT_DESKTOP) {
      display: flow-root;
      margin-top: 5px;
      top: 0 !important;
      padding: 10px 20px 10px 10px;
    }
  }

  .form-group{
    margin-bottom: 30px;
  }

  .ticket--message {
    width: 100%;
    margin-bottom: 20px;

    textarea {
      min-height: 90px;
    }

    .form-row {
      width: 100%;
    }

    label {
      color: $APPLI_GREY4;
      position: relative;
    }

    .attachment-files {
      list-style-type: none;
      padding-left: 5px;
      margin-top: 10px;

      @include breakpoint($BREAKPOINT_NOT_DESKTOP) {
        padding-left: 0;
      }

      .file--warning {
        color: $ALSTOM_GREY;
        font-style: italic;
        text-align: center;
        background-color: transparent;
        border: 1px solid $ALSTOM_GREY;
        border-radius: 5px;
        padding: 10px;
      }

      li {
        padding: 10px 30px 10px 10px;
        list-style-type: none;
        background: $APPLI_GREY8;
        color: $APPLI_GREY6;
        position: relative;


        .Icon {
          width: 24px;
          height: 24px;
          position: absolute;
          right: 5px;
          top: 50%;
          transform: translateY(-50%);

          fill: $APPLI_GREY6;
        }

        &:hover {
          color: $APPLI_PURPLE_DARK;

          .Icon {
            fill: $APPLI_PURPLE_DARK;
          }
        }
      }
    }
  }

  .ticket--subject {
    width: 100%;

    .form-row {
      width: 100%;
    }
  }

  .ticket--attachment {
    margin-top: 20px;
    position: relative;
    padding-top: 15px;
    display: block !important;

    .Button {
      margin: 1em;
      // padding: 20px 45px !important;


      @include breakpoint($BREAKPOINT_NOT_DESKTOP) {
        left: 0;
        top: 40px;
      }

      @include breakpoint($BREAKPOINT_DESKTOP) {
      }
    }

    .button--reset {
      border: 0;
      width: auto;
      padding: 0;
      right: 0;
      background: transparent;
      border-radius: 0;
      margin: 1em;

      &:hover {
        background-color: transparent;

        .Icon {
          fill: $RED;
        }
      }

      .Icon {
        width: 24px;
        height: 24px;
        fill: $APPLI_PURPLE;
      }
    }
  }


  .Buttons-group {
    width: 100%;
    margin: 40px 0 20px 0;

    .btn-primary {
      @include breakpoint($BREAKPOINT_NOT_DESKTOP) {
        margin: 0;
      }
    }
  }

  .Ticket-detail {
    padding: 0;
    margin: 0;

    p {
      font-weight: 300;
      letter-spacing: 1px;
      font-size: 0.875rem;
      color: $ALSTOM_DARK_GREY;
    }

    .card-body {
      display: flex;
      padding: 0;
      strong {
        color: $ALSTOM_DARK_GREY;
        letter-spacing: 1px;
        font-weight: 700;
        font-size: 0.8125rem;
      }

      .card-subject strong {
        margin-right: 5px;
      }

      p:nth-child(1) {
        margin: 0;
      }
      @include breakpoint($BREAKPOINT_DESKTOP) {
        p:not(:nth-child(1)) {
          margin: 0 0 0 20px;
        }
      }

      @include breakpoint($BREAKPOINT_NOT_DESKTOP) {
        p {
          margin: 1rem 0;
        }

        .card-subject {
          strong {
            display: block;
          }
        }
      }
    }
  }

  .messages-label {
    margin: 20px 10px 10px 0;

    @include breakpoint($BREAKPOINT_NOT_DESKTOP) {
      margin: 0 0 10px;
    }
  }

  .messages-label strong,
  .Ticket-form label {
    letter-spacing: 1px;
    font-weight: 700;
    font-size: 0.8125rem;
    margin-bottom: 10px;
  }

  .Ticket-response {
    margin: 0;
    background-color: $WHITE;
    .card-header {
      background: $APPLI_PURPLE;
      font-size: 14px;
      color: $WHITE;
      padding: 10px;
      .Icon {
        transform: translateY(-2px);
      }
    }

    @include breakpoint($BREAKPOINT_NOT_DESKTOP) {
      .card-body {
        padding: 20px 30px;
        letter-spacing: 0.05rem;
        div {
          padding: 0.5rem 0;
        }
        .card-info {
          display: flex;
          flex-direction: row;
          font-weight: 400;
          color: $ALSTOM_DARK_GREY;
        }

        .card-message {
          font-weight: 300;
          color: $ALSTOM_DARK_GREY;
        }

        .card-attachments {
          display: flex;
          flex-direction: column;
          a {
            margin: 0;
            width: 100%;
            color: $PRIMARY_COLOR;
            font-weight: 600;
          }
        }
      }
    }


    .card-footer {
      background: $APPLI_GREY8;
      padding: 10px;

      .Icon {
        fill: $TEXT_DEFAULT !important;
        transform: translateY(-1px);
      }

      hr {
        height: 1px;
        border: 0;
        background-color: $APPLI_GREY6;
        margin-bottom: 5px;
      }

      a {
        color: $TEXT_DEFAULT;
      }
    }

    .card-header,
    .card-footer {

      > div {
        position: relative;
        padding-left: 25px;
      }

      .Icon {
        position: absolute;
        left: 0;
        top: 4px;
        width: 16px;
        height: 16px;
        fill: $APPLI_DARK_YELLOW;

      }
    }

    &:first-of-type {
      margin-top: 0;
    }
  }

  .Ticket-responseForm {
    display: none;
    margin: 0 0 20px 0;
    padding: 0 10px;

    .form-row {
      width: 100%;
    }

    textarea {
      padding: 5px;
      border: 1px solid $APPLI_PURPLE;
      width: 100%;
      min-height: 300px;
    }

    &.is-visible {
      display: block;
    }
  }

  .ticket-responseButton {
    background: $WHITE;
    color: $APPLI_PURPLE;

    margin-bottom: 20px;

    &:hover {
      background: $APPLI_PURPLE;
      color: $WHITE;

    }
  }

  .Ticket-extraButtons {
    button {
      text-transform: uppercase;
    }
  }

  @include breakpoint($BREAKPOINT_DESKTOP) {

    .Ticket-extraButtons {
      display: flex;
      justify-content: space-between;
      align-items: center;

      button {
        margin: 0;
      }
    }

    .Ticket-detail {
      display: inline-block;
      margin: 10px 0 0 0;
    }

    .Ticket-response {
      margin: 0;
      .card-header {
        display:flex;
        justify-content: space-between;
        .Icon {
          transform: translateY(-1px);
        }
      }

      .card-body {
        border-bottom: 1px solid $ALSTOM_LIGHT_GREY;
      }
    }

    .Ticket-responseForm {
      margin: 0 0 20px 0;
      padding: 20px;

      -webkit-box-shadow: 0 0 10px 0 rgba(153,153,153,1);
      -moz-box-shadow: 0 0 10px 0 rgba(153,153,153,1);
      box-shadow: 0 0 10px 0 rgba(153,153,153,1);

      textarea {
        padding: 0;
        border: 0;
        border-bottom: 1px solid $APPLI_PURPLE;
      }
    }

    .card-body {
      padding: 10px;
      display: flex;
      flex-direction: row;
      justify-content: space-between;
      flex-wrap: nowrap;

      div {
        margin: 10px 20px;
      }

      .card-info {
        display: flex;
        flex-direction: column;
        min-width: 100px;
        margin-left: 0;
        letter-spacing: 1px;
        font-weight: 400;
        color: $ALSTOM_DARK_GREY;
        align-self: center;
        .info-date {
          margin-top: 0;
          white-space: nowrap;
        }
      }

      .card-message {
        flex-grow: 1;
        color: $ALSTOM_DARK_GREY;
        font-size: 0.875rem;
        font-weight: 300;
        letter-spacing: 1px;
        margin: 0;
        display: flex;
        align-items: center;
      }

      .card-attachments {
        display: flex;
        flex-direction: column;
        min-width: 100px;

        a {
          color: $PRIMARY_COLOR;
          font-weight: bold;
        }
      }
    }

    .anonymous-ticket-form {

      .card-body {
        display: flex;
        flex-direction: column;

        .user-info {
          display: flex;
          flex-direction: row;
          flex-wrap: wrap;
          margin: 0;
          .form-row {
            flex: 46%;
            margin: 10px;
          }
        }
      }

      div {
        margin: 0;
      }

      textarea {
        border: none;
      }

      .attachment-files {
        margin-top: 20px;
      }
    }

    .form-row {
      width: 48%;
    }

    .LoginForm-fields .form-row {
      width: 80%;
    }
  }
}

body[class*="Page--ticket_create"],
body[class*=" Page--what-are-you-looking-for"],
body[class*=" Page--search"] {
  .Ticket-Form {

    .cart-header {
      font-size: 2em;
      color: $BLACK;
      margin-top: 10px;
    }
    form {
      padding-top: 0;

      @include breakpoint($BREAKPOINT_DESKTOP) {
        margin-left: -50px;
      }

      .ticket--subject input {
        border: 0;
        box-shadow: none;
      }

      .ticket--message {
        margin-top: 0;

        label {
          margin: 0 0 2px 20px;
          color: $ALSTOM_DARK_GREY;
          text-transform: uppercase;
          letter-spacing: 1px;
        }

        textarea {
          padding: 15px 20px;
          font-weight: 300;
        }

        .attachment-files {
          padding-left: 0;
          margin-top: 30px;
        }
      }

      .card-body {
        display: flex;
        flex-direction: column;

        textarea {
          border: none;
          resize: none;
        }
      }

      .ticket--attachment .Button {
        transform: none;
        left: 0;

        &.js-button-upload {
          &:hover {
            border: 1px solid $PRIMARY_COLOR;
          }
        }

        &.button--reset {
          right: 0;
          left: auto;
          margin-right: 0;
        }
      }

      button[type="submit"] {
        margin-left: 20px;
      }
    }
  }

  @include breakpoint($BREAKPOINT_NOT_DESKTOP) {
    .Form {
      padding: 0;
    }

    .Ticket-Form form .ticket--message label {
      margin-left: 0;
    }

    .Ticket-Form form .ticket--message .attachment-files {
      margin-top: 70px;
    }

    .Buttons-group {
      margin-top: 10px;
    }

    .Ticket-Form form button[type="submit"] {
      margin: 0 auto;
    }
  }
}

body[class*=" Page--ticket_edit"] ,
body[class*=" Page--contact"],
body[class*=" Page--what-are-you-looking-for"],
body[class*=" Page--merchant_ticket_edit"],
body[class*=" Page--ticket_external_edit"],
body[class*=" Page--search"] {

  .container-inner {
    padding-bottom: 20px;
    display: flex;
    flex-direction: column;

    form {
      order: -1;
    }

    @include breakpoint($BREAKPOINT_DESKTOP) {
      display: block;
    }
  }

  .ticket--message {
    .Buttons-group {
      margin-top: 0;

      button {
        margin: 0 auto;
      }

      .btn-primary {
        background: $WHITE;
        color: $APPLI_PURPLE;
        &:hover {
          background: $APPLI_PURPLE;
          color: $WHITE;
        }
      }
    }
  }

  .Message-item {
    //margin-bottom: 10px;
  }

  .ticket--resolved {
    //display: none;
    margin-top: 50px;
    padding-left: 10px;

    .form-row {
      min-height: 0;

      .form-control-label:after ,
      .form-control-label:before {
        transform: translateY(5px) !important;
      }
    }
  }
  .Buttons-group {
    margin-bottom: 0;
  }

  @include breakpoint($BREAKPOINT_DESKTOP) {
    .ticket--attachment {
      .Button {
      }
    }
  }
}

body[class*=" Page--contact"],
body[class*=" Page--what-are-you-looking-for"],
body[class*=" Page--search"] {

  @include breakpoint($BREAKPOINT_NOT_DESKTOP) {
    .Ticket-Form {
      max-width: 100%;
      margin: 0;
    }

    textarea {
      border: none;
      color: $ALSTOM_GREY;
      padding: 15px;
    }
  }
}

body[class*=" Page--ticket_edit"],
body[class*=" Page--contact"],
body[class*=" Page--what-are-you-looking-for"],
body[class*=" Page--search"] {
  .side-menu-container {
    margin-bottom: 0;
  }

  .ticket-textarea {
    width: 100%;

    .error {
      color: red;
    }
  }

  textarea {
    padding: 20px 30px;

    &::-webkit-input-placeholder {
      color: $ALSTOM_GREY;
      font-size: 1rem;
      font-weight: 300;
    }

    &:-moz-placeholder {
      color: $ALSTOM_GREY;
      font-size: 1rem;
      font-weight: 300;
    }

    &::-moz-placeholder {
      color: $ALSTOM_GREY;
      font-size: 1rem;
      font-weight: 300;
    }

    &:-ms-input-placeholder {
      color: $ALSTOM_GREY;
      font-size: 1rem;
      font-weight: 300;
    }
  }

  .form-buttons {
    margin-top: 10px;

    [type="checkbox"]:not(:checked) + label:before, [type="checkbox"]:checked + label:before {
      top: 5px;
    }

    [type="checkbox"]:not(:checked) + label:after, [type="checkbox"]:checked + label:after {
      top: 9px;
    }
  }
}

body[class*=" Page--ticket_external_edit"] {
  .button_margin {
    margin-left: 0;
  }
}

.Ticket-form {
  display: flex;
  flex-direction: column;

  button {
    margin-left: 0;
  }

  label {
    font-weight: 900;
    color: $ALSTOM_DARK_GREY;
  }

  [type="checkbox"]:not(:checked) + label, [type="checkbox"]:checked + label {
    padding-top: 0;

    @include breakpoint($BREAKPOINT_NOT_DESKTOP) {
      padding-top: 4px;
    }
  }

  [type="checkbox"]:not(:checked) + label:before,
  [type="checkbox"]:checked + label:before {
    background-color: $WHITE;
  }

  .row-container {
    //display: flex;
    //flex-direction: row;
    //justify-content: space-between;

    .form-row {
      width: 100% !important;
    }

    textarea {
      border: none;
      resize: none;
      flex-grow: 1;
      width: 100%;
      min-height: 200px;
      padding: 20px 30px;

      @include breakpoint($BREAKPOINT_NOT_DESKTOP) {
        min-height: 133px;
      }
    }

    .attachment-files {
      list-style-type: none;
      padding-left: 5px;
      margin-top: 10px;

      @include breakpoint($BREAKPOINT_NOT_DESKTOP) {
        padding-left: 0;
      }

      .file--warning {
        color: $ALSTOM_GREY;
        font-style: italic;
        text-align: center;
        background-color: transparent;
        border: 1px solid $ALSTOM_GREY;
        border-radius: 5px;
        padding: 10px;
      }

      li {
        padding: 10px 30px 10px 10px;
        list-style-type: none;
        background: $APPLI_GREY8;
        color: $APPLI_GREY6;
        position: relative;


        .Icon {
          width: 24px;
          height: 24px;
          position: absolute;
          right: 5px;
          top: 50%;
          transform: translateY(-50%);

          fill: $APPLI_GREY6;
        }

        &:hover {
          color: $APPLI_PURPLE_DARK;

          .Icon {
            fill: $APPLI_PURPLE_DARK;
          }
        }
      }
    }

    button {
      background-color: $PRIMARY_COLOR;
      color: $WHITE;
      border: solid 2px $PRIMARY_COLOR;
      padding: 15px;
      width: fit-content;
      height: fit-content;
      margin-top: 0;
      &:hover {
        background-color: $WHITE;
        color: $PRIMARY_COLOR;
        border: solid 2px $PRIMARY_COLOR;
      }
    }

    .attachments {
      display: flex;
      flex-direction: column;
      min-width: 200px;
      margin-top: 0;
    }


  }

  .btn-save {
    margin: 20px 0;
  }

  @include breakpoint($BREAKPOINT_NOT_DESKTOP) {
    margin: 0;

    .row-container {
      display: flex;
      flex-direction: column;

      button {
        display: block;
        width: fit-content;
        padding: 10px 20px 10px 10px;
        margin: 5px 0 0;
      }

      .attachments {
        margin: 1rem 0;
      }
    }
  }
}

@include breakpoint($BREAKPOINT_NOT_DESKTOP) {

  .card-body {
    .header {
      width: 90%;
      margin: 0 auto;
    }
    a {
      width: 90%;
      margin: 1rem auto;
    }
  }

  .Ticket-detail {
    margin: 0 !important;
    padding: 0 !important;
    .card-body {
      display: flex;
      flex-direction: column;
      p {
        margin: 0;
      }
    }
  }

  .Ticket-response {
    border-bottom: 1px solid $ALSTOM_LIGHT_GREY;
    .card-body {
      padding: 1rem;
      .card-info {
        display: flex;
        flex-direction: row;
        flex-wrap: wrap;
        justify-content: space-between;
        font-weight: 600;
        color: $ALSTOM_DARK_GREY;
      }
    }

    &:last-child {
      border-bottom: 0;
    }
  }
}

.link-previous {
  margin: 1rem 0;

  i {
    margin-right: 8px;
    border-color: $ALSTOM_GREY;
  }
  a {
    display: flex;
    align-items: center;
    color: $ALSTOM_GREY;
    font-size: 0.875rem;
    font-weight: 600;
  }

  @include breakpoint($BREAKPOINT_NOT_DESKTOP) {
    margin-top: 0;
  }
}

.messages-label {
  color: $ALSTOM_DARK_GREY;
  margin: 1rem 0;

  @include breakpoint($BREAKPOINT_NOT_DESKTOP) {
    margin: 0 0 1rem;
  }
}

body[class*=" Page--search"] {
  #Search-Content form#form-ticket-edit {
    display: block;
  }
}

#form-ticket-edit #js-submit-button {
  padding: 20px 45px !important;
}

#ticket_isForAdmin {
  input {
    display: inline-block;
    margin: 0;
    height: inherit;
    border: none;
    box-shadow: none;
    width: inherit;
  }
  label {
    position: inherit;
  }
  + label {
    display: none;
  }
}

#ticket_messages_0_content {
  height: 200px;
}
