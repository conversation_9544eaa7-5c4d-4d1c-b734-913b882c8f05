@import "../variables";

body[class*="Page--order"] {
  background-color: $ALSTOM_GREY_BACKGROUND;

  .update-icon {
    width: 15px;
    height: 15px;
    display: inline-block;
    background-repeat: no-repeat;
    background-size: 100% 100%;
    margin-left: 10px;
    cursor: pointer;
  }

  .header-container {
    display: flex;
    flex-direction: column-reverse;
    max-height: 65px;
  }

  .validation_number_action  {
    color: $PRIMARY_COLOR;
    font-weight: 600;
    text-align: right;
  }

  .search {
    label {
      display: none;
    }

    form {
      display: flex;
      justify-content: flex-end;
      padding-top: 0;
      margin-bottom: 5px;

      input[type="text"] {
        margin: 0;
        font-size: 0.75rem;
        overflow: hidden;
        white-space: nowrap;
        text-overflow: ellipsis;
        padding-right: 10px;
        box-shadow: none;
      }

      div {
        width: 100%;
      }
    }

    .Icon {
      stroke: $WHITE;
      width: 20px;
      height: 20px;
    }

    button {
      height: 60px;
      width: 60px;
      padding: 0;
      margin: 0;

      &:hover .Icon {
        stroke: $PRIMARY_COLOR;
      }
    }
  }

  .export a {
    color: $WHITE;

    &:hover {
      color: $PRIMARY_COLOR;
    }

    @include breakpoint($BREAKPOINT_TABLET) {
      width: 200px;
    }
  }

  @include breakpoint($BREAKPOINT_DESKTOP) {
    .header-container {
      justify-content: space-between;
      flex-direction: row;

      .tab-header {
        width: 60%;
      }
      .search-form {
        width: 40%;
      }
    }

    .search form div {
      max-width: 300px;
    }

    .side-container {
      display: flex;
    }
    .Page-inner {
      width: 100%;
      padding: 30px 0px;
    }

    .side-menu-container:hover {
      width: 370px;
    }

    .tab-infos.plain-tab .tab-title .tab-h4 {
      padding: 0 30px;
      display: flex;
      flex-direction: column;
      justify-content: center;
    }

    .export a {
      margin: 0;
    }
  }

  @include breakpoint($BREAKPOINT_NOT_DESKTOP) {
    .Page-inner {
      padding-top: 0;
      padding-top: 65px;
    }

    .header-container .tab-header {
      padding-top: 10px;
      min-height: 70px;
    }

    .search-form {
      padding: 0px;

      .form-row {
        margin-top: 0;
        min-height: 0;
      }
    }

    .tab-infos.plain-tab .tab-title {
      h4 {
        padding: 20px 15px;
        text-align: left;
      }
    }
  }
}

.export {
  margin-left: auto;

  a {
    color: $ALSTOM_GREY;
  }
}

.between {
  background-color: $ALSTOM_GREY_BACKGROUND;
  height:20px;
}

.order-status {
  display: flex;
  font-weight: 600;
  color: $ALSTOM_DARK_GREY;
  font-size: 16px;
  float: right;
}

.order-sub-id {
  color: $ALSTOM_DARK_GREY;
  font-size: 16px;
}

.tab_pagination {
  margin: 10px 0;
}
.content-orders {
  .content-list {
    display: flex;
    flex-direction: column;
  }
}

.order-block {
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  min-height: 100px;
  background-color: $WHITE;
  color: $ALSTOM_DARK_GREY;
  font-size: 0.9375rem;

  &:not(.empty-tab) {
    margin: 35px;

    @include breakpoint($BREAKPOINT_NOT_DESKTOP) {
      margin: 8px 15px;
    }
  }

  .text-underlined {
    text-decoration: underline !important;
  }
  .text-bold {
    font-weight: 600 !important;
  }
  .cost-center-additional-info {
    margin: auto 0 0 48px;
    display: none;
  }
  .p-value {
    margin: 0;
    padding: 0;
  }

  .block {
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    font-weight: 300;

    .order-row {
      line-height: 2.1875rem;

      span {
        text-align: left;
        letter-spacing: 1px;
      }
      .title {
        font-weight: 400;
      }

      @include breakpoint($BREAKPOINT_NOT_DESKTOP) {
        line-height: 25px;
      }
    }

    @include breakpoint($BREAKPOINT_MOBILE_MAX) {
      max-width: 100%;
    }
  }

  .order-info {
    .order-total {
      span {
        font-weight: 700 !important;
      }
    }
  }

  .order-action {
    height: 100%;
    text-align: right;

    .order-links {
      display: flex;
      justify-content: flex-end;
      span, a {
        color: $ALSTOM_BLUE;
        font-weight: 600;
      }
    }
  }
    @include breakpoint($BREAKPOINT_MOBILE_MAX) {
      display: flex;
      flex-direction: column;
      height: auto;

      .order-action {
        text-align: left;
      }

      .order-links {
        display: flex;
        flex-direction: column;
      }

      .order-row {
        margin: 0.3rem 0;
      }
  }
}

.Page-order-detail {

  width: 100%;
  margin-top: 20px;

  .previous-block {
    display: flex;
    align-items: center;
    .previous-page {
      display: flex;
      align-items: center;
      color: $ALSTOM_GREY;
      font-size: 1rem;
      font-weight: 600;
      margin: 1rem 0;
      i {
        margin: 0 0.5rem;
      }

      @include breakpoint($BREAKPOINT_NOT_DESKTOP) {
        margin: 0 1rem 5px;
      }
    }
  }
  .resume-block {
    background-color: $WHITE;
    padding: 10px;
    margin-bottom: 15px;

    .order-block .details-link {
      display: none;
    }
  }

  .Page-content {

    .Page-body {
      background-color: $WHITE;
      .merchant-bloc-actions {
        display: flex;
        align-items: center;
        justify-content: space-between;
        padding: 20px 45px 15px 45px;
        width: 100%;

        .nb-products {
          color: $PRIMARY_COLOR;

          @include breakpoint($BREAKPOINT_MOBILE_MAX) {
            display: block;
            margin-left: 35px;
          }
        }

        a {
          color: $PRIMARY_COLOR;
          font-weight: 600;
          text-align: right;
        }

        .right-block {
          display: flex;
          flex-direction: column;
          align-items: flex-end;

          .order-links-mobile {
            display: flex;
            flex-direction: column;
          }

          div, a {
            margin: 5px 0;
          }
        }

        @include breakpoint($BREAKPOINT_MOBILE_MAX) {
          flex-direction: column;
          align-items: flex-start;
          padding: 20px 25px;

          .order-status {
            font-weight: 300;
            font-size: 0.875rem;
          }

          .right-block {
            width: 100%;
            align-items: flex-start;

            .order-links-mobile {
              width: 100%;
              align-items: flex-start;
            }
          }
        }
      }

      .merchant-title {
        margin: 60px 0 10px;
        color: $BLACK;

        i {
          width: 15px;
          height: 15px;
          margin-right: 15px;
        }

        &:first-of-type {
          margin-top: 0;
        }
      }
    }
    .Page-footer {

    }

    table {
      margin: 0;
      width: 100%;
      th:nth-child(1) {
        display: table-cell;
        width: 25%;
      }
      th:nth-child(2) {
        width: 10%;
      }
      th:nth-child(3) {
        width: 10%;
      }
      th:nth-child(4) {
        width: 10%;
      }

      td:nth-child(1) {
        display: table-cell;
      }

      &.seller-table {
        .label-detail {
          padding: 10px 45px;
        }

        .product {
          margin: 5px 45px;
          padding: 10px 0;

          .img-container {
            max-width: 100px;
            width: 100px;
            height: 100px;
            overflow: hidden;
            display: flex;
            align-items: center;
            img {
              position: relative;
              width: auto;
              height: auto;
              max-height: 100px;
              left: 50%;
              transform: translateX(-50%);
            }
          }
        }

        thead th,
        tbody td {
          padding: 10px;
        }
      }

      .resume-table {
        td:nth-child(3) {
          display: flex;
          text-align: left;
        }
        td:nth-child(4) {
          text-align: right;
        }
      }

      .item-total-price {
        text-align: right;
      }
    }
    .total-container {
      margin: 0 0 60px;
      background-color: $ALSTOM_LIGHT_GREY;
    }
  }

  @include breakpoint($BREAKPOINT_MOBILE_MAX) {
    .resume-block {
      margin: 1rem;
      padding: 0.5rem;
    }
    .seller-table {
      .label-detail {
        display: none;
      }

      .product {
        margin: 5px 1rem !important;
      }

      td:nth-child(1) {
        margin-left: 0 !important;
      }

      td:not(:nth-child(1)) {
        margin-left: 7rem !important;
      }

      tbody .subtotal, .taxes, .subtotal-vat {
        padding: 0 1rem;
      }

      tbody tr {
        margin: 0 20px;
        padding-bottom: 20px;

        td {
          padding: 0;

          .product {
            padding: 0;
            margin: 0 !important;
          }

          .mobile-center {
            text-align: right;
          }
        }

        .desktop-only {
          padding: 0;
        }
      }
    }

    .Page-content .total-container {
      padding: 30px 0;
      margin-bottom: 0;
    }
  }
}
