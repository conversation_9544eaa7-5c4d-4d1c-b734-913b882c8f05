body[class*=" Page--offer_detail"] {

  .proforma {
    .Icon.stroke {
      fill: $WHITE;
      width: 17px;
      height: 17px;
      margin-right: 10px;
    }

    a {
      display: flex;
      align-items: center;
      padding: 10px 15px;
      height: 40px;
      margin: 0;
      justify-content: center;

      &:hover {
        .Icon.stroke {
          fill: $PRIMARY_COLOR;
        }
      }
    }
  }


  main.container {
    max-width: 100%;
    padding: 140px 0 0;

    @include breakpoint($BREAKPOINT_NOT_DESKTOP) {
      padding-top: 20px;
      margin-top: 49px;
      border-top: 1px solid $ALSTOM_LIGHT_GREY;
    }
  }

  input[type="number"] {
    -moz-appearance: textfield;
    &::-webkit-outer-spin-button,
    &::-webkit-inner-spin-button
    {
      -webkit-appearance: textfield;

    }
  }

  #js-contact-merchant-modal {
    .Modal-content {
      min-width: 350px;
    }

    .Modal-body {
      padding: 10px 20px 30px;

      #form_object {
        display: block;
        visibility: visible;
        opacity: 1;
        width: 100%;
        pointer-events: inherit;
        padding: 11px;
        position: relative;
        margin-bottom: 33px;
        border-color: $ALSTOM_LIGHT_GREY;
        font-size: 12px;
        color: $ALSTOM_DARK_GREY;
        box-shadow: 0px 0px 4px rgba($ALSTOM_GREY, 0.2);
      }

      textarea {
        border-color: $ALSTOM_LIGHT_GREY;
        padding: 20px;
        color: $ALSTOM_DARK_GREY;
        margin: 0;
      }
    }

    form {
      padding-top: 0;
    }

    input:not([type="file"]) {
      margin-top: 0;
      background: $WHITE;
      box-shadow: none;
    }
  }

  .spin {
    display: flex;
    flex-direction: column;

    .arrow {
      border-color: $ALSTOM_DARK_GREY;
    }
  }

  .back-to-search {
    margin: 66px 0 11px 10px;

    a {
      display: flex;
      align-items: center;
      color: $ALSTOM_GREY;
      font-size: 0.875rem;
      font-weight: 600;
      text-transform: uppercase;

      .arrow {
        border-color: $ALSTOM_GREY;
        margin-right: 10px;
      }
    }

    @include breakpoint($BREAKPOINT_NOT_DESKTOP) {
      margin: 20px 20px;
    }
  }

  .Offer-image {
    &-big, &-small {
      background-size: contain;
      background-repeat: no-repeat;
      background-position: center;
    }

    &-big {
      width: 271px;
      height: 271px;
      margin-bottom: 25px;
    }

    &-small {
      border: 1px solid $ALSTOM_GREY;
      opacity: 0.5;
      width: 70px;
      height: 70px;

      &.selected {
        opacity: 1;
      }
    }

    .small-pic-container {
      display: flex;
      justify-content: space-between;
    }

    @include breakpoint($BREAKPOINT_MOBILE_MAX) {
      display: none;
    }
  }

  .Offer-image-print {
    display: none;

    @media print {
      display: block;

      .sp-image {
        height: auto;
        max-height: 400px;
        margin: 0 auto 25px;
        width: 100%;
      }
    }
  }

  .Offer-image-mobile {
    display: none;

    @include breakpoint($BREAKPOINT_MOBILE_MAX) {
      display: block;
    }
  }

  .Offer-detail-slider {
    height: 215px;

    .sp-slides-container {
      height: 215px;
    }

    .sp-image {
      height: 215px;
    }

    .sp-button {
      width: 10px;
      height: 10px;
      border: 0;
      background-color: $ALSTOM_LIGHT_GREY;

      &.sp-selected-button {
        background-color: $ALSTOM_GREY;
      }
    }
  }

  .Offer-data {
    display: flex;
    flex-direction: column;
    flex-grow: 1;
    justify-content: space-between;
    margin: 0 30px;
    color: $ALSTOM_DARK_GREY;
    font-size: 0.9375rem;
    max-width: 570px;

    .Icon.stroke {
      fill: #FFF;
      stroke: #A4A7B3;
    }

    h1 {
      color: $BLACK;
      margin-top: 0.5rem;
    }

    &-ref, &-stock {
      text-transform: uppercase;
      letter-spacing: 1.2px;
      font-size: 0.8125rem;
      font-weight: 700;
      color: $ALSTOM_GREY;
    }


    &-details {
      display: flex;
      flex-direction: column;
      color: $ALSTOM_GREY;

      div {
        margin: 5px 0;
      }

      .details-value {
        color: $ALSTOM_DARK_GREY;
        font-weight: 600;
        margin: 0 10px 0 10px;

        a {
          color: $PRIMARY_COLOR;
        }
      }

      .ref {
        letter-spacing: 1px;
        font-weight: 600;
      }
    }

    .stock-block {
      display: flex;
      align-items: center;
    }

    .info-stock {
      width: 10px;
      height: 10px;
      border-radius: 5px;
      background-color: $ALSTOM_GREY;
      margin-right: 10px;
    }

    .info-delivery {
      font-weight: 400;
      color: $ALSTOM_GREY;

      .details-value {
        font-weight: 600;
        color: $ALSTOM_DARK_GREY;
      }
    }

    &-stock {
      margin: 20px 0;
    }

    &-stock.in-stock {
      color: $ALSTOM_GREEN;

      .info-stock {
        background-color: $ALSTOM_GREEN;
      }
    }

    &-description {
      font-size: 1rem;
      font-weight: 300;

      a {
        color: $ALSTOM_GREY;
        text-decoration: underline;
        font-size: 0.875rem;
        margin-bottom: 20px;

        &:hover {
          color: $ALSTOM_BLUE;
        }
      }
    }

    &-mycatalog-ref {
      a {
        color: $PRIMARY_COLOR;
      }

      .Icon {
        margin-bottom: -8px;
      }
    }

    &-seller {
      padding: 10px 0;
      display: flex;
      align-items: flex-end;
      justify-content: space-between;
    }

    &-footer {
      border-top: 1px solid $ALSTOM_LIGHT_GREY;
      border-bottom: 1px solid $ALSTOM_LIGHT_GREY;
      display: flex;
      align-items: center;
      justify-content: space-between;
      color: $ALSTOM_GREY;

      .ask-seller a {
        color: $ALSTOM_BLUE;
        text-decoration: underline;
        cursor: pointer;
      }

      .compare {
        display: flex;
        align-items: center;

        a {
          color: $ALSTOM_GREY;
          text-decoration: underline;
          font-weight: 300;
          display: flex;
          align-items: center;

          &:hover {
            color: $ALSTOM_BLUE;
          }
        }

        .Icon {
          fill: $ALSTOM_MIDDLE_BLUE;
          margin: 0 5px 0 0;
          width: 20px;
          height: 20px;
        }
      }

      .wishlist-link {
        display: flex;
        align-items: center;

        a {
          color: $ALSTOM_GREY;
          text-decoration: underline;
          font-weight: 300;
          display: flex;
          align-items: center;

          &:hover {
            color: $PRIMARY_COLOR;
          }
        }

        .Icon {
          stroke: $PRIMARY_COLOR;
          fill: $PRIMARY_COLOR;
          margin: 0 5px 0 0;
          width: 20px;
          height: 20px;
        }
      }
    }

    @include breakpoint($BREAKPOINT_NOT_DESKTOP) {
      margin: 50px 0 0;

      &-ref {
        display: none;
      }

      &-description p {
        margin-bottom: 30px;
      }

      &-details, &-footer {
        flex-direction: column;
        line-height: 30px;
        align-items: flex-start;

        .details-value {
          margin-left: 5px;
        }
      }

      &-footer {
        height: unset;
        padding: 20px 0;

        .ask-seller,
        .compare {
          margin: 5px 0;
          line-height: 30px;
        }

        .compare {
          margin-bottom: 15px;
        }
      }
    }
  }

  .Offer-price-cart {
    display: flex;
    flex-direction: column;

    border: 1px solid $ALSTOM_LIGHT_GREY;
    box-shadow: 0px 0px 4px rgba($ALSTOM_GREY, 0.2);
    padding: 15px;
    width: 270px;
    color: $ALSTOM_GREY;
    font-size: 1rem;

    &-unit {
      color: $ALSTOM_DARK_GREY;
    }

    &-unit-price {
      display: flex;
      color: $ALSTOM_BLUE;
      font-size: 1.875rem;
      font-weight: 600;
    }

    #unit-price {
      display: flex;
    }

    &-incoterm {
      text-transform: uppercase;
      letter-spacing: 1.2px;
      font-size: 0.8125rem;
      font-weight: 700;
    }

    &-discount-info {
      font-weight: 300;
      font-size: 0.875rem;
      border-top: 1px solid $ALSTOM_LIGHT_GREY;
      border-bottom: 1px solid $ALSTOM_LIGHT_GREY;
      margin: 15px 0 20px;
      padding: 10px 0 10px 5px;
      color: $ALSTOM_DARK_GREY;

      .discount-title, .discount-lines {
        display: flex;
        justify-content: space-between;
        align-items: center;
        color: $ALSTOM_DARK_GREY;
      }

      .discount-lines {
        flex-direction: column;
        color: $ALSTOM_GREY;
      }

      .line {
        display: flex;
        justify-content: space-between;
        width: 100%;
        margin-top: 10px;

        &-key {
          margin-left: 3px;
        }

        &-value {
          margin-right: 3px;
        }

        div {
          display: flex;
        }
      }

      #discount-plus, #discount-minus {
        width: 17px;
        height: 17px;
        position: relative;

        &:before, &:after {
          position: absolute;
          content: ' ';
          left: 8.5px;
          height: 17px;
          width: 1px;
          background-color: $ALSTOM_DARK_GREY;
        }

        &:before {
          transform: rotate(90deg);
        }
      }

      #discount-minus {
        &:after {
          content: none;
        }
      }
    }

    &-quantity-form {
      display: flex;
      flex-direction: column;
      justify-content: space-between;
      flex-grow: 1;

      font-size: 1rem;
      font-weight: 300;

      label {
        color: $ALSTOM_DARK_GREY;
        display: flex;
        align-items: center;
        justify-content: space-between;
        font-size: 1rem;
        font-weight: 300;
      }

      input[type=number] {
        border: 1px solid $ALSTOM_LIGHT_GREY;
        padding: 10px 0;
        width: 140px;
        height: 44px;
        color: $ALSTOM_DARK_GREY;
        font-weight: 300;
        text-align: center;

        &::-webkit-input-placeholder
        {
          color: $ALSTOM_GREY;
          font-weight: 300;
          font-style: italic;
        }
        &::-moz-placeholder {
          color: $ALSTOM_GREY;
          font-weight: 300;
          font-style: italic;
        }
        &:-ms-input-placeholder {
          color: $ALSTOM_GREY;
          font-weight: 300;
          font-style: italic;
        }
        &:-moz-placeholder {
          color: $ALSTOM_GREY;
          font-weight: 300;
          font-style: italic;
        }
      }

      .total-price {
        color: $ALSTOM_DARK_GREY;
        display: flex;
        justify-content: space-between;
        margin: 25px 0 15px;

        .price {
          display: flex;
          justify-content: center;
          color: $BLACK;
          font-weight: 400;
        }
      }

      button {
        margin: 0;
        width: 100%;
        text-transform: uppercase;
      }

      @include breakpoint($BREAKPOINT_NOT_DESKTOP) {
        @include breakpoint($BREAKPOINT_TABLET) {

          button {
            margin-top: 30px;
            width: auto;
          }

          .align-button-tablet {
            display: flex;
            justify-content: flex-end;
          }
        }
      }
    }

    @include breakpoint($BREAKPOINT_NOT_DESKTOP) {
      margin-top: 25px;
      width: 100%;
    }

    &.anonymous-display {
      height: 100%;
      display: flex;
      flex-direction: column;
      justify-content: space-between;
    }
  }

  .offer-container {
    max-width: 1200px;
    margin: 0 auto;
  }

  .offer-notice {
    width: 270px;

    p {
      border: 1px solid $ALSTOM_LIGHT_GREY;
      box-shadow: 0px 0px 4px rgba($ALSTOM_GREY, 0.2);
      padding: 15px;
      background-color: $ALSTOM_GREY_BACKGROUND;
      color: $ALSTOM_GREY;
    }
  }

  .Offer-detail-product {
    padding: 10px 20px;
    margin-bottom: 20px;
    display: flex;
    justify-content: space-between;

    @include breakpoint($BREAKPOINT_NOT_DESKTOP) {
      flex-wrap: wrap;
    }

    @include breakpoint($BREAKPOINT_MOBILE_MAX) {
      flex-direction: column;
      padding-top: 0px;
    }
  }

  .Offer-detail-info {
    background-color: $ALSTOM_GREY_BACKGROUND;
  }

  .Offer-detail-products {
    background-color: $ALSTOM_GREY_BACK_PRODUCT;
    padding: 35px 0 50px;

    &-list {
      padding: 10px 20px;

      @include breakpoint($BREAKPOINT_DESKTOP) {
        margin: auto;
      }

      @include breakpoint($BREAKPOINT_NOT_DESKTOP) {
        h2 {
          font-size: 1.5rem;
        }

        @include breakpoint($BREAKPOINT_MOBILE_MAX) {
          .Product {
            width: 100%;
            margin: 0 0 20px;
          }

          &.related-products {
            display: none;
          }
        }

        @include breakpoint($BREAKPOINT_TABLET) {

          .Product {
            width: 30%;
          }

          .Product-List-products {
            flex-direction: row;
            flex-wrap: wrap;
          }
        }
      }
    }
  }
}

.Modal--add {
  .Modal-body {
    padding: 10px 50px 20px 50px;
  }

  textarea.full_width {
    width: 100%;
    font-size: 14px;
    min-height: 120px;
  }
}

