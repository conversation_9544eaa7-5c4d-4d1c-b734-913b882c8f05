[class*="Page--dashboard_stats"] {
  background-color: $ALSTOM_GREY_BACKGROUND;

  @include breakpoint($BREAKPOINT_DESKTOP) {
    .side-container {
      display: flex;
    }
    .Page-inner {
      width: 100%;
      margin: 30px 0 60px;
      background-color: $WHITE;
      padding: 30px 40px;
    }

    .side-menu-container:hover {
      width: 370px;
    }
  }

  @include breakpoint($BREAKPOINT_NOT_DESKTOP) {
    .Page-inner {
      padding: 0 20px;
    }

    .stat-container {
      background-color: $WHITE;
      padding: 20px;
      margin-bottom: 40px;
    }

    .all_select {
      flex-direction: column;
    }
  }

  .year-select,
  .cost-center-select,
  .accrued-select {
    margin-right: 35px;
    flex-grow: 1;

    .select-wrapper__placeholder {
      margin-top: 10px;
    }

    .select-label {
      font-weight: 600;
      margin-bottom: 5px;
    }

    .stats-select-wrapper {
      padding: 13.5px 30px 13.5px 10px;
      border: 1px solid $ALSTOM_GREY;
      border-radius: 1px;
      background-color: $ALSTOM_GREY_BACKGROUND;
      background-image: url(../../images/arrow-select.png);
      background-position: calc(100% - 15px) 22px;
      background-repeat: no-repeat;

      select {
        -webkit-appearance:none;
        -moz-appearance: none;
        min-width: 150px;
        color: $ALSTOM_DARK_GREY;
        opacity: 1;
        position: relative;
        border: none;
        background-color: $ALSTOM_GREY_BACKGROUND;
        pointer-events: initial;
      }

      @include breakpoint($BREAKPOINT_NOT_DESKTOP) {
        width: 100%;

        select {
          width: 100%;
        }
      }
    }

    @include breakpoint($BREAKPOINT_NOT_DESKTOP) {
      margin: 0 0 20px;
    }
  }

  .all_select {
    width:100%;
    display: flex;
    margin-bottom: 20px;
  }

  .stat-container {
    display: flex;
    flex-direction: column;
  }

  #tdChart1, #tdChart2 {
    max-width: 600px;
    margin: 20px auto;
  }
}
