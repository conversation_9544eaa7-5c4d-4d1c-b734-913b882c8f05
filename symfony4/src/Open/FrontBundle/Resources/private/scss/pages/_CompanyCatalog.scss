
.Page--company_catalog {

  .Page-inner {
    margin-top: 40px !important;

    .infos-title {
      color: $ALSTOM_DARK_GREY;
      font-weight: 700;
      text-transform: uppercase;
      margin-bottom: 10px;
      letter-spacing: 1px;
    }

    .infos {
      color: $ALSTOM_DARK_GREY;
      font-weight: 300;

      a {
        color: $PRIMARY_COLOR;
        font-weight: 600;
        text-decoration: underline;
      }
    }

    .catalog-upload {
      label {
        background-color: $PRIMARY_COLOR;
        border: solid 2px $PRIMARY_COLOR;
        padding: 10px 20px;
        cursor: pointer;
        font-size: 14px;
        letter-spacing: 1px;
        color: $WHITE;

        &:hover {
          background-color: $WHITE;
          color: $PRIMARY_COLOR;
          border: solid 2px $PRIMARY_COLOR;
        }
      }

      a {
        text-decoration: none;
      }

      button {
        display: inline;
        margin: 5px 0;
        padding: 10px 20px;
      }


    }
  }

  @include breakpoint($BREAKPOINT_NOT_DESKTOP) {

    width: 100%;
    overflow: hidden;

    .Page-inner {
      padding: 0 20px;
      max-width: 100%;

      .catalog-upload label,
      .catalog-upload button{
        width: 100%;
      }
    }

    .catalog-upload {
      display: flex;
      flex-direction: column;

      label {
        margin: 5px 0;
        text-align: center;
      }
    }
  }
}
