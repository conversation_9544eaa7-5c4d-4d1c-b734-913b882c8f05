@import "../variables";

.Page--register, .Page--register__isRedirect_true,.Page--merchant_register, .Page--merchant_register_isRedirect_true, .Page--vendor_register, .Page--vendor_register_isRedirect_true {
  main.container {
    padding-top: $NAV_HEIGHT_WITH_SEARCHBAR;

    .Messages {
      margin-top: 60px;
    }

    @include breakpoint($BREAKPOINT_NOT_DESKTOP) {
      padding: 20px 20px 40px;
      background-color: $ALSTOM_GREY_BACKGROUND;
    }
  }

  .label-tooltip {
    z-index: 1;
    position: absolute;
    top: -33px;
    left: 0;
    display: flex;
    align-items: center;

    label {
      text-transform: uppercase;
      letter-spacing: 1px;
      color: #6B6F82;
      margin-bottom: 0;
    }

    .Icon {
      width: 18px;
      height: 18px;
    }

    .tooltiptext {
      top: 25px;
      left: 0px;
    }
  }
}

.register-background {
  background-image: url("../../images/landing-background.png");
  position: absolute;
  top: 170px;
  right: 0;

  @include breakpoint($BREAKPOINT_NOT_DESKTOP) {
    display: none;
  }
}

.form-register {
  max-width: 770px;
  margin: 0;
  position: relative;

  .primary {
    color: $PRIMARY_COLOR;
  }

  h1 {
    line-height: 35px;
    margin-bottom: 20px;
  }

  h4 {
    margin: 80px 0 -25px;
    font-size: 1.0625rem;
    font-weight: 600;
    color: $BLACK;
  }

  form {
    width: 100%;
  }

  p {
    font-size: 1rem;
    line-height: 1.72;
    font-weight: 300;
    color: $ALSTOM_DARK_GREY;
  }

  .form-title {
    p {
      max-width: 570px;
    }
  }

  .form-fields {
    display: block;
    width: 100%;

    .form-row {
      float: none;
      width: 100%;
      margin-right: 30px;
    }

    @include breakpoint($BREAKPOINT_DESKTOP) {
      display: flex;
      flex-wrap: wrap;
    }

    @include breakpoint($BREAKPOINT_NOT_DESKTOP) {
      .margin-mobile {
        margin-bottom: 55px;
      }
    }
  }

  .form-control {
    position: relative;
    box-sizing: border-box;
    height: auto;
    padding: 10px;
    font-size: 16px;
  }

  .form-control:focus {
    z-index: 2;
  }

  .CompanyIdentification .form-control-label::before {
    content: attr(title);
    position: absolute;
    right: 0;
    top: -15px;
    transform: translateX(130%);
    //display: none;
    opacity: 0;
    border: 2px solid black;
    background: white;
    border-radius: 5px;
    padding: 10px;
    white-space: nowrap;
    z-index: 100;

    transition: opacity 60ms;
    transition-delay: 0ms;

    box-shadow: 4px 4px 10px 0px rgba(161,158,161,0.6);
  }

  .CompanyIdentification input:hover + small + .form-control-label::before ,
  .CompanyIdentification input:hover + .form-control-label::before {
    opacity: 1;

    transition-delay: 900ms;
  }

  button {
    margin: 0 0 100px;
  }

  .merchant-ask-login {
    a {
      text-decoration: underline;
    }
  }

  .accept-terms {
    margin: 124px 0 35px;
    font-size: 0.875rem;

    @include breakpoint($BREAKPOINT_NOT_DESKTOP) {
      margin-bottom: 0;
    }
  }

  @include breakpoint($BREAKPOINT_DESKTOP) {

    .form-row,
    .form-fields .form-row {
      width: 270px;
    }
  }
}

body[class*='Page--resetting_check-email'] {
  .Page-inner {
    padding: 50px;
  }

  .Page-header {
    margin-bottom: 30px;
  }
}

body[class*='Page--resetting_reset'] {
  .Page-inner {
    padding: 30px 0px;
  }

  .Reset--confirm {
    button {
      margin: 0 0 60px;

      @include breakpoint($BREAKPOINT_MOBILE_MAX) {
        width: 100%;
        margin: 0;
      }
    }
  }
}

.password-help {
  position: relative;
  top: -26px;
  font-size: 11px;
}
