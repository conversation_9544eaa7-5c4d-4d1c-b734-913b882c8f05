@import "../variables";

body[class*="Page--comparisonSheet"] {
  background-color: $ALSTOM_GREY_BACKGROUND;

  main.container {
    padding-left: 0;
    padding-right: 0;
    max-width: 100%;
  }

  .title {
    margin: 40px 0 20px;
  }

  .name {
    margin-bottom: 0;

    a {
      color: $PRIMARY_COLOR;
    }
  }

  .Page-inner {
    display: flex;
    flex-direction: column;
    justify-content: center;

    @include breakpoint($BREAKPOINT_DESKTOP) {
      padding: 0 15px;
    }
  }

  .Page-content {
    width: 100%;
    max-width: 1200px;
    margin: 0 auto;
  }

  form .Button {
    margin: 20px 0 90px;

    @include breakpoint($BREAKPOINT_NOT_DESKTOP) {
      margin: 40px auto 90px;
    }
  }

  .comment-row {
    .comment-title {
      font-size: 0.875rem;
      font-weight: 400;
      margin-top: 20px;
      color: $ALSTOM_DARK_GREY;
    }

    textarea {
      border: 1px solid $ALSTOM_LIGHT_GREY;
      width: 50%;
      min-height: 120px;
      padding: 15px;
      color: $ALSTOM_DARK_GREY;
      font-weight: 300;
      margin: 10px 0 30px;
    }
  }

  @include breakpoint($BREAKPOINT_NOT_DESKTOP) {
    .title {
      width: 90%;
      margin: 20px auto;
      h1 {
        font-size: 1.5rem;
      }
    }

    .comment-row {
      width: 90%;
      margin: 0 auto;

      textarea {
        width: 100%;
      }
    }
  }

  .seller-table {
    width: 100%;
    tr {
      background-color: $WHITE;
      border: 1px solid $ALSTOM_LIGHT_GREY;
      td {
        padding: 15px;

        p {
          margin-bottom: 0;
        }

        strong {
          color: $ALSTOM_DARK_GREY;
        }

        & > div {
          display: flex;
        }
      }
    }

    .img-container {
      width: 140px;
      height: 140px;
      margin: 0 auto;
      overflow: hidden;
      display: flex;
      align-items: center;
      img {
        position: relative;
        width: auto;
        height: 100%;
        left: 50%;
        transform: translateX(-50%);
      }
    }

    .Product-info {
      .details {
        color: $ALSTOM_GREY;
        .details-value {
          color: $ALSTOM_DARK_GREY;
          font-weight: bold;
        }
      }
    }

    .detail-content {
      display: flex;
      flex-direction: column;

      .incoterm {
        color: $ALSTOM_DARK_GREY;
        font-size: 14px;
        font-weight: 700;
      }

      @include breakpoint($BREAKPOINT_NOT_DESKTOP) {
        flex-direction: row;

        .details-align {
          display: flex;
          flex-direction: column;
          align-items: flex-end;
        }
      }
    }

    .price-info {
      color: $ALSTOM_DARK_GREY;
      font-weight: bold;

    }

    .details {
      color: $ALSTOM_DARK_GREY;
      font-weight: 300;
    }

    .details-value {
      font-weight: 400;
      letter-spacing: 0.5px;
      color: $ALSTOM_DARK_GREY;
    }

    .details-title {
      font-weight: 700;
      color: $ALSTOM_DARK_GREY;
      justify-content: center;
    }

    .details-price {
      color: $ALSTOM_DARK_GREY;
      font-weight: 400;
      display: flex;

      .details-value {
        color: $ALSTOM_DARK_GREY;
      }
    }

    .align-unit-price {
      display: flex;
      @include breakpoint($BREAKPOINT_DESKTOP) {
        justify-content: flex-end;
      }
    }

    .Icon {
      width: 18px;
      height: 18px;
    }

    .trash {
      display: block;
    }

    .tresholds {
      display: flex;
      flex-direction: column;
      justify-content: flex-end;

      div {
        display: flex;
        @include breakpoint($BREAKPOINT_NOT_DESKTOP) {
          justify-content: space-between;
        }
      }

      @include breakpoint($BREAKPOINT_DESKTOP) {
        align-items: flex-end;
      }
    }

    @include breakpoint($BREAKPOINT_DESKTOP) {
      .delivery-block {
        text-align: center;
        div {
          display: flex;
          justify-content: center;
        }
      }

      .align-end-price {
        justify-content: flex-end;
      }

      .img-td {
        width: 200px;
      }
    }

    @include breakpoint($BREAKPOINT_NOT_DESKTOP) {
      width: 90%;
      margin: 0 auto;
      tr {
        display: block;
        td {
          display: flex;
          flex-direction: column;
          width: 100%;
          padding: 0 15px;

          & > div {
            display: flex;
            flex-direction: row;
            justify-content: space-between;
            width: 100%;
          }
        }
      }

      .underline {
        position: relative;
        padding: 15px;
      }

      .price-info, .Product-info {
        color: $ALSTOM_DARK_GREY;
        font-weight: bold;

      }

      .remove-item {
        position: absolute;
        right: 15px;
        top: 15px;

        .Icon {
          margin: 0;
        }
      }
    }
  }

  .empty-comparison {
    background-color: $WHITE;
    text-align: center;
    display: flex;
    flex-direction: column;
    margin-top: -20px;

    h1 {
      font-size: 1.875rem;
      margin-top: 75px;
    }

    a {
      font-size: 1rem;
      text-decoration: underline;

      &:hover {
        color: $ALSTOM_GREY;
      }
    }

    .cart-empty-img {
      width: 830px;
      height: auto;
      margin: 25px auto 60px;
    }

    @include breakpoint($BREAKPOINT_NOT_DESKTOP) {
      padding: 0 15px;
      background-color: $ALSTOM_GREY_BACKGROUND;
    }
  }
}
