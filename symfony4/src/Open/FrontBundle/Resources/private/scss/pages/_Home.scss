@import "../_variables";

.ie11 {
  body[class*=" Page--homepage"] {
    .BuyerButton,
    .MerchantButton {
      margin-top: 70px;
      margin-bottom: 0;
    }
  }
}

body[class*=" Page--homepage"] {

  .Home-icon-section {
    max-width: $SITE_WIDTH_WIDE;
    display: flex;
    width: 90%;
    background-color: white;
    padding: 60px 0;
    margin: 0 auto;
    justify-content: space-between;

    @include breakpoint($BREAKPOINT_NOT_DESKTOP) {
      flex-direction: column;
    }

    .Home--col {
      display: flex;
      flex-direction: column;
      @include breakpoint($BREAKPOINT_DESKTOP) {
        width: 40%;
      }
      @include breakpoint($BREAKPOINT_NOT_DESKTOP) {
        width: 80%;
        margin: auto;
      }

    }

    .Section-icon-row {
      width: 100%;
      display: flex;
      justify-content: space-around;

      @include breakpoint($BREAKPOINT_DESKTOP) {
        margin-top: 70px;
      }
      @include breakpoint($BREAKPOINT_NOT_DESKTOP) {
        margin-top: 40px;
      }

      .button {
        -webkit-box-shadow: 5px 5px 15px 0px rgba(49, 49, 49, 0.29);
        box-shadow: 5px 5px 15px 0px rgba(49, 49, 49, 0.29);
      }

      .Home--icon:first-child .Home--icon--image, .Home--icon:first-child .Home--icon--label {
        justify-content: left;
      }

      .Home--icon:nth-child(2) .Home--icon--image, .Home--icon:nth-child(2) .Home--icon--label {
        justify-content: right;
      }
    }

    .Section-icon-row:first-child {
      order: 1;
    }

    .Section-icon-row:nth-child(3) {
      @include breakpoint($BREAKPOINT_DESKTOP) {
        order: 3;
      }
      @include breakpoint($BREAKPOINT_NOT_DESKTOP) {
        order: 2;
      }
    }

    .Section-buyer-register-start, .Section-merchant-register-start {
      @include breakpoint($BREAKPOINT_DESKTOP) {
        order: 2;
      }
      @include breakpoint($BREAKPOINT_NOT_DESKTOP) {
        order: 3;
      }

    }

    .Section-merchant-register-start .button {
      background-color: $APPLI_DARK_YELLOW;
      border: 1px solid $APPLI_DARK_YELLOW
    }

    .Home--icon {
      width: 48%;
      display: flex;
      justify-content: space-around;
      flex-direction: column;

      &--image {
        width: 100%;
        display: flex;
        /*justify-content: space-around;*/
      }

      &--label {
        color: $PRIMARY_COLOR;
        width: 100%;
        display: flex;
        justify-content: space-around;
        margin-top: 15px;
        font-size: 18px;
      }
    }

  }
}
