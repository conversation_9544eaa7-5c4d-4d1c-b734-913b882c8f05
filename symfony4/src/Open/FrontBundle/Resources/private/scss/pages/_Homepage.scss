@import "../_variables";


body[class*=" Page--homepage"] {

  main {
    max-width: 100%;
    padding: 50px 0 0 0;
    background-color: $ALSTOM_GREY_BACKGROUND;
  }

  .Messages {
    position: fixed;
    width: 80%;
    z-index: 1000;
    left: 10%;

    .Message-item:first-of-type {
      margin-top: 60px;
    }
  }

  .Homepage {

    &-downArrow {
      position: fixed;
      bottom: 60px;
      left: 50%;
      transform: translateX(-50%);
      z-index: 100;
      cursor: pointer;
      svg {
        fill: $WHITE;
        width: 50px;
        height: 50px;
      }

      &:hover {
        svg {
          fill: $APPLI_DARK_YELLOW;
          transition: fill 100ms linear;
        }
      }

      @include breakpoint($BREAKPOINT_SMALLER_MOBILE) {
        bottom: 40px;
      }
    }

    &-slider {
      display: none;

      .sp-slides-container {
        margin-top: 60px;
      }

      .Slide-title {
        color: $WHITE;
        font-size: 3em;
        text-align: left;
      }

      .Slide-subTitle {
        color: $WHITE;
        font-size: 2.1em;
        margin-top: 10px;
        opacity: 1;
        text-align: left;
        font-family: 'HKNova', 'Poppins', sans-serif;
        font-weight: 400;
      }

      .Slide-button {
        margin: 0 auto;
        text-transform: uppercase;
      }

      .sp-buttons {
        position: absolute;
        bottom: 10px;
        text-align: center;

        .sp-button {
          background-color: $ALSTOM_GREY;
          border: 2px solid $ALSTOM_GREY;
          width: 12px;
          height: 12px;
        }

        .sp-button.sp-selected-button {
          background-color: $PRIMARY_COLOR;
          border: 2px solid $PRIMARY_COLOR;
        }
      }

      .sp-image:not([data-source]) {
        opacity: 1;
        transition: opacity 0.5s;
      }
      .sp-image[data-source] {
        opacity: 0;
      }

      .Slide-register-action {
        display: flex;
      }

      #Header-SearchBar {
        position: absolute;
        width: 60%;
        min-width: 700px;
        top: auto;
        bottom: 15%;
        background-color: transparent;
        padding: 0;
        z-index: 100;
        margin: 0 auto;
        left: 0;
        right: 0;

        #Search-bar {
          height: 70px;
          border: none;

          .input-text {
            margin-top: 1.3rem;
          }

          .element-button {
            width: auto;
            padding: 0 40px;
            margin-right: 10px;
          }

          .advanced-search {
            margin-top: 10px;
            a {
              font-size: 0.875rem;
              color: $WHITE;
              opacity: 0.7;
              text-decoration: underline;

              &:hover {
                color: $ALSTOM_BLUE;
              }
            }
          }
          form .select-wrapper .select-wrapper__placeholder {
            padding-right: 25px;
          }
        }

        button {
          padding: 0;
        }
      }

    }

    &-mobileSlider {
      display: block;
      max-height: 500px;
      overflow: hidden;

      .Slide-title {
        margin-top: 0;
        font-size: 2.9em;
        text-align: center;
      }

      .Slide-subTitle {
        margin-bottom: 40px;
        text-align: center;
      }

      .Slide-button {
        width: 95%;
        max-width: 300px;
        font-size: 18px;
        margin-bottom: 30px
      }

      .Slide-buttonVideo {
        margin-left: 5%;
      }

      .sp-button {
        width: 15px;
        height: 15px;
      }

      @include breakpoint($BREAKPOINT_NOT_DESKTOP) {
        .sp-layer {
          height: 450px;
          display: flex;
          flex-direction: column;
          justify-content: center;

          p {
            text-align: center;
          }

          @include breakpoint($BREAKPOINT_MOBILE_MAX) {
            height: 380px;
          }

          @include breakpoint($BREAKPOINT_SMALLER_MOBILE) {
            height: 240px;
          }
        }
      }

      @include breakpoint($BREAKPOINT_MOBILE_MAX) {
        .Slide-title {
          font-size: 1.5625rem;
          margin-bottom: 0;
          text-align: center;
        }
        .Slide-subTitle {
          font-size: 1.25rem;
          margin: 20px 0;
          text-align: center;
        }
        .Slide-button {
          padding: 10px 20px;
          margin-bottom: 0;
        }

        .Slide-buttonVideo {
          width: 64px;
          height: 64px;
          margin-left: 15%;
          margin-top: -10px;
          transform: translateY(-15px);
          border: 3px solid $APPLI_GREEN;

          &:after {
            top: 100%;
            left: 0;
            right: 50%;
            font-size: 16px;
            transform: translate(-70%, 10px);
          }
        }
      }

      @include breakpoint($BREAKPOINT_SMALLER_MOBILE) {
        max-height: 300px;
      }


    }

    &--products {
      color: $BLACK;
      display: flex;
      flex-wrap: wrap;
      flex-direction: column;
      justify-content: flex-start;
      max-width: 1200px;
      padding: 30px 15px 38px;
      margin: 0 auto;

      h2 {
        margin-bottom: 20px;
        color: $BLACK;
        font-weight: 600;
        width: 100%;
        font-size: 1.5rem;
        padding: 0 10px 0 0;
      }

      .underlined-title {
        color: $ALSTOM_RED;
        padding-bottom: 5px;
        border-bottom: 1px solid $BLACK;
      }

      .Product {
        width: 100%;
        margin: 0 0 10px;
        &-compare {
          display: none;
        }
      }

      p {
        color: $BLACK;
        margin-bottom: 20px;
        padding: 0 10px 0 10px;
      }

      &--allOffers {
        width: 100%;
        display: flex;
        margin-top: 20px;

        .button {
          min-width: 230px;
        }
      }

      @include breakpoint($BREAKPOINT_TABLET) {
        flex-direction: row;

        .Product {
          width: 32%;
          margin: 0 5px 10px;
        }
      }

      @include breakpoint($BREAKPOINT_DESKTOP) {
        padding: 100px 0;
        padding: 30px 0 38px;

        h2 {
          padding-left: 15px;
        }

        .Product {
          width: 24%;
        }
      }
    }

    &-why-buy {
      max-width: 1200px;
      margin: 10px auto 0;
      padding-bottom: 100px;
      text-align: center;

      h2 {
        color: $BLACK;
        font-size: 1.125rem;
        margin: 30px 0;
        font-weight: 500;
      }

      p {
        color: $ALSTOM_GREY;
        font-size: 1rem;
        font-weight: 300;
        line-height: 25px;
        max-width: 330px;
      }

      .why-container {
        display: flex;
        justify-content: space-between;

        @include breakpoint($BREAKPOINT_MOBILE_MAX) {
          flex-direction: column;
        }
      }

      .why-bloc {
        margin-top: 55px;
        display: flex;
        flex-direction: column;
        align-items: center;
        width: 33%;

        .Icon {
          cursor: initial;
        }

        @include breakpoint($BREAKPOINT_MOBILE_MAX) {
          margin-top: 25px;
          width: 100%;
        }
      }

      @include breakpoint($BREAKPOINT_SMALLER_MOBILE) {
        padding-bottom: 30px;

        h2 {
          margin: 20px 0;
        }
      }
    }
  }

  #SearchBar--Page--homepage {
    display: none;
  }

  #Header-navigation {
    top: $NAV_USER_HEIGHT;
  }

  @include breakpoint($BREAKPOINT_DESKTOP) {

    main {
      padding-top: $NAV_USER_HEIGHT;
    }

    .Homepage {

      &-mobileSlider {
        display: none !important;
      }

      &-slider {
        display: block;

        p {
          text-align: center;
        }

        .Slide-title {
          font-size: 2.375rem;
          line-height: 2.625rem;
          text-align: center;
          max-width: 70%;
          margin: auto;
          white-space: normal;
        }

        .Slide-subTitle {
          font-size: 1.375rem;
          margin-top: 10px;
          text-align: center;
        }

        .Slide-register-action {
          display: flex;
        }
      }
    }

  }


}
