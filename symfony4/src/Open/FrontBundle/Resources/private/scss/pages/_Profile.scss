@import "../modules/ProfileForm";
@import "../variables";

body[class*=" Page--profile"] {
  background: $WHITE;

  .Page-inner {
    margin: 0 auto;
  }

  .ProfileIndex {
    padding: 10px;


    .Icon {
      fill: $TEXT_DEFAULT;
    }

    .Icon--plus {
      fill: $APPLI_GREEN;
    }

    &-title {
      text-align: center;
      font-size: 1.8em;
    }

    h3 {
      font-size: 1em;
      margin-top: 0; // hack dégeu
      margin-left: 1px;
      font-style: italic;
    }

    &-sectionTitle {
      margin-bottom: 0;
      margin-top: 40px;
    }

    &-account {
      margin-top: 70px;

      a:hover .Icon {
        fill: $APPLI_GREEN;
      }

      .Icon {
        fill: $TEXT_DEFAULT; // override .Page-inner h2 .Icon
      }
    }

    &-orders {
      margin-top: 50px;

      a:hover .Form-label {
        color: $APPLI_GREEN;
        cursor: pointer
      }

      .Form-label {
        width: 100%;
        display: flex;
        justify-content: space-between;
        margin-right: 0; //not sure why ?
      }

      .ProfileIndex-ordersItem {
        padding-right: 5%;
        text-overflow: ellipsis;
        flex-grow: 1;
        white-space: nowrap;
        overflow: hidden;
      }

      .ProfileIndex-ordersPrice {
        text-align: right;
        //margin-right: 10px;
      }

      .ProfileIndex-ordersDate {
        margin-left: 10px;
      }

      @include breakpoint($BREAKPOINT_DESKTOP) {

        .ProfileIndex-ordersItem {
          float: none;
        }

        .ProfileIndex-ordersPrice {
          float: none;
          flex-shrink: 1;
          //margin-right: 10px;
        }


        .ProfileIndex-ordersDate {
          float: right;
          display: inline;
          flex-shrink: 1;
        }

      }
    }



    &-contact {
      text-align: right;
      padding-top: 20px;

      span {
        line-height: 32px; // same size as the icon
        margin-right: 10px;
        font-style: italic;
      }

      a {
        display: block !important; // override display: flex;
        text-decoration: none;
        color : $TEXT_DEFAULT;

        &:hover {
          color: $APPLI_GREEN;
        }
      }

      .Icon {
        float: right;
      }
    }

    .Form-group {
      margin-top: 20px;
      padding-bottom: 5px;

      a {
        display: block;
        width: 100%;
        text-decoration: none;

        &:hover .Icon {
          fill: $APPLI_GREEN;
        }

        .Icon {
          float: right;
        }
      }

      a:hover .Form-label {
        cursor: pointer;
      }

      .Form-label:after {
        display: none;
      }
    }

    .Form-label {
      small {
        font-size: .6em;
        color: $TEXT_DEFAULT;
        font-style:italic;
      }
    }

    .Icon {
      margin-right: 10px;
    }
  }


  .Profile {

    &-sections {
      margin: 0;
      padding: 0;
      list-style-type: none;
      border: 1px solid $GRAY_DARKEST;

      li {
        background-color: $GRAY_DARKER;
        padding: 20px;
        border-bottom: 1px solid $GRAY_DARKEST;
        cursor: pointer;
        position: relative;

        a {
          color: $BLACK;

          &:hover {
            //color: $APPLI_GREEN;
            .Icon {
              fill: $APPLI_GREEN;
            }
          }
        }

        span {
          width: 69%;
          display: inline-block;
          font-weight: bold;
          //margin-bottom: 10px;

          small {
            display: block;
            font-weight: normal;
          }
        }

        button {
          position: absolute;
          right: 20px;
          top: 50%;
          transform: translate(0, -50%);
          text-transform: uppercase;
        }

        .Icon {
          //float: right;
          position: absolute;
          right: 20px;
          top: 50%;
          transform: translate(0, -16px);
          fill: $GRAY_DARKEST;
        }

        &:last-of-type {
          border: 0;
        }
      }

      .Profile-sectionSite span small,
      .Profile-sectionUser.Profile--emailConfirm span small {
        display: inline;
        color: dodgerblue;
      }

      .Profile-sectionSite,
      .Profile-sectionUser {
        background: $GRAY_LIGHTER;
      }

      .Section--fullWidth {
        width: 100%;
      }

    }

    &-contact {
      text-align: right;
      margin-top: 10px;

      a {
        text-decoration: none;
        color: $GRAY_DARKEST;

        &:hover {
          color: $APPLI_GREEN;

          .Icon {
            fill: $APPLI_GREEN;
          }
        }
      }

      span {
        float: right;
        margin-top: 7px;
        margin-right: 7px;
      }

      .Icon {
        float: right;
        fill: $GRAY_DARKEST;
      }
    }

  }

  @include breakpoint($BREAKPOINT_SMALLER_MOBILE) {
    .Page-inner {

      .ProfileIndex-orders {
        .ProfileIndex-ordersItem {
          max-width: 54%;
        }
        .ProfileIndex-ordersPrice {
          flex-grow: 1;
        }

      }

      h2 {
        span {
          font-size: 28px;
        }
      }
    }
  }

  @include breakpoint($BREAKPOINT_TABLET) {
    background: $APPLI_GRAY;

    .Profile {
      &-sections {
        .SectionStatus {
          margin-top: 8px;
          margin-right: 10px;
        }
      }
    }
  }

  @include breakpoint($BREAKPOINT_DESKTOP) {
    .ProfileIndex {
      padding: 0;
    }
  }

}

// Override the greedy rule @ top of this file
/*body.Page--profile {
  .Page-inner {
    max-width: $SITE_WIDTH;
  }
}*/


