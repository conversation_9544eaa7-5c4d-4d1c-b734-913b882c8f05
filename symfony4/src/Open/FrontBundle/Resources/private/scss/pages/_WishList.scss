@import "../_variables";

body[class*=" Page--wishlist"]{
  background-color: $ALSTOM_GREY_BACKGROUND;

  .side-menu-container:hover {
    width: 370px;
  }

  .top-wishlist {
    display: flex;
    justify-content: space-between;

    button {
      margin: 0;
      height: 65px;
    }

    @include breakpoint($BREAKPOINT_NOT_DESKTOP) {
      flex-direction: column;

      button {
        width: 90%;
        margin: 10px auto 20px;
      }
    }
  }

  .previous-page {
    display: flex;
    align-items: center;
    color: $ALSTOM_GREY;
    font-size: 1rem;
    font-weight: 600;
    margin: 1rem 0;

    .arrow {
      margin-right: 0.5rem;
    }

    @include breakpoint($BREAKPOINT_NOT_DESKTOP) {
      width: 90%;
      margin: auto;
    }
  }

  .wishlist-title {
    margin: 30px 0 10px;

    @include breakpoint($BREAKPOINT_NOT_DESKTOP) {
      width: 90%;
      margin: 30px auto 10px;
    }
  }

  .side-container {
    display: flex;

    .Page-inner {
      margin-top: 30px;
      width: 100%;
    }

    @include breakpoint($BREAKPOINT_NOT_DESKTOP) {
      flex-direction: column;

      .Page-inner {
        margin-top: 0px;
      }
    }
  }

  .wishlist-container {
    width: 100%;

    tr.invalid {
      background-color: $ERROR_TRANSPARENT;
    }

    .wishlist {
      width: 100%;
      background-color: $WHITE;
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 30px;
      border-bottom: 1px solid $ALSTOM_LIGHT_GREY;

      .details {
        h1 {
          margin-bottom: 0;
        }

        .img-container {
          width: 100px;
          height: 100px;
          overflow: hidden;
          margin-right: 15px;

          img {
            position: relative;
            width: auto;
            height: auto;
            max-height: 100px;
            left: 50%;
            -webkit-transform: translateX(-50%);
            transform: translateX(-50%);
          }
        }

        .product-details {
          display: flex;
          flex-direction: column;
          justify-content: space-between;
          min-width: 300px;

          .Product-incoterm {
            margin-top: 2px;
            color: $ALSTOM_GREY;
          }

          @include breakpoint($BREAKPOINT_NOT_DESKTOP) {
            min-width: 0;
          }
        }

        @include breakpoint($BREAKPOINT_NOT_DESKTOP) {
          width: 80%;
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: nowrap;

          .actions .Icon {
            width: 30px;
            height: 30px;
            margin: -2px 0 0;
          }

          h1 {
            font-size: 1.25rem;
            text-overflow: ellipsis;
            overflow: hidden;
          }
        }
      }

      .actions .Icon {
        width: 25px;
        height: 25px;
        fill: $ALSTOM_GREY;
      }

      a {
        color: $BLACK;
        &:hover {
          color: $PRIMARY_COLOR;
          text-decoration: none;
        }
      }

      .Icon:hover {
        fill: $PRIMARY_COLOR;
      }

      .button-align {
        display: flex;
        justify-content: flex-end;
        padding-right: 40px;
      }

      button {
        width: 25px;
        height: 25px;
        padding: 0;
        border: none;
        background-color: transparent;
        pointer-events: visible;
        margin: 0;

        &:hover {
          background-color: $WHITE;
        }
      }
    }

    thead {
      background-color: $WHITE;
      color: $ALSTOM_GREY;
      border-bottom: 1px solid $ALSTOM_LIGHT_GREY;

      th {
        padding: 10px 0;
        font-size: 0.875rem;
        font-weight: 400;
        text-align: center;

        &:first-child {
          padding-left: 20px;
          text-align: left;
        }
      }
    }

    .quantity {
      text-align: center;
      padding: 0;

      .save-quantity {
        display: none;
      }

      input {
        width: 150px;
        text-align: center;
        padding-left: 0;
      }

      @include breakpoint($BREAKPOINT_NOT_DESKTOP) {
        margin: 15px 0;
        display: flex;
        width: 100%;
        justify-content: space-between;
        align-items: center;
        color: $ALSTOM_DARK_GREY;
        font-weight: 300;
        font-size: 0.875rem;

        input {
          margin: 0;
        }
      }
    }

    .delivery-time,
    .unit-price,
    .total-price {
      text-align: center;
      color: $ALSTOM_DARK_GREY;
      font-weight: 300;
      font-size: 0.875rem;

      @include breakpoint($BREAKPOINT_NOT_DESKTOP) {
        display: flex;
        justify-content: space-between;
      }
    }

    .wishlist-row {

      display: table-row;

      .details {
        display: table-cell;
        padding: 20px;
      }

      @include breakpoint($BREAKPOINT_NOT_DESKTOP) {
        display: flex;
        flex-direction: column;

        td {
          display: flex;
          width: 100%;
          padding: 0;
        }

        .details {
          padding: 0;
          margin-bottom: 15px;
        }
      }
    }

    @include breakpoint($BREAKPOINT_NOT_DESKTOP) {
      display: block;

      tbody {
        display: block;
      }
    }
  }

  .wishlist-offer-list {
    h3 {
      margin-bottom: 0;
    }

    .details {
      align-content: center;
      justify-content: space-between;

      .product {
        display: flex;
      }

      .Product-reference {
        color: $ALSTOM_GREY;
      }
    }
  }

  .wishlist-total-container {
    background-color: $ALSTOM_LIGHT_GREY;
    font-weight: 600;

    td {
      text-align: center;
      padding: 40px 0;
    }

    .delivery-total {
      padding: 20px 0 30px;
      color: $ALSTOM_DARK_GREY;

      .title-delivery {
        margin-bottom: 15px;
        text-align: left;
      }
    }

    @include breakpoint($BREAKPOINT_NOT_DESKTOP) {
      width: 100%;
      display: flex;
      flex-direction: column;
      justify-content: space-between;
      font-size: 1rem;

      td {
        padding: 10px 30px 40px;
        display: flex;
        justify-content: space-between;
      }

      .delivery-total {
        padding: 40px 30px 10px;
      }
    }
  }

  .wishlist-no-items {
    width: 100%;
    padding: 20px;
    background-color: $WHITE;
  }

  .empty-wishlist {
    background-color: $WHITE;
    text-align: center;
    display: flex;
    flex-direction: column;

    h1 {
      font-size: 1.875rem;
      margin-top: 75px;
    }

    .cart-empty-img {
      width: 765px;
      height: auto;
      margin: 25px auto 60px;
    }

    @include breakpoint($BREAKPOINT_NOT_DESKTOP) {
      padding: 0 15px;
      background-color: $ALSTOM_GREY_BACKGROUND;

      h1 {
        font-size: 1rem;
        margin-top: 15px;
      }
    }
  }
}
