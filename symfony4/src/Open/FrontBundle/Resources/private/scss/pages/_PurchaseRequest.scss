.Page--purchase-request {
  @include breakpoint($BREAKPOINT_NOT_DESKTOP) {
    .side-container {
      .Page-inner {
        padding: 0 20px;
      }
    }
  }
  @include breakpoint($BREAKPOINT_DESKTOP) {
    .side-container {
      display: flex;

      .Page-inner {
        flex: 1;
        margin-top: 22px;
      }
    }
  }
  .no-import {
    @include breakpoint($BREAKPOINT_DESKTOP) {
      padding: 50px;
      margin: 50px 0;
    }
    @include breakpoint($BREAKPOINT_NOT_DESKTOP) {
      padding: 10px;
      margin: 10px 0;
    }
    background: #fff;
    text-align: center;
    h1 {
      font-size: 40px;
    }
  }
  .btn.import {
    margin: 0;
    svg {
      display: inline-block;
      width: 12px;
      margin-right: 10px;
      vertical-align: sub;
      fill: #FFFFFF;
    }
    &:hover svg {
      fill: #9600FF;
    }
  }
  input.error {
    color: #f60e0e;
    border: 1px solid #f60e0e;
  }
  .loading {
    background: url("/images/loader.gif") no-repeat center;
    width: 33px;
    height: 33px;
    background-size: 70%;
    span {
      display: none;
    }
  }
  .progress {
    @include breakpoint($BREAKPOINT_NOT_DESKTOP) {
      margin-bottom: 10px;
    }
    height: 16px;
    padding: 2px;
    border-radius: 8px;
    box-shadow: inset 0 1px 1px 0 rgba(0, 0, 0, 0.29);
    border: solid 1px #dcdada;
    background-color: #e5e5e5;
    overflow: hidden;
    @include breakpoint($BREAKPOINT_DESKTOP) {
      width: 256px;
      display: inline-block;
      margin-left: 20px;
    }
    position: relative;
    .progress-bar {
      height: 11px;
      border-radius: 11px;
      background-image: linear-gradient(to bottom, #aa34fd 8%, #9600ff 100%);
      transition: width linear 0.2s;
    }
    .progress-info {
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      text-align: center;
      font-size: 9px;
      color: #fff;
      line-height: 16px;
    }
  }
  form {
    margin: 0;
    padding: 0;
  }
  .btn:disabled,
  .btn[disabled] {
    cursor: not-allowed;
    background-color: #d5d8e3;
    color: #fff;
    border-color: #d5d8e3;
  }
  .btn-sm {
    padding: 0px 5px;
    margin: 0;
    display: inline-block;
    line-height: 11px;
  }
  .product-remove-cart {
    border: none;
    background-color: transparent;
    padding: 0;
    margin: 0;
    color: #3d3b3b;
    text-transform: uppercase;
    text-decoration: underline;
  }
  .page-wrap {
    background: #f4f5fa;
    .purchase-header {
      @include breakpoint($BREAKPOINT_DESKTOP) {
        display: flex;
      }
      h1 {
        @include breakpoint($BREAKPOINT_DESKTOP) {
          flex: 1;
        }
        margin: 0;
      }
      .btn-container {
        @include breakpoint($BREAKPOINT_DESKTOP) {
          display: flex;
        }
        .btn {
          font-family: 'OpenSans', 'Poppins', sans-serif;
          @include breakpoint($BREAKPOINT_DESKTOP) {
            margin-left: 15px;
          }
          text-transform: uppercase;
          padding: 5px 15px;
          &.grey {
            background-color: #b3b6c0;
            border-color: #b3b6c0;
            &:hover {
              background-color: #fff;
              border-color: #b3b6c0;
              color: #333;
            }
          }
        }
      }
    }
    .products-list {
      padding: 5px 0;
      display: flex;
      flex-direction: column;
      .card {
        box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.12);
        background-color: #ffffff;
        padding: 15px 30px;
        margin: 5px 0;
        position: relative;
        .purchase-product-ref {
          font-weight: bold;
          flex: 1;
        }
        .more {
          text-transform: uppercase;
          font-weight: bold;
          text-align: center;
          width: 100%;
          &.loading {
            background: url("/images/loader.gif") no-repeat center;
            button {
              display: none;
            }
          }
          button {
            background: transparent;
            border: none;
            color: #6b6f82;
            span {
              display: block;
              position: relative;
              margin: 0 auto 10px;
              height: 24px;
              width: 24px;
              &::after,
              &::before {
                content: '';
                display: block;
                background: #9600FF;
                position: absolute;
                top: 10px;
                height: 4px;
                left: 0;
                right: 0;
              }
              &::after {
                transform: rotate(90deg);
              }
            }
          }
        }
        .products {
          @include breakpoint($BREAKPOINT_DESKTOP) {
            display: flex;
            flex-wrap: wrap;
          }
          margin: 0 -10px;
          .product {
            box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.12);
            background-color: #ffffff;
            padding: 10px;
            @include breakpoint($BREAKPOINT_DESKTOP) {
              flex: 1 1 calc(25% - 20px);
              max-width: calc(25% - 20px);
            }
            margin: 10px;
            &.selected,
            &:hover {
              border: 1px solid #9600FF;
              padding: 9px !important;
            }
            .img {
              background-size: contain;
              padding-bottom: 65%;
              background-position: center;
              background-repeat: no-repeat;
            }
            &-ref {
              font-weight: bold;
              color: #a4a7b3;
              font-size: 12px;
            }
            &-title {
              overflow: hidden;
              display: -webkit-box;
              -webkit-line-clamp: 2;
              -webkit-box-orient: vertical;
              color: #032639;
              font-weight: bold;
              font-size: 18px;
            }
            &-info {
              color: #6b6f82;
              font-weight: bold;
              span {
                color: #a4a7b3;
                font-weight: normal;
              }
            }
            &-selector {
              input {
                display: none;
              }
              label {
                position: relative;
                height: 16px;
                width: 16px;
                border: solid 0.5px #e9e6e6;
                background-color: #f4f5fa;
                display: block;
                border-radius: 50%;
                margin: 0;
                &::after {
                  content: "";
                  display: block;
                  position: absolute;
                  height: 8px;
                  width: 8px;
                  top: 3px;
                  left: 3px;
                  border-radius: 50%;
                }
              }
              input:checked + label {
                &::after {
                  background: #9600FF;
                }
              }
            }
          }
        }
        &.no-product {
          order: 1;
          color: #a7aeb5 !important;
          .card-collapse-container {
            display: none;
          }
          .card-collapse-toggle {
            opacity: 0;
          }
          .product-remove-cart {
            display: none;
          }
        }
        .card-title {
          display: flex;
          .card-title-content {
            flex: 1;
            @include breakpoint($BREAKPOINT_DESKTOP) {
              display: flex;
            }
            .action {
              @include breakpoint($BREAKPOINT_NOT_DESKTOP) {
                margin-top: 10px;
              }
              display: flex;
              align-items: flex-start;
            }
            .product-ref {
              flex: 1;
              font-weight: bold;
              text-transform: uppercase;
            }
            .details {
              flex: 1;
              display: flex;
              button {
                margin: 0;
                padding: 5px 20px;
              }
            }
            .no-product-info {
              flex: 1;
              display: flex;
              align-items: center;
            }
          }
        }
        .card-close {
          position: absolute;
          top: 2px;
          left: 2px;
          height: 26px;
          width: 26px;
          cursor: pointer;
        }
        .card-collapse-toggle {
          color: #9600FF;
          cursor: pointer;
          display: flex;
          margin-left: 20px;
          margin-top: 0px;
          align-items: flex-start;
          svg {
            transform: rotate(180deg);
            transition: all .3s ease-in-out;
          }
          &.loading {
            background: url("/images/loader.gif") no-repeat center;
            width: 15px;
            background-size: contain;
            svg {
              display: none;
            }
          }
        }
        &.collapsed {
          .card-collapse-toggle svg {
            transform: rotate(0deg);
          }
          .card-collapse-container {
            display: none;
          }
        }
      }
    }
  }
}
