@import "../_variables";


body[class*=" Page--company_sites"] {
  .Page-inner {
    padding: 0;
  }

  h1 {
    margin-bottom: 0;
  }

  .add-shipping-point-container .Buttons-group {
    @include breakpoint($BREAKPOINT_NOT_DESKTOP) {
      padding-bottom: 30px;

      div {
        padding: 0;
      }
    }
  }
}

.hidden {
  display: none;
}

.SiteForm {
  li {
    list-style-type: none;
  }

  textarea {
    width: 100%;
  }

  .form-title h3 {
    color: $BLACK;
    font-style: normal;
    font-size: 1.0625rem;
  }

  .form-title {
    margin-bottom: 10px;
  }

  .form-second-part {
    margin-top: 35px;

    @include breakpoint($BREAKPOINT_NOT_DESKTOP) {
      margin-top: 55px;
    }
  }

  .country {
    font-size: 16px;
    width: 100%;
    margin-bottom: 30px;
  }

  button {
    margin-left: 0;
  }

  .actions-site {
    display: flex;
    align-items: flex-end;
    padding-bottom: 30px;

    button {
      margin-bottom: 0;
    }

    a.cancel-edit {
      color: $ALSTOM_GREY;
      font-size: 0.8125rem;
      line-height: 0.8125rem;
      font-weight: 700;
      letter-spacing: 1px;
      margin-left: 10px;

      &:hover {
        color: $PRIMARY_COLOR;
        cursor: pointer;
      }
    }
  }
}

.site-comment label {
  position: absolute;
  top: -20px;
  left: 0;
  color: $BLACK;
}

#js-contact-remove {

  .Icon {
    float: right;
    pointer-events: all;
    width: 16px;
    height: 16px;
    margin-right: 10px;
    margin-top: 5px;
    fill: $PRIMARY_COLOR;
  }
}

.contact-form {
  @include breakpoint($BREAKPOINT_DESKTOP) {
    display: flex;
    flex-wrap: wrap;
    justify-content: space-between;
    width: 100%;
  }
}

.openingTime-table {
  width: 100%;
  float: left;
  margin-bottom: 70px;
}

.openingTime-table .form-row {
  height: auto;
  margin: 0;
  width: 90%;
  padding: 0;
  min-height: 0;
}

.openingTime-table input {
  border: 1px solid $APPLI_GREY5;
  height: 50px;
  padding: 0;
  text-align: center;
  margin-top: 5px;
}

.openingTime-table-label {

  @include breakpoint($BREAKPOINT_TABLET) {
    display: block;
  }
  @include breakpoint($BREAKPOINT_MOBILE_MAX) {
    display: none;
  }
}

.openingTime-table-label-mobile, .openingTime-table-label {
  float: left;
  background-color: $APPLI_GREY5;
  color: $PRIMARY_COLOR;
  word-break: keep-all;
  border: 3px solid white;
  height: 60px;
  font-weight: bold;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 90%;
  padding: 5px;
}

.openingTime-table-label-mobile  {
  @include breakpoint($BREAKPOINT_TABLET) {
    display: none;
  }
  @include breakpoint($BREAKPOINT_MOBILE_MAX) {
    width: 50%;
    margin-right: 5%;
  }
}

.openingTime-table-cell-mobile {
  height: 60px;
  @include breakpoint($BREAKPOINT_TABLET) {
    width: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
  }
  @include breakpoint($BREAKPOINT_MOBILE_MAX) {
    width: 45%;
    float: left;
  }
}

.openingTime-table-header .flex-between-container {
  text-align: center;
  @include breakpoint($BREAKPOINT_TABLET) {
    width: 16.66%;
    display: block;
    float: left;
  }
  @include breakpoint($BREAKPOINT_MOBILE_MAX) {
    display: block;
    width: 100%;
  }
}

.openingTime-table-header .flex-between-container:first-child {
  @include breakpoint($BREAKPOINT_MOBILE_MAX) {
    display: none;
  }
}

.openingTime-table-header-cell.header-label, .openingTime-table-header-cell.header-label-mobile {
  float: left;
  height: 50px;
  background-color: $PRIMARY_COLOR;
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
}

.openingTime-table-header-cell.header-label{
  @include breakpoint($BREAKPOINT_MOBILE_MAX) {
    display: none;
  }
}

.openingTime-table-header-cell.header-label-mobile {
  cursor: pointer;
  position: relative;
  @include breakpoint($BREAKPOINT_TABLET) {
    display: none;
  }
}

.openingTime-table-header-cell.header-label-mobile:before {
  content: "\25BC";
  position: absolute;
  top: 10px;
  font-size: 18px;
  left: 30px;
  color: white;

}

.openingTime-table-header-cell.header-label-mobile.open:before {
  content: "\25B2";
}

.openingTime-content {
  @include breakpoint($BREAKPOINT_MOBILE_MAX) {
    display: none;
  }
}

.openingTime-content.open{
  @include breakpoint($BREAKPOINT_MOBILE_MAX) {
    display: block;
  }
}

.ui-timepicker-standard {
  border: none;
}

.TableList {
  margin: 0 auto 90px;

  @include breakpoint($BREAKPOINT_NOT_DESKTOP) {
    margin-top: 45px;
  }

  .sites-list {
    width: 100%;

    .site-row{
      margin-top: 30px;
      padding: 30px 30px 0;
      background-color: $WHITE;
      min-height: 50px;
      display: flex;
      flex-direction: column;
      flex-wrap: wrap;
      position: relative;

      @include breakpoint($BREAKPOINT_NOT_DESKTOP) {
        padding: 15px 20px 0;
      }

      .site-status {
        display: flex;
        justify-content: left;
        align-items: center;
        width: 100%;
        padding-left: 5px;

        .status{
          width:16px;
          height: 16px;
          border-radius: 50%;

          &.draft{
            background-color: $ERROR;
          }

          &.pending {
            background-color: $PENDING;
          }

          &.valid {
            background-color: $OK;
          }
        }

        .text {
          color : $PRIMARY_COLOR;
          text-align: left;
          margin-left: 10px;
          width: 80%;
        }

        @include breakpoint($BREAKPOINT_DESKTOP) {
          width: 45%;
        }
      }

      .site-label {
        display: flex;
        align-items: center;
        width: 100%;
        border-bottom: 1px solid $ALSTOM_LIGHT_GREY;
        padding-bottom: 18px;

        .action-icons {
          margin: 5px 0 0 45px;
        }

        a:hover {
          text-decoration: none;

          .Icon {
            fill: $PRIMARY_COLOR;

            &.stroke {
              fill: $WHITE;
              stroke: $PRIMARY_COLOR;
            }
          }
        }

        .Icon {
          fill: $ALSTOM_GREY;
          width: 25px;
          height: 25px;

          &.stroke {
            fill: $WHITE;
            stroke: $ALSTOM_GREY;
          }
        }

        @include breakpoint($BREAKPOINT_NOT_DESKTOP) {
          h1 {
            font-size: 1.5rem;
            margin-left: 30px;
            font-weight: 600;
          }

          .action-icons {
            display: flex;
            margin-left: 20px;
          }
        }
      }

      .shipping-points-list {
        display: flex;
        justify-content: space-between;
        flex-wrap: wrap;
        padding-bottom: 30px;

        .shipping-point {
          justify-content: space-between;
          text-align: left;
          padding: 30px;
          background-color: $ALSTOM_GREY_BACKGROUND;
          border: 1px solid $ALSTOM_LIGHT_GREY;
          font-size: 1rem;
          font-weight: 300;
          box-shadow: 0px 1px 4px rgba($ALSTOM_GREY, 0.15);

          &:hover {
            text-decoration: none;
          }

          &-name {
            color: $BLACK;
            font-size: 1.0625rem;
            font-weight: 600;
            margin-bottom: 10px;
          }

          &-address {
            margin-bottom: 10px;
            color: $ALSTOM_DARK_GREY;
          }

          &-user {
            text-transform: uppercase;
            color: $ALSTOM_GREY;
            letter-spacing: 1px;
            font-weight: 700;
            font-size: 0.83rem;
          }

          &-phone {
            letter-spacing: 1px;
            color: $ALSTOM_DARK_GREY;
            display: flex;
            justify-content: space-between;

            .Icon {
              fill: $ALSTOM_GREY;
              width: 25px;
              height: 25px;

              &.stroke {
                fill: $ALSTOM_GREY_BACKGROUND;
                stroke: $ALSTOM_GREY;
              }
            }

            a:hover {
              text-decoration: none;

              .Icon {
                fill: $PRIMARY_COLOR;

                &.stroke {
                  fill: $WHITE;
                  stroke: $PRIMARY_COLOR;
                }
              }
            }
          }

          &-actions {
            .modify {
              color: $ALSTOM_GREY;
            }

            .delete {
              color: $ALSTOM_GREY;
            }
          }
        }

        .add-shipping-point {
          cursor: pointer;
          justify-content: center;
          text-align: center;
          border: 2px dashed $ALSTOM_GREY;
          text-transform: uppercase;
          font-weight: 700;
          letter-spacing: 1px;
          font-size: 0.83rem;

          .plus-symbol {
            font-size: 25px;
            margin-bottom: 5px;
            color: $ALSTOM_DARK_GREY;
            font-weight: 300;
          }

          &:hover {
            text-decoration: none;
            background-color: $ALSTOM_GREY_BACKGROUND;
          }

          @include breakpoint($BREAKPOINT_NOT_DESKTOP) {
            margin-bottom: 20px;
          }
        }

        .shipping-point, .add-shipping-point {
          width: 48%;
          min-height: 205px;
          height: auto;
          max-height: 320px;
          color: $ALSTOM_GREY;
          line-height: 25px;
          display: flex;
          flex-direction: column;
          margin-top: 30px;

          @include breakpoint($BREAKPOINT_NOT_DESKTOP) {
            width: 100%;
          }
        }

        &.hide {
          display: none;
        }
      }
    }

    .site-address-add {
      display: none;
      margin-top: -1px;
      background-color: $ALSTOM_LIGHT_GREY;
      padding: 10px 30px;

      @include breakpoint($BREAKPOINT_NOT_DESKTOP) {
        padding: 0;
      }

      .Page-inner {
        width: 100%;
      }

      .SiteForm {
        max-width: 100%;
        margin-bottom: 20px;
      }

      .Buttons-group {
        display: flex;
        align-items: flex-end;
        justify-content: flex-start;

        a {
          color: $ALSTOM_GREY;
          font-size: 0.8125rem;
          line-height: 0.8125rem;
          font-weight: 700;
          letter-spacing: 1px;
          margin-left: 10px;

          &:hover {
            color: $PRIMARY_COLOR;
            cursor: pointer;
          }
        }
      }

      button.button_margin {
        border-style: solid;
        border-color: $PRIMARY_COLOR;
        background-color: $PRIMARY_COLOR;
        color: $WHITE;
        height: 60px;
        font-size: 0.875rem;
        padding: 16px 25px;
        margin-top: 0;

        &:hover {
          background-color: $WHITE;
          color: $PRIMARY_COLOR;
        }
      }

      &.show {
        display: block;
      }
    }
  }

  button.button_margin {
    margin-bottom: 0;
    background-color: $ALSTOM_GREY_BACKGROUND;
    justify-content: center;
    text-align: center;
    border: 2px dashed $ALSTOM_GREY;
    color: $ALSTOM_GREY;
    text-transform: uppercase;
    font-weight: 700;
    letter-spacing: 1px;
    font-size: 0.83rem;
    width: 100%;
    height: 100px;
    padding: 0;

    .plus-symbol {
      font-size: 25px;
      margin: -10px 0 2px;
      color: $ALSTOM_DARK_GREY;
      font-weight: 300;
    }

    &:hover {
      background-color: $WHITE;
      color: $ALSTOM_GREY;
    }
  }

  #add-site-form {
    display: none;

    form {
      background: $WHITE;
      display: flex;
      justify-content: space-between;
      margin-top: 30px;
      padding: 20px;

      label {
        display: none;
      }

      input:not([type="file"]) {
        width: 500px;
        border: 0;
        box-shadow: none;
        margin: 0;
        font-family: 'HKNova', 'Poppins', sans-serif;
        font-size: 2em;
        font-weight: bold;
        color: $BLACK;

        &::-webkit-input-placeholder
        {
          font-weight: normal;
        }
        &::-moz-placeholder {
          font-weight: normal;
        }
        &:-ms-input-placeholder {
          font-weight: normal;
        }
        &:-moz-placeholder {
          font-weight: normal;
        }

        @include breakpoint($BREAKPOINT_NOT_DESKTOP) {
          width: 100%;
          font-size: 1.375rem;
        }
      }

      button {
        margin: 0;
        border: 1px solid $PRIMARY_COLOR;
      }

      @include breakpoint($BREAKPOINT_NOT_DESKTOP) {
        height: auto;
        display: flex;
        flex-direction: column;
      }
    }

    &.show {
      display: block;
    }

    .actions-add {
      display: flex;
      align-items: flex-end;

      a {
        color: $ALSTOM_GREY;
        font-size: 0.8125rem;
        line-height: 0.8125rem;
        font-weight: 700;
        letter-spacing: 1px;
        margin-right: 10px;

        &:hover {
          color: $PRIMARY_COLOR;
          cursor: pointer;
        }
      }

      @include breakpoint($BREAKPOINT_NOT_DESKTOP) {
        justify-content: flex-end;
      }
    }
  }

  .button-add-site {
    display: block;

    &.hide {
      display: none;
    }

    @include breakpoint($BREAKPOINT_NOT_DESKTOP) {
      &.button_margin {
        height: 131px;
      }
    }
  }
}

.Page--site_document {
  margin-bottom: 30px;
}
