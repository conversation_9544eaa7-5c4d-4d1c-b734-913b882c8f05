.UserForm {
  padding: 10px;

  &-checkboxes {
    margin-left: 35px;
  }

  &-roles {
    .Form-group.Form--radio select {
      width: 100%;
    }
  }

  &-sites.Form-group {
    label:after {
      display: none; // hide border
    }
  }

  &-sitesError {

    label.error {
      position: relative;
      top: 0;
    }

    ul {
      list-style-type: none;
      padding-left: 0;

      li {
        color: $RED;
      }
    }
  }

  @include breakpoint($BREAKPOINT_DESKTOP) {
    //padding: 0;

    &-roles {
      .Form-group.Form--radio { // override faux positif of radio detection logic in appli_layout.html.twig
        display: flex;
      }
    }
  }

  @include breakpoint($BREAKPOINT_DESKTOP_WIDE) {
    padding: 0;
  }
}
