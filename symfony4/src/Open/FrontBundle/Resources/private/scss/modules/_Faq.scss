@import "../variables";

.Home-Insert {

  background-color: white;
  display: flex;
  flex-wrap: wrap;
  margin: 0 auto;

  .Home--section {
    display: flex;
    flex-grow: 1;
    flex-direction: column;
    width: 50%;
    justify-content: space-between;
    text-align: left;
    min-height: 340px;
    padding: 40px 50px;


    h2 {
      font-size: 30px;
      color: $WHITE;
      font-weight: bold;
    }

    h3 {
      color: $WHITE;
    }

    .cartel {
      display: flex;
      flex-direction: column;
      justify-content: space-between;
      min-height: 270px;
    }

    @include breakpoint($BREAKPOINT_NOT_DESKTOP) {
      width: 95%;
      padding: 7% 5% 7% 5%;
      min-height: 0;

      h2 {
        font-size: 2em;
      }
    }

    h3 {
      text-transform: uppercase;
      font-size: 18px;
      font-weight: normal;
      margin-bottom: 0;
      height: 25px;
    }

    p {
      color: $WHITE;
    }

    .button {
      margin: 0;
      font-weight: bold;
      white-space: nowrap;

      @include breakpoint($BREAKPOINT_DESKTOP) {
        max-width: 200px;
      }
      @include breakpoint($BREAKPOINT_NOT_DESKTOP) {
        width: 45%;
        padding: 8px 12px;
        font-size: 12px;
      }
    }
  }

  .Home--section--sustainability {
    background-color: $PRIMARY_COLOR;
    h2 {
      color: $WHITE;
    }

    .cartel {
      padding: 20px 20px;
      background-color: transparent;
    }
  }

  .Home--section--contact h2 {
    color: $BLACK;
    margin-top: 20px;
  }

  .Home--section--news {
    background-image: url("../../images/Carte-contact.jpg");
    display: flex;
    flex-direction: row;
    align-items: flex-end;
    .button {
      background-color: $PRIMARY_COLOR;
      border: 1px solid $PRIMARY_COLOR;
      color: $WHITE;
      text-align: center;
      white-space: nowrap;

      &:hover {
        background-color: $PRIMARY_COLOR;
        border: 1px solid $PRIMARY_COLOR;
        color: white;
      }
    }

    .cartel {
      width: 60%;
      min-width: 400px;
      padding: 20px 20px;
      background-color: rgba(0, 0, 0, 0.3);
    }
    .see-all {
      color: $ALSTOM_RED;
      text-transform: uppercase;
      letter-spacing: 2px;
      border-bottom: 1px solid $ALSTOM_RED;

      &:hover {
        text-decoration: none;
      }
    }

  }

  .Home--section--text {
    font-size: 16px;
    padding-bottom: 20px;
  }

  @include breakpoint($BREAKPOINT_NOT_DESKTOP) {
    padding-top: 0;
  }
}
