{% extends '@OpenFront/base.html.twig' %}
{% trans_default_domain 'AppBundle' %}

{% block title %} {{ 'payment.pre_virement_mode.success.title' | trans }} {% endblock %}
{% block stylesheets %}
    <link href="{{ asset('css/page-default-v1.css') }}" rel="stylesheet" media="screen">
{% endblock %}

{% block body %}
    <div class="Page-inner checkout-success" style="min-height: 200px; padding: 50px 30px 80px">
        <h2 style="margin-top:50px">{{ 'payment.pre_virement_mode.success.title' | trans }}</h2>
        <div>
            <p>
                {{ 'payment.pre_virement_mode.success.text' | trans({'%numOrder%':numOrder}) }}
            </p>
            <p>
                {{ 'payment.pre_virement_mode.success.payment_details' | trans }}
            </p>
            <ul>
                <li>
                    {{ 'payment.pre_virement_mode.success.payment_details_iban_account_name' | trans({'%iban_account_name%':ibanAccountName}) }}
                </li>
                <li>
                    {{ 'payment.pre_virement_mode.success.payment_details_iban' | trans({'%iban%':iban}) }}
                </li>
                <li>
                    {{ 'payment.pre_virement_mode.success.payment_details_key' | trans({'%key%':reconciliationKey}) }}
                </li>
            </ul>
            <div class="actions">
                <a href="{{ path('front.search')}}" style="text-decoration: none;"><button class="btn-primary inverse">{{ 'payment.time_mode.succes.text_link1'|trans }}</button></a>
                <a href="{{ path('front.orders.list')}}" style="text-decoration: none;"><button class="btn-primary">{{ 'payment.time_mode.succes.text_link2'|trans }}</button></a>
            </div>
        </div>
    </div>

{% endblock %}


{% block javascripts %}
    <script type="text/javascript">

    </script>
{% endblock %}
