{% extends '@OpenFront/base.html.twig' %}
{% trans_default_domain 'AppBundle' %}

{% block title %}{{ 'payment.pre_cc_mode.title' |trans }}{% endblock %}
{% block stylesheets %}
    <link href="{{ asset('css/page-default-v1.css') }}" rel="stylesheet" media="screen">
{% endblock %}
{% block body %}
    {% include('@OpenFront/cart/cart_steps.html.twig') with {
        'hasShipping': hasShipping,
        'isProductAndAddressSelection' : false,
        'isShipping': false,
        'isOrderConfirmation': true
    } %}
    <div class="Page-inner checkout-success" style="min-height: 200px; padding: 50px 30px 80px">
        <h2 id='checkout-title' style="margin-top: 50px">{{ 'payment.pre_cc_mode.title' |trans }}</h2>
        <div id="status">
            {{ 'payment.pre_cc_mode.waiting_status' | trans({'%id%':order.idNumber}) }}
        </div>

        <div class="actions">
            <a href="{{ path('front.search')}}" style="text-decoration: none;"><button class="btn-primary inverse">{{ 'payment.time_mode.succes.text_link1'|trans }}</button></a>
            <a href="{{ path('front.orders.list')}}" style="text-decoration: none;"><button class="btn-primary">{{ 'payment.time_mode.succes.text_link2'|trans }}</button></a>
        </div>
    </div>
{% endblock %}

{% block javascripts %}
    <script type="text/javascript">
        document.addEventListener('DOMContentLoaded', function () {
            window.UI.Modal.showLoading();

            var checkPayment = function() {
                $.ajax(
                    "{{ path('front.check.cc', {'orderId': orderId}) }}",
                    {
                        success: function (data) {
                            if (data.status === "ok") {
                                document.title = "{{ 'payment.pre_cc_mode.title_ok' | trans({'%id%':order.idNumber}) }}";
                                $('#checkout-title').html('{{ 'payment.pre_cc_mode.success.title' | trans }}');
                                $('#status').html(
                                    '<p>{{ 'payment.pre_cc_mode.success.text' | trans({'%id%':order.idNumber}) }}</p>'
                                    + '<p>{{ 'payment.pre_cc_mode.success.text_2' | trans }}</p>'
                                    + '<p>{{ 'payment.pre_cc_mode.success.text_3' | trans }}</p>'
                                );

                                if (data.currency && data.currency === 'USD') {
                                    $("#cart-quantity-usd").parent().addClass("hide");
                                }

                                if (data.currency && data.currency === 'EUR') {
                                    $("#cart-quantity-eur").parent().addClass("hide");
                                }

                                window.UI.Modal.hideLoading();
                            }
                            else if (data.status === 'pending') {
                                setTimeout(checkPayment, 5000);
                            }
                            else if (data.status === "ko") {
                                document.title = "{{ 'payment.pre_cc_mode.title_ko' | trans({'%id%':order.idNumber}) }}";
                                $("#status").html('{{ 'payment.pre_cc_mode.ko' | trans({'%id%':order.idNumber}) }}');

                                window.UI.Modal.hideLoading();
                            }
                            else {
                                document.title = "{{ 'payment.pre_cc_mode.title_error' | trans({'%id%':order.idNumber}) }}";
                                $("#status").html('{{ 'payment.pre_cc_mode.error' | trans({'%id%':order.idNumber}) }}');

                                window.UI.Modal.hideLoading();
                            }
                        },
                        error: function () {
                            document.title = "{{ 'payment.pre_cc_mode.title_error' | trans({'%id%':order.idNumber}) }}";
                            $("#status").html('{{ 'payment.pre_cc_mode.error' | trans({'%id%':order.idNumber}) }}');

                            window.UI.Modal.hideLoading();
                        }
                    }
                );
            };

            checkPayment();
        });
    </script>
{% endblock %}


