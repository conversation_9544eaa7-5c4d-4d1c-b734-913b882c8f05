{% extends '@OpenFront/base.html.twig' %}
{% trans_default_domain 'AppBundle' %}

{% block title %} {{ 'payment.pre_virement_mode.success.title' | trans }} {% endblock %}
{% block stylesheets %}
    <link href="{{ asset('css/page-default-v1.css') }}" rel="stylesheet" media="screen">
{% endblock %}

{% block body %}
    {% include('@OpenFront/cart/cart_steps.html.twig') with {
        'hasShipping': hasShipping,
        'isProductAndAddressSelection' : false,
        'isShipping': false,
        'isOrderConfirmation': true
    } %}

    <div class="Page-inner checkout-success" style="min-height: 200px; padding: 50px 30px 80px">
        <h2 style="margin-top: 50px">{{ 'payment.pre_virement_mode.pending.title' | trans }}</h2>
        <div>
            <p>{{ 'payment.pre_virement_mode.pending.text' | trans }}</p>
            <p>{{ 'payment.pre_virement_mode.pending.text_2' | trans }}</p>
            <p>{{ 'payment.pre_virement_mode.pending.text_3' | trans }}</p>
            <p>{{ 'payment.pre_virement_mode.pending.text_4' | trans }}</p>

            <div class="actions">
                <a href="{{ path('front.search')}}" style="text-decoration: none;"><button class="btn-primary inverse">{{ 'payment.pre_virement_mode.pending.text_link1'|trans }}</button></a>
                <a href="{{ path('front.orders.list')}}" style="text-decoration: none;"><button class="btn-primary">{{ 'payment.pre_virement_mode.pending.text_link2'|trans }}</button></a>
            </div>
        </div>
    </div>
{% endblock %}


{% block javascripts %}
    <script type="text/javascript">

    </script>
{% endblock %}
