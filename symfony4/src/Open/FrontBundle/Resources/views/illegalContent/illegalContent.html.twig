{% extends '@OpenFront/menu/menu.html.twig' %}
{% trans_default_domain 'AppBundle' %}

{% block title %}
    {{ 'footer.help.illegal_content'|trans }}
{% endblock %}


{% block body %}
    <div class="Page-inner">

        <div class="Form ContractForm">

            <div class="form-title">
                <h1>{{ 'illegal_content.form.title'|trans }}</h1>
            </div>

            {{ form_start(form) }}

            {{ form_row(form.content) }}

            {{ form_row(form.url) }}

            {{ form_end(form) }}
        </div>
    </div>

    <script type="text/javascript">
        'use strict';
        document.addEventListener('DOMContentLoaded', function() {
            $('#illegal_content_form_url').val("{{ url }}");
        });
    </script>

{% endblock %}
