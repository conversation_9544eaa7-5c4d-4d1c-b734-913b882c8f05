{% extends '@OpenFront/menu/buyer_menu.html.twig' %}
{% form_theme form 'Form/appli_layout.html.twig' %}
{% trans_default_domain 'AppBundle' %}

{% set title = 'shipping_point.form.title_new' %}
{% set formPath =  path('front.shipping_point.create', {'site_id':siteId}) %}

{% if shippingPoint.id is defined and shippingPoint.id %}
    {% set title = 'shipping_point.form.title_edit' %}
    {% set formPath =  path('front.shipping_point.edit', {'site_id':siteId, 'id': shippingId}) %}
{% endif %}

{% block title %}
    {{ title|trans }} {{ 'shipping_point.form.title_common'|trans }}
{% endblock %}

{% block body %}
    <div class="Page-inner">
        <div class="Form SiteForm">

            {{ form_start(form, {'attr':{'id':'js-shipping_point-form', 'action': formPath }}) }}

            <div class="form-title">
                <h1>
                    {{ title|trans }} {{ 'shipping_point.form.title_common'|trans }}
                </h1>
            </div>

            <div class="flex-container">
                <div class="form-title"><h3>{{ 'shipping_point.form.address.label'|trans }}</h3></div>

                <div class="country">{{ company.mainAddress.country |trans }}</div>
                <div style="display:none">
                    <select id="country" name="country">
                        <option selected="selected" value="{{ company.mainAddress.country.id }}"></option>
                    </select>
                </div>

                <div class="row-2-1">
                    {{ form_row(form.name) }}
                </div>
                <div class="row-2-1">
                    {{ form_row(form.address.address) }}
                </div>
                <div class="row-2-1">
                    {{ form_row(form.address.address2) }}
                </div>
                <div class="row-1-1-1">
                    {{ form_row(form.address.zipCode, {'id': 'shipping_point_address_zipCode_' ~ siteId}) }}
                    {{ form_row(form.address.city, {'id': 'shipping_point_address_city_' ~ siteId}) }}
                    {{ form_row(form.address.regionText) }}
                </div>

                <div class="form-title form-second-part"><h3>{{ 'shipping_point.form.contact.accountant'|trans }}</h3>
                </div>
                {{ form_row(form.accountantEmail) }}

                <div class="form-title form-second-part"><h3>{{ 'shipping_point.form.contact.label'|trans }}</h3></div>

                {{ form_row(form.contact.lastName) }}
                {{ form_row(form.contact.firstName) }}

                <div class="row-2-1">
                    {{ form_row(form.contact.email) }}
                </div>
                <div class="row-1-1-1">
                    {{ form_row(form.contact.phone1) }}
                    {{ form_row(form.contact.phone2) }}
                    {{ form_row(form.contact.function) }}
                </div>
                <div class="row-2-1">
                    <div class="custom-text-area-container">
                        {{ form_row(form.comment) }}
                    </div>
                </div>
            </div>
            <div class="requestDocs">
                <div class="form-title form-second-part">
                    <h3 style="transform: none">{{ 'shipping_point.form.packaging_request.label'|trans }}
                        <div class="tooltip" style="margin-left: 10px">
                            <span>
                                <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 16 16">
                                    <path fill="#9600FF" d="M8 0C3.579 0 0 3.579 0 8c0 4.421 3.579 8 8 8 4.421 0 8-3.579 8-8 0-4.421-3.579-8-8-8zM6.81 3.046c.153-.023.314-.047.479-.058.172-.011.33-.018.468-.018.148 0 .314.005.492.018.172.01.338.035.486.***************.302.**************.011.302.011.45 0 .143-.005.29-.01.444-.012.154-.03.302-.054.458-.148.023-.309.042-.479.053-.172.005-.331.01-.48.01-.144 0-.303-.005-.475-.01-.177-.011-.338-.03-.491-.053-.053-.302-.077-.606-.077-.902-.002-.283.022-.585.077-.9zm2.986 9.984H6.204c-.053-.23-.076-.468-.076-.71 0-.114.005-.239.018-.368.01-.13.035-.255.058-.368h.878V7.461h-.878c-.023-.114-.047-.238-.058-.368-.011-.13-.018-.249-.018-.368 0-.243.023-.479.076-.71h2.714v5.569h.878c.***************.058.368.**************.018.367 0 .243-.023.481-.076.711z"></path>
                                </svg>
                            </span>
                            <div class="tooltiptext">
                                <span class="info" style="color: rgb(255, 255, 255);">{{ 'shipping_point.form.packaging_request.tips'|trans }}</span>
                            </div>
                        </div>
                    </h3>
                </div>
                {{ form_row(form.packagingRequest1) }}
                {{ form_row(form.packagingRequest2) }}
                {{ form_row(form.packagingRequest3) }}
            </div>

            <div class="form-title form-second-part">
                <h3 style="transform: none">{{ 'shipping_point.form.documents_requests.label'|trans }}
                    <div class="tooltip" style="margin-left: 10px">
                            <span>
                                <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 16 16">
                                    <path fill="#9600FF" d="M8 0C3.579 0 0 3.579 0 8c0 4.421 3.579 8 8 8 4.421 0 8-3.579 8-8 0-4.421-3.579-8-8-8zM6.81 3.046c.153-.023.314-.047.479-.058.172-.011.33-.018.468-.018.148 0 .314.005.492.018.172.01.338.035.486.***************.302.**************.011.302.011.45 0 .143-.005.29-.01.444-.012.154-.03.302-.054.458-.148.023-.309.042-.479.053-.172.005-.331.01-.48.01-.144 0-.303-.005-.475-.01-.177-.011-.338-.03-.491-.053-.053-.302-.077-.606-.077-.902-.002-.283.022-.585.077-.9zm2.986 9.984H6.204c-.053-.23-.076-.468-.076-.71 0-.114.005-.239.018-.368.01-.13.035-.255.058-.368h.878V7.461h-.878c-.023-.114-.047-.238-.058-.368-.011-.13-.018-.249-.018-.368 0-.243.023-.479.076-.71h2.714v5.569h.878c.***************.058.368.**************.018.367 0 .243-.023.481-.076.711z"></path>
                                </svg>
                            </span>
                        <div class="tooltiptext">
                            <span class="info" style="color: rgb(255, 255, 255);">{{ 'shipping_point.form.packaging_request.tips'|trans }}</span>
                        </div>
                    </div>
                </h3>
            </div>
            <div style="display: flex; justify-content: space-between">
                {{ form_row(form.documentationRequest1) }}
                {{ form_row(form.documentationRequest2) }}
            </div>
            <div style="display: flex; justify-content: space-between">
                {{ form_row(form.documentationRequest3) }}
                {{ form_row(form.documentationRequest4) }}
            </div>
            <div style="display: flex; justify-content: space-between">
                {{ form_row(form.documentationRequest5) }}
                {{ form_row(form.documentationRequest6) }}
            </div>
            <div style="display: flex; justify-content: space-between">
                {{ form_row(form.documentationRequest7) }}
                {{ form_row(form.documentationRequest8) }}
            </div>
            <div style="display: flex; justify-content: space-between">
                {{ form_row(form.documentationRequest9) }}
                {{ form_row(form.documentationRequest10) }}
            </div>

            <div class="actions-site">
                {{ form_row(form.save) }}
                <a class="cancel-edit" href="{{ path('front.company.sites') }}">{{ 'site.form.cancel'|trans }}</a>
            </div>

            {{ form_end(form) }}

            <script type="text/javascript">
                'use strict';

                document.addEventListener('DOMContentLoaded', function () {

                    window.UI.Select.init();
                    var siteId = '{{ siteId }}';
                    UI.AutoComplete.createAutoComplete({
                        "path": "{{ path('city_autocomplete') }}",
                        "countryId": "#country",
                        "cityId": "#shipping_point_address_city_" + siteId,
                        "zipcodeId": "#shipping_point_address_zipCode_" + siteId,
                        "cityField": true
                    });

                    // Auto complete zipcode
                    UI.AutoComplete.createAutoComplete({
                        "path": "{{ path('city_autocomplete') }}",
                        "countryId": "#country",
                        "cityId": "#shipping_point_address_zipCode_" + siteId,
                        "zipcodeId": "#shipping_point_address_city_" + siteId,
                        "cityField": false
                    });

                    $('#js-shipping_point-form').validate({
                        focusInvalid: false,
                        invalidHandler: function (form, validator) {

                            if (!validator.numberOfInvalids())
                                return;

                            $('html, body').animate({
                                scrollTop: $(validator.errorList[0].element).offset().top - 300
                            }, 0);

                        }
                    });
                });

            </script>
        </div>
    </div>
{% endblock %}
