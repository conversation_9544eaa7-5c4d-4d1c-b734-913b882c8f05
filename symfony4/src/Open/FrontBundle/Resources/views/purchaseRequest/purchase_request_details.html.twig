{% extends '@OpenFront/menu/buyer_menu.html.twig' %}
{% trans_default_domain 'AppBundle' %}

{% block title %} {{ 'purchase_request.detail' | trans }} {% endblock %}

{% block body %}
<div class="Page-inner">
    <div class="content-orders">
        <div id="app-purchase-request"></div>
    </div>
</div>
{% endblock %}

{% block javascripts %}
    {% set cacheBuster = 'v0' %} {# TODO Use webpack to load those js files #}

    <script type="text/javascript">
        let purchaseRequestViewModel = JSON.parse('{{ purchaseRequestViewModel|e('js') }}');
        let csvMapping = JSON.parse('{{ csvMapping|e('js') }}');
        let foundCsvMapping = JSON.parse('{{ foundCsvMapping|e('js') }}');

        var store = {
            items: purchaseRequestViewModel.items,
            importUrl: "{{ path('purchase_request.import') }}",
            importCsrf: "{{ csrf_token('import') }}",
            clearUrl: "{{ path('purchase_request.clear') }}",
            clearCsrf: "{{ csrf_token('clear') }}",
            searchOfferUrl: "{{ path('purchase_request.search_offer', {'purchaseRequestItemId': '--purchaseRequestItemId--'}) }}",
            addToCartUrl: "{{ path('purchase_request.add_to_cart', {'purchaseRequestItemId': '--purchaseRequestItemId--'}) }}",
            addToCartCsrf: "{{ csrf_token('addToCart') }}",
            removeFromCartUrl: "{{ path('purchase_request.remove_from_cart', {'purchaseRequestItemId': '--purchaseRequestItemId--'}) }}",
            removeFromCartCsrf: "{{ csrf_token('removeFromCart') }}",
            removeItemUrl: "{{ path('purchase_request.remove_item', {'purchaseRequestItemId': '--purchaseRequestItemId--'}) }}",
            removeItemCsrf: "{{ csrf_token('removeItem') }}",
            shippingIcon: "{{ asset('images/Truck.svg') }}",
            offerDetailUrl: "{{ path('front.offer.detail.short', {'ref': '--offerId--'}) }}",
            sendCsv: "{{ path('purchase_request.send') }}",
            noProductItems: [],
            finish: false,
            translation: {
                language: "{{ locale }}",
                title: "{{ 'purchase_request.title'|trans|e('js') }}",
                clear: "{{ 'purchase_request.clear'|trans|e('js') }}",
                export: "{{ 'purchase_request.export'|trans|e('js') }}",
                exportFound: "{{ 'purchase_request.exportFound'|trans|e('js') }}",
                modal: {
                    confirm: "{{ 'modal.confirm'|trans|e('js') }}",
                    cancel: "{{ 'modal.cancel'|trans|e('js') }}"
                },
                import: {
                    title: "{{ 'purchase_request.import.title'|trans|e('js') }}",
                    error: {
                        csv_format: "{{ 'purchase_request.import.error.csv_format'|trans|e('js') }}",
                        csv_size: "{{ 'purchase_request.import.error.csv_size'|trans|e('js') }}",
                        internal_error: "{{ 'purchase_request.import.error.internal_error'|trans|e('js') }}"
                    }
                },
                import_waiting: "{{ 'purchase_request.import_waiting'|trans|e('js') }}",
                instruction: '{{ 'purchase_request.instruction'|trans({'%link%':purchase_request_csv_template_download})|e('js') }}',
                pr_item: {
                    buyer_reference: "{{ 'purchase_request.pr_item.buyer_reference'|trans|e('js') }}",
                    manufacturer_reference: "{{ 'purchase_request.pr_item.manufacturer_reference'|trans|e('js') }}",
                    manufacturer_name: "{{ 'purchase_request.pr_item.manufacturer_name'|trans|e('js') }}",
                    product_name: "{{ 'purchase_request.pr_item.product_name'|trans|e('js') }}",
                    quantity_expected: "{{ 'purchase_request.pr_item.quantity_expected'|trans|e('js') }}",
                    unit_price_of_reference: "{{ 'purchase_request.pr_item.unit_price_of_reference'|trans|e('js') }}",
                    expected_delivery_date: "{{ 'purchase_request.pr_item.expected_delivery_date'|trans|e('js') }}",
                    cost_center: "{{ 'purchase_request.pr_item.cost_center'|trans|e('js') }}",
                    purchase_request_number: "{{ 'purchase_request.pr_item.purchase_request_number'|trans|e('js') }}",
                    buyer_order_number: "{{ 'purchase_request.pr_item.buyer_order_number'|trans|e('js') }}",
                    order_line: "{{ 'purchase_request.pr_item.order_line'|trans|e('js') }}",
                    ref: "{{ 'purchase_request.pr_item.ref'|trans|e('js') }}",
                    merchant: "{{ 'purchase_request.pr_item.merchant'|trans|e('js') }}",
                    no_ref: "{{ 'purchase_request.pr_item.no_ref'|trans|e('js') }}",
                    quantity: "{{ 'purchase_request.pr_item.quantity'|trans|e('js') }}",
                    unit_price: "{{ 'purchase_request.pr_item.unit_price'|trans|e('js') }}",
                    details: "{{ 'purchase_request.pr_item.details'|trans|e('js') }}",
                    see_more: "{{ 'purchase_request.pr_item.see_more'|trans|e('js') }}"
                },
                cart: {
                    add: "{{ 'purchase_request.cart.add'|trans|e('js') }}",
                    remove: "{{ 'purchase_request.cart.remove'|trans|e('js') }}"
                },
                offer: {
                    merchant: "{{ 'purchase_request.offer.merchant'|trans|e('js') }}",
                    manufacturer: "{{ 'purchase_request.offer.manufacturer'|trans|e('js') }}",
                    unit_price: "{{ 'purchase_request.offer.unit_price'|trans|e('js') }}",
                    batch_price: "{{ 'purchase_request.offer.batch_price'|trans|e('js') }}",
                    quantity: "{{ 'purchase_request.offer.quantity'|trans|e('js') }}",
                    see_more: "{{ 'purchase_request.offer.see_more'|trans|e('js') }}",
                    sku_price: "{{ 'purchase_request.offer.sku_price'|trans|e('js') }}",
                    select: "{{ 'purchase_request.offer.select'|trans|e('js') }}",
                    selected: "{{ 'purchase_request.offer.selected'|trans|e('js') }}",
                    error: {
                        businesseverywhere: "{{ 'purchase_request.offer.error_businesseverywhere'|trans|e('js') }}",
                        bafv: "{{ 'purchase_request.offer.error_bafv'|trans|e('js') }}",
                        noprice: "{{ 'purchase_request.offer.error_noprice'|trans|e('js') }}"
                    },
                    quantity_errors: {
                        too_much: "{{ 'offer_detail.too_much_quantity'|trans({'%max%':'_MAX_'})|e('js') }}",
                        too_small: "{{ 'offer_detail.too_small_quantity'|trans({'%min%':'_MIN_'})|e('js') }}",
                        not_batch_size_multiple: "{{ 'offer_detail.not_batch_size_multiple'|trans({'%batchSize%':'_BATCH_SIZE_'})|e('js') }}",
                    }
                }
            }
        };
    </script>
    {{ encore_entry_script_tags('purchase-request') }}
    {{ encore_entry_link_tags('purchase-request') }}

{% endblock %}
