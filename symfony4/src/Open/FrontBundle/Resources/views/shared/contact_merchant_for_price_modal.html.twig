<script type="text/template" id="js-contact-merchant-for-price-modal-tpl">
    <div class="Modal-inner">
        <div class="Modal-header">
            <h5 class="Modal-title">{{ 'offer_detail.ask_vendor'|trans([], 'AppBundle') }}</h5>
        </div>
        {{ form_start(contactMerchantToAskPriceForm, {'attr': {'id': 'contact_merchant_to_ask_price_form'}})}}
        <div class="Modal-body">
            <div class="form-group">
                {{ form_row(contactMerchantToAskPriceForm.message) }}
            </div>
        </div>

        <div class="Modal-footer">
            {{ form_widget(contactMerchantToAskPriceForm.save, {'attr': {'class': 'buttonModal'}}) }}
        </div>
        {{ form_end(contactMerchantToAskPriceForm) }}
    </div>
</script>

<script type="text/template" id="js-contact-merchant-for-no-price-offer-modal-tpl">
    <div class="Modal-inner">
        <div class="Modal-header">
            <h5 class="Modal-title">{{ 'offer_detail.ask_vendor_price'|trans([], 'AppBundle') }}</h5>
        </div>
        {{ form_start(contactMerchantToAskPriceForNoPriceOfferForm, {'attr': {'id': 'contact_merchant_to_ask_price_for_no_price_offer_form'}})}}
        <div class="Modal-body">
            <div class="form-group">
                {{ form_row(contactMerchantToAskPriceForNoPriceOfferForm.message) }}
            </div>
        </div>

        <div class="Modal-footer">
            {{ form_widget(contactMerchantToAskPriceForNoPriceOfferForm.save, {'attr': {'class': 'buttonModal'}}) }}
        </div>
        {{ form_end(contactMerchantToAskPriceForNoPriceOfferForm) }}
    </div>
</script>

{% block javascripts %}
<script>
    $(document).ready(function() {
        var sendMessageToMerchant = function($btn, idForm) {
            var url = $btn.data('url').toString();
            var merchantId = $btn.data('merchant-id');
            var merchantName = $btn.data('merchant-name');
            var ref = $btn.data('ref');

            var $form = $("#" + idForm);
            var $save = $("#" + idForm + "_save", $form);
            var $ref = $("#" + idForm + "_ref", $form);
            var $url = $("#" + idForm + "_url", $form);
            var $merchantId = $("#" + idForm + "_merchantId", $form);
            var $merchantName = $("#" + idForm + "_merchantName", $form);

            $ref.val(ref);
            $merchantId.val(merchantId);
            $merchantName.val(merchantName);

            $url.val(url);

            $form.removeAttr('action');
            $form.prop('action',url);

            $form.on('submit',function (e) {
                $save.attr("disabled", true);
                $save.addClass('btn-grey');
            });
        };

        $('.ask-vendor-for-price').on('click',function (e) {
            e.preventDefault();
            var $btn = $(this)

            UI.Modal.show('js-contact-merchant-for-price-modal','Modal--add', $($('#js-contact-merchant-for-price-modal-tpl').html()), true, true);

            sendMessageToMerchant($btn, 'contact_merchant_to_ask_price_form');
        })

        $('.ask-vendor-for-no-price-offer').on('click',function (e) {
            e.preventDefault();
            var $btn = $(this);

            UI.Modal.show('js-contact-merchant-for-no-price-offer-modal','Modal--add', $($('#js-contact-merchant-for-no-price-offer-modal-tpl').html()), true, true);

            sendMessageToMerchant($btn, 'contact_merchant_to_ask_price_for_no_price_offer_form');
        });
    });

</script>
{% endblock javascripts %}
