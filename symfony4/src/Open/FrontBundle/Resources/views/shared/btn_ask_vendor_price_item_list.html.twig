{% trans_default_domain 'AppBundle' %}
{# @var product \AppBundle\Model\Offer#}
{% if offerTitle is defined %}{% set title = offerTitle %}{% else %}{% set title = offer.offer.offerTitle %}{% endif %}
{% if product is not  defined %}{% set product = offer.offer %}{% endif %}
{% if bafv is defined and bafv is not empty and bafv.showBtn[product.merchant.id] %}
    {% set ref = title ~ " REF:"~ product.sellerRef %}
    {% set url = path('front.askprice') %}
    {% set hasSendTheRequestAllReady = product.merchant.id in bafv.merchant  %}

    <button
            type="button"
            data-productId="{{ product.productId }}"
            data-merchant-id="{{ product.merchant.id }}"
            data-merchant-name="{{ product.merchant.name }}"
            data-ref="{{ ref }}"
            data-url="{{ url }}"
            {% if hasSendTheRequestAllReady %}disabled{% endif %}
            style="padding: 4px 10px; width: 150px; font-size: 12px; {% if hasSendTheRequestAllReady %} pointer-events: none;{% endif %}"
            class="ask-vendor-for-price {% if hasSendTheRequestAllReady %}btn-grey{% else %} btn-primary {% endif %}"
    >
        {{ bafv.label[product.merchant.id] }}
    </button>

{% elseif product.noPrice and product.businessEverywhere %}
    {% set ref = title ~ " REF:"~ product.sellerRef %}
    {% set url = path('front.askprice_for_nopriceoffer') %}
    <button
        type="button"
        data-productId="{{ product.productId }}"
        data-merchant-id="{{ product.merchant.id }}"
        data-merchant-name="{{ product.merchant.name }}"
        data-ref="{{ ref }}"
        data-url="{{ url }}"
        style="padding: 4px 10px; width: 150px; font-size: 12px;"
        class="ask-vendor-for-no-price-offer  btn-primary"
    >
        {{ 'offer_detail.ask_vendor_price'|trans }}
    </button>

{% endif %}
