{% trans_default_domain 'AppBundle' %}

{% set nbPagesTab =  [] %}
{% set nbProducts = products|length %}
{% set step = (products|length < 4) ? products|length : 4 %}
{% for p in range(0, nbProducts, step) %}
    {% set nbPagesTab = nbPagesTab|merge([p]) %}
{% endfor %}
{% set nbPages = (nbPagesTab|length -1) %}


<div class="Product-List">
    <div class="list-head">
        <h2 class="Product-List-title">{{ title|trans }}</h2>
        {% if products|length > 4 %}
            <div class="list-pagination">
                <a id="prev-{{ id }}" onclick="showPrev('{{ id }}', '{{ nbPages }}')">
                    <svg class="Icon prev">
                        <use xlink:href="#icon-pagination-prev"></use>
                    </svg>
                </a>
                <div class="v-line"></div>
                <a id="next-{{ id }}" class="active" onclick="showNext('{{ id }}', '{{ nbPages }}')">
                    <svg class="Icon next">
                        <use xlink:href="#icon-pagination-next"></use>
                    </svg>
                </a>
            </div>
        {% endif %}
    </div>
    <div id="first-part-{{ id }}" class="Product-List-products">
        {% for o in products|slice(0, 4) %}
            {% include '@OpenFront/shared/_product.html.twig' with { product : o } %}
        {% endfor %}
    </div>
    {% if products|length > 4 %}
        <div id="second-part-{{ id }}" class="Product-List-products hide">
            {% for o in products|slice(4, 4) %}
                {% include '@OpenFront/shared/_product.html.twig' with { product : o } %}
            {% endfor %}
        </div>
    {% endif %}
    {% if products|length > 8 %}
        <div id="third-part-{{ id }}" class="Product-List-products hide">
            {% for o in products|slice(8, 4) %}
                {% include '@OpenFront/shared/_product.html.twig' with { product : o } %}
            {% endfor %}
        </div>
    {% endif %}
    {% if products|length > 12 %}
        <div id="fourth-part-{{ id }}" class="Product-List-products hide">
            {% for o in products|slice(12, 4) %}
                {% include '@OpenFront/shared/_product.html.twig' with { product : o } %}
            {% endfor %}
        </div>
    {% endif %}
</div>
