{% extends '@OpenFront/base.html.twig' %}
{% trans_default_domain 'AppBundle' %}


{% block title %}{{ 'home.title'|trans }}{% endblock %}

{% block meta_description %}{{ 'home.description'|trans }}{% endblock %}

{% block header_js %}
    <script src="{{ asset('js/jquery.sliderPro.min.js') }}"></script>
{% endblock %}

{% block body %}

    <div class="Homepage-slider slider-pro" id="js-home-slider">
        <div class="sp-slides">
            {% for slide in slides %}
                <div class="sp-slide">
                    {% if slide.button is defined and slide.button.name|length %}
                        {% set imgLink = false %}
                    {% else %}
                        {% set imgLink = true %}
                    {% endif %}
                    {% if imgLink %}
                        {% set target = (slide.isExternal) ? 'target="_blank"' : '' %}
                        <a href="{{ slide.url }}" {{ target }} class="no_hover">
                    {% endif %}
                    {% if empty_slides %}
                        <img class="sp-image" data-source="{{ asset('images/sliders_backgrounds/' ~ slide.image) }}"/>
                    {% else %}
                        <img class="sp-image" data-source="{{ path('front.get.image', {'id': slide.image }) }}"/>
                    {% endif %}
                    <div class="sp-layer" data-position="topCenter"  data-vertical="15%" >
                        <h1 class="Slide-title">{{ slide.title |raw }}</h1>
                        <p class="Slide-subTitle">{{ slide.text |raw }}</p>
                        {% if slide.button is defined and slide.button.name|length %}
                            {% set target = (slide.isExternal) ? 'onclick="window.open(\'' ~ slide.url ~ '\', \'_blank\');"' : 'onclick="window.location.href = \'' ~ slide.url ~ '\'"' %}
                            <button class="Slide-button btn-primary" {{ target|raw }}'">
                                {{ slide.button.name }}
                            </button>
                        {% endif %}
                    </div>
                    {% if imgLink %}
                        </a>
                    {% endif %}
                </div>
            {% endfor %}
        </div>

    </div>

    <div class="Homepage-slider Homepage-mobileSlider slider-pro" id="js-home-slider-mobile">
        <div class="sp-slides">
            {% for slide in slides %}
                <div class="sp-slide">
                        {% if slide.button is defined and slide.button.name|length %}
                            {% set imgLink = false %}
                        {% else %}
                            {% set imgLink = true %}
                        {% endif %}
                        {% set target = (slide.isExternal) ? 'target="_blank"' : '' %}
                        <a href="{{ slide.url }}" {{ target }} class="no_hover">
                        {% if empty_slides %}
                            <img class="sp-image" data-source="{{ asset('images/sliders_backgrounds/' ~ slide.image) }}"/>
                        {% else %}
                            <img class="sp-image" data-source="{{ path('front.get.image', {'id': slide.image }) }}"/>
                        {% endif %}
                        <div class="sp-layer" data-position="topCenter" data-vertical="5%" data-width="93%">
                            <h1 class="Slide-title">{{ slide.title |raw }}</h1>
                            <p class="Slide-subTitle">{{ slide.text |raw }}</p>
                            {% if slide.button is defined and slide.button.name|length %}
                                {% set target = (slide.isExternal) ? 'onclick="window.open(\'' ~ slide.url ~ '\',\'_blank\');"' : 'onclick="window.location.href = \'' ~ slide.url ~ '\'"' %}
                                <button class="Slide-button btn-primary" {{ target|raw }}">
                                    {{ slide.button.name }}
                                </button>
                            {% endif %}
                        </div>
                            </a>
                </div>

            {% endfor %}
        </div>
    </div>

    {#
    {% if logos %}
        <div id="logosSlider" class="logos-slider-container" style="background: #fff; padding: 15px 0">
            <div class="sp-slides">
                {% for logo in logos %}
                    <div class="sp-slide">
                        {% if logo.url %}<a target="_blank" href="{{ logo.url }}">{% endif %}
                            <img class="sp-image" src="{{ path('front.get.image', {'id': logo.img }) }}"/>
                            {% if logo.url %}</a>{% endif %}
                    </div>
                {% endfor %}
            </div>
        </div>
    {% endif %}
    #}

    {% if logos %}
        <div style="background: #fff; padding: 15px 0; width: 100%; margin: 0px; align-items: center; display: flex; flex-direction: row; justify-content: center;">
            {% for logo in logos|slice(0, 9) %}
                {% if logo.url %}<a target="_blank" href="{{ logo.url }}">{% endif %}
                    <img style="height: 50px; display: block; margin-left: 20px;" src="{{ path('front.get.image', {'id': logo.img }) }}"/>
                {% if logo.url %}</a>{% endif %}
            {% endfor %}
        </div>
    {% endif %}

    <!-- Disabled as per task #47620: https://tuleap.lundimatin.biz/plugins/tracker/?aid=47620 -->
    {#
    <div class="Homepage--products">
        {% if offers|length > 0 %}
          <h2>
            {{ 'products.home_product_list_header'|trans }}
          </h2>
        {% endif %}
        {% for o in offers|slice(0, 8) %}
            {% include '@OpenFront/shared/_product.html.twig' with { product : o } %}
        {% endfor %}
      <div class="Homepage--products--allOffers">
        <a class="button" href="{{ path('front.search') }}">{{ 'products.all_offers'|trans }}</a>
      </div>
    </div>
    #}

    <br>
    <div class="Homepage-why-buy">
        <h1>{{ 'home.why_buy.title'|trans }}</h1>
        <div class="why-container">
            <div class="why-bloc">
                <svg class="Icon">
                    <use xlink:href="#icon-why-fast"></use>
                </svg>
                <h2>
                    {{ 'home.why_buy.fast'|trans }}
                </h2>
                <p>{{ 'home.why_buy.fast_text'|trans }}</p>
            </div>
            <div class="why-bloc">
                <svg class="Icon">
                    <use xlink:href="#icon-why-simple"></use>
                </svg>
                <h2>
                    {{ 'home.why_buy.simple'|trans }}
                </h2>
                <p>{{ 'home.why_buy.simple_text'|trans }}</p>
            </div>
            <div class="why-bloc">
                <svg class="Icon">
                    <use xlink:href="#icon-why-certified"></use>
                </svg>
                <h2>
                    {{ 'home.why_buy.certified'|trans }}
                </h2>
                <p>{{ 'home.why_buy.certified_text'|trans }}</p>
            </div>
            </div>
    </div>
    {% include '@OpenFront/shared/contact_merchant_for_price_modal.html.twig' ignore missing with {
        'contactMerchantToAskPriceForm': contactMerchantToAskPriceForm,
        'contactMerchantToAskPriceForNoPriceOfferForm': contactMerchantToAskPriceForNoPriceOfferForm
    } %}
  <script type="text/template" id="js-video-tpl">
    <iframe width="{{ "{{ width }}" }}" height="{{ "{{ height }}" }}" src="https://www.youtube.com/embed/{{ "{{ src }}" }}?rel=0" frameborder="0" allowfullscreen></iframe>
  </script>
{% endblock %}

{% block javascripts %}

    <script type="text/javascript">
      // Scrolling disabled as per task #47620: https://tuleap.lundimatin.biz/plugins/tracker/?aid=47620
      /**
       * Initialize homepage slider with the correct ratio
       * @param screenWidth
       * @param screenHeight
       * @param navHeight
       * @param orientation
       * @param sliderOptions
       */
      /*$('#logosSlider').sliderPro({
          width: 200,
          height: 100,
          slideDistance: 30,
          visibleSize: '100%',
          forceSize: 'fullWidth',
          autoSlideSize: false,
          imageScaleMode: 'contain',
          autoplayDelay: 3000,
          buttons: false,
          keyboard: false
      });*/

      var initSlider = function(screenWidth, screenHeight, navHeight, orientation, sliderOptions) {
        // Calculate screen ratio (on available height)
        var ratio = screenWidth / (screenHeight - navHeight);
        if (screenWidth >= 960 && orientation === 'landscape') {

          // if more height than the image ratio then limit the ratio to the image ratio
          if (ratio < 2) {
            ratio = 3;
          }
          if($('#js-home-slider').find('.sp-slide').length <= 1) {
              sliderOptions.touchSwipe = false;
          }
          return jQuery('#js-home-slider').sliderPro(
            jQuery.extend({}, sliderOptions, {aspectRatio : 3.2 })
          ).data( 'sliderPro' );

        } else {

          if (ratio > 1.4) {
            ratio = 1.4;
          }
          if($('#js-home-slider-mobile').find('.sp-slide').length <= 1) {
              sliderOptions.touchSwipe = false;
          }
          return jQuery('#js-home-slider-mobile').sliderPro(
            jQuery.extend({}, sliderOptions, {aspectRatio : 2 })
          ).data( 'sliderPro' );
        }
      };

      [].forEach.call(document.querySelectorAll('.sp-image[data-source]'), function(img) {
          img.setAttribute('src', img.getAttribute('data-source'));
          img.onload = function() {
              img.removeAttribute('data-source');
          };
      });

      $( document ).ready(function() {
        var $w = $(window);
        var screenWidth = $('body').width();
        var screenHeight = $w.height();
        var slideHeigth = screenHeight / 1.7;
        var navHeight = $('#js-header').height();
        var $slider;
        var orientation = window.UI.Utils.getScreenOrientation();

        var sliderOptions = {
            width : 'auto',
            height: slideHeigth,
            slideDistance : 0,
            touchSwipe : Modernizr.touchevents,
            responsive: true,
            fade : true,
            fadeOutPreviousSlide : true,
            fadeDuration: 2000,
            fadeArrows : true,
            buttons: true,
            autoplayDelay : 8000,
            centerImage: true,
            keyboard: false
        };

        $slider = initSlider(screenWidth, screenHeight, navHeight, orientation, sliderOptions);


        window.UI.BrowserDetect.init();

        if (window.UI.BrowserDetect.getBrowser() === 'IE' && window.UI.BrowserDetect.getVersion() === 11 ) {
            $('html').addClass('ie11');
        }

        var $arrow = $('#js-scroll-arrow');

        var checkScroll = function () {
          // If user scrolled a bit then stop listening and hide the arrow
          if ($w.scrollTop() > 100) {
            $(window).off('scroll', checkScroll);
            $arrow.fadeOut('fast');
          }
        };

        // Bind click event on the arrow
        $arrow.on('click', function () {

          // Scroll a bit
          $('html, body').animate({ scrollTop:  $arrow.offset().top + 30 }, 'slow');

          // kill scroll listener
          $w.off('scroll', checkScroll);

          // hide the arrow
          $arrow.fadeOut('fast');
        });

        // Bind click event on the window to hide the arrow
        $w.on('scroll', checkScroll);

        // Listen for resize changes
        $w.on("resize", function() {
            var $w = $(window);
            var screenWidth = $('body').width();
            var screenHeight = $w.height();
            var slideHeigth = screenHeight / 1.7;
            var navHeight = $('#js-header').height();
            var $slider;
            var orientation = window.UI.Utils.getScreenOrientation();

            var sliderOptions = {
                width : 'auto',
                height: slideHeigth,
                slideDistance : 0,
                touchSwipe : Modernizr.touchevents,
                responsive: true,
                fade : true,
                fadeOutPreviousSlide : true,
                fadeDuration: 2000,
                //arrows : Modernizr.touchevents,
                fadeArrows : true,
                buttons: true,
                autoplayDelay : 8000
            };
            initSlider(screenWidth, screenHeight, navHeight, orientation, sliderOptions);
        });

          // Show video modal when clicking onthe slides buttons
          $('.js-video-play').on('click', function (ev) {
              var $videoContent;

              ev.preventDefault();

              var src = $(ev.currentTarget).data('videoSrc');

              $videoTemplate = Handlebars.compile($('#js-video-tpl').html());


              if (screenWidth >= 960 && UI.Utils.getScreenOrientation() === 'landscape') {

                  $videoContent = $videoTemplate({
                      'src' : src,
                      'width' : 960,
                      'height' : 540
                  });

              } else {

                  var height = (screenWidth * 9) / 16;

                  if (height >= screenHeight) {
                      height = screenHeight - 50;
                  }

                  $videoContent = $videoTemplate({
                      'src': src,
                      'width' : screenWidth,
                      'height' : height
                  });


              }

              $slider.stopAutoplay();

              UI.Modal.show('js-video-modal', 'Modal--homepageVideo', $videoContent, true, null, function () {
                  $slider.startAutoplay();
              });

          });

      });
    </script>
{% endblock %}
