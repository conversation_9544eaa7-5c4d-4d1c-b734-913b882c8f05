{% trans_default_domain 'AppBundle' %}
{% set activeIndex = 1 %}
{% if activeTab is defined and activeTab  is not empty %}
    {% set activeIndex = activeTab %}
{% endif %}
<div {% if class is defined %} class="tab-infos {{ class }}" {% else %}class="tab-infos" {% endif %}>
    <div {% if type is defined and type == 'CUSTOM' %} {% else %} class="desktop-only" {% endif %}>
        {% block tabdesktop %}
            <div class="header-container">
                <div class="tab-header">
                    {% for attribute in attributes %}
                        {% if attribute.route is defined and attribute.route is not empty %}
                            <div class="tab-title">
                                <a href="{{ path(attribute.route, attribute.query) }}">
                                    <h4 id="Tab-Title-{{ loop.index }}" class="tab-h4{{ loop.index == activeIndex ? ' active'}}">
                                        {{ attribute['title']|trans }}
                                    </h4>
                                </a>
                            </div>
                        {% else %}
                            <div class="tab-title" onclick="return window.UI.TabInfos.onClickTab({{ loop.index }});">
                                <h4 id="Tab-Title-{{ loop.index }}" class="tab-h4{{ loop.index == activeIndex ? ' active'}}">{{ attribute['title']|trans }}</h4>
                            </div>
                        {% endif %}

                    {% endfor %}

                    {% if export is defined %}
                        <div class="export">
                            <a href="{{ path(export) }}">{{ 'orders.list.export' | trans }}</a>
                        </div>
                    {% endif %}

                    {% if dataSheet is defined and dataSheet is not empty %}
                        <div class="datasheet">
                            <a href="{{ dataSheet }}" target="_blank" class="button">
                                <svg class="Icon stroke">
                                    <use xlink:href="#icon-side-profile"></use>
                                </svg>
                                {{ 'orders.list.download_pdf' | trans }}
                            </a>
                        </div>
                    {% endif %}
                </div>
                <div class="search-form">
                    {% if searchForm is defined %}
                        <div class="search">
                            {{ form_start(searchForm) }}
                            {{ form_row(searchFormRow) }}

                            <button type="submit"{% if searchFormId %} id="{{ searchFormId }}"{% endif %}>
                                <svg class="Icon">
                                    <use xlink:href="#icon-search"></use>
                                </svg>
                            </button>

                            {{ form_end(searchForm) }}
                        </div>
                    {% endif %}
                </div>
            </div>
            <div class="h-line"></div>

            <div id="Tab-Content">
                {% block content %}
                    {% for attribute in attributes %}
                        <div id="Tab-Content-{{ loop.index }}" class="tab-content{{ loop.index == 1 ? ' show' }}">
                            {% if attribute['type'] == 'GRID' %}
                                <div class="content-grid">
                                    {% for attr in attribute['data'] %}
                                        <div class="attribute">
                                            <p class="title">{{ attr.label }}</p>
                                            <p class="content">
                                                {% if checkPdf(attr.value) %}
                                                    <a href="{{ attr.value }}" target="_blank">
                                                        <svg class="Icon">
                                                            <use xlink:href="#icon-download-pdf"></use>
                                                        </svg>
                                                    </a>
                                                {% else %}
                                                    {{ attr.value }}
                                                {% endif %}
                                            </p>
                                        </div>
                                    {% endfor %}
                                </div>
                            {% elseif attribute['type'] == 'TEXT' %}
                                <div class="content-text">
                                    <p class="text">{{ attribute['data']|shortDescription|raw }}</p>
                                </div>
                            {% elseif attribute['type'] == 'TEXT_AND_IMAGE' %}
                                <div class="about-block">
                                    <div class="about-seller-container">
                                        <div class="about-seller-container-img">
                                            {% if attribute['data']['icon'] is not null %}
                                                <img src="{{ attribute['data']['icon'] }}" />
                                            {% endif %}
                                        </div>
                                        <div class="seller-info">
                                            <h3>{{ attribute['data']['title'] }}</h3>
                                            {% if attribute['data']['description']|length %}
                                                <div class="seller-text-title">{{ 'tab_infos_seller.presentation'|trans }}</div>
                                                <p class="seller-text">{{ attribute['data']['description']|raw }}</p>
                                            {% endif %}
                                        </div>
                                    </div>

                                    <a href="{{ path('front.search', {'commonFacetFilters[merchant:name][]': attribute['data']['title'], sortBy: 'relevance', 'searchType':'search.searchbar.in_marketplace'}) }}" class="link-product-seller">{{ 'tab_infos_seller.see_all_products'|trans }}</a>
                                    {% if attribute['data']['cgv'] is defined and is_granted("IS_AUTHENTICATED_REMEMBERED") %}
                                        {% if not offer.offer.limited and not offer.offer.getFrameContract() %}
                                            <a href="{{ attribute['data']['cgv'] }}" target="_blank" class="link-product-seller" style="margin-left:50px;">{{ 'tab_infos_seller.cgv'|trans }}</a>
                                        {% endif %}
                                        {% if offer.offer.getFrameContract() %}
                                            <br>{{ 'tab_infos_seller.frame_contract'|trans({'%frame_contract%': offer.offer.getFrameContract() }) }}
                                        {% endif %}
                                    {% endif %}
                                    {% if attribute['data']['minimum_order_amount'] is defined and attribute['data']['minimum_order_amount'] > 0  and is_granted("IS_AUTHENTICATED_REMEMBERED") %}
                                        <p class="minimum-order-amount">{{ 'tab_infos_seller.minimum_order_amount'|trans({'%amount%': attribute['data']['minimum_order_amount']| round  }) }}{{ offer.offer.currency|upper }}</p>
                                    {% endif %}
                                </div>
                            {% endif %}
                        </div>
                    {% endfor %}
                {% endblock %}
            </div>
        {% endblock %}
    </div>
        {% if type is defined and type == 'CUSTOM' %}
        {% else %}
        <div class="mobile-only">
                {% for attribute in attributes %}
                    <div class="tab-header">
                        <div class="tab-title" onclick="window.UI.TabInfos.onClickTab('mobile-{{ loop.index }}')">
                            <h4 id="Tab-Title-mobile-{{ loop.index }}" class="tab-h4">
                                {{ attribute['title']|trans }}
                                <div class="symbol symbol-plus">+</div>
                                <div class="symbol symbol-minus">-</div>
                            </h4>
                        </div>
                    </div>
                    <div id="Tab-Content">
                        <div id="Tab-Content-mobile-{{ loop.index }}" class="tab-content">
                            {% if attribute['type'] == 'GRID' %}
                                <div class="content-grid">
                                    {% for attr in attribute['data'] %}
                                        <div class="attribute">
                                            <p class="title">{{ attr.label }}</p>
                                            <p class="content">{{ attr.value }}</p>
                                        </div>
                                    {% endfor %}
                                </div>
                            {% elseif attribute['type'] == 'TEXT' %}
                                <div class="content-text">
                                    <p class="text">{{ attribute['data']|raw }}</p>
                                </div>
                            {% elseif attribute['type'] == 'TEXT_AND_IMAGE' %}
                                <div class="about-block">
                                    <div class="about-seller-container">
                                        {% if attribute['data']['icon'] is not null %}
                                            <div class="about-seller-container-img">
                                                <img src="{{ attribute['data']['icon'] }}" />
                                            </div>
                                        {% endif %}
                                        <div class="seller-info">
                                            <h3>{{ attribute['data']['title'] }}</h3>
                                            {% if attribute['data']['description']|length %}
                                                <div class="seller-text-title">{{ 'tab_infos_seller.presentation'|trans }}</div>
                                                <p class="seller-text">{{ attribute['data']['description']|raw }}</p>
                                            {% endif %}
                                        </div>
                                    </div>

                                    <a href="{{ path('front.search', {'commonFacetFilters[merchant:name][]': attribute['data']['title'], sortBy: 'relevance', 'searchType':'search.searchbar.in_marketplace'}) }}" class="link-product-seller">{{ 'tab_infos_seller.see_all_products'|trans }}</a>

                                    {% if attribute['data']['cgv'] is defined and is_granted("IS_AUTHENTICATED_REMEMBERED") %}
                                        <a href="{{ attribute['data']['cgv'] }}" target="_blank" class="link-product-seller" style="margin-left:50px;">{{ 'tab_infos_seller.cgv'|trans }}</a>
                                    {% endif %}
                                </div>
                            {% endif %}

                        </div>
                    </div>
                    {% if loop.index != 4 %}
                        <div class="h-line"></div>
                    {% endif %}
                {% endfor %}
        </div>
    {% endif %}
</div>
