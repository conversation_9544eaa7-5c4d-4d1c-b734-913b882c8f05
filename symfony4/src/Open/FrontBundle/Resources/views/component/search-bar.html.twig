{% trans_default_domain 'AppBundle' %}
{% form_theme formSearchBar 'Form/appli_layout.html.twig' %}

{% if user and user != "anon." and user.itemInComparisonSheet > 0 %}
    <a class="sticky-comparator mobile-only" href="{{ path("comparisonSheet.info") }}">
        <div class="sticky-label">{{ 'comparaisonSheet.sticky'|trans([], 'AppBundle') }}</div>
        <div class="sticky-number">{{ user.itemInComparisonSheet }}</div>
    </a>
{% endif %}

{% if isMobileSearch is defined and isMobileSearch %}

    <div id="Header-SearchBar" class="mobile-only{{ formSearchBar.searchTypes is not defined ? ' anonymous-search' }}">

        <div id="Search-bar">
            {{ form_start(formSearchBar, {'attr':{'id':'js-search-form', 'autocomplete': 'off', 'class': 'search-mobile'}, 'action': path('front.search')}) }}

            {% if formSearchBar.searchType is defined %}
                <div class="hidden-element">
                    {{ form_widget(formSearchBar.searchType, {'id': 'select-search-type'}) }}
                </div>
            {% endif %}

            <div class="element input-text">
                {{ form_widget(formSearchBar.text, {'id': 'input-search'}) }}
                <div id="autocomplete-list" class="hide">
                </div>
            </div>

            <div class="element-button">
                <button type="submit" id="search_bar_form_searchSubmit">
                    <svg class="Icon">
                        <use xlink:href="#icon-search"></use>
                    </svg>
                </button>
            </div>

            {{ form_end(formSearchBar) }}
        </div>

        {% if hasCatalog is defined and hasCatalog %}
            <div class="custom-search">
                <a href="#" onclick="showCustomSearch()">{{ 'search.searchbar.custom_search'|trans }}</a>
            </div>
        {% endif %}
    </div>
{% else %}
    <div id="Header-SearchBar" class="desktop-only{{ formSearchBar.searchTypes is not defined ? ' anonymous-search' }}">
        {% if user and user != "anon." and user.itemInComparisonSheet > 0 %}
            <a class="sticky-comparator" href="{{ path("comparisonSheet.info") }}">
                <div class="sticky-label">{{ 'comparaisonSheet.sticky'|trans([], 'AppBundle') }}</div>
                <div class="sticky-number">{{ user.itemInComparisonSheet }}</div>
            </a>
        {% endif %}
        <div id="Search-bar" class="desktop">
            {{ form_start(formSearchBar, {'attr':{'id':'js-search-form', 'autocomplete': 'off', 'class': 'search-dekstop'}, 'action': path('front.search')}) }}

            {% if formSearchBar.searchType is defined %}
                <div class="element select-department">
                    <div class="js-select-wrapper select-wrapper has-text">
                        {{ form_widget(formSearchBar.searchType, {'id': 'select-search-type'}) }}
                    </div>
                </div>
            {% endif %}

            {% if formSearchBar.facetFilters is defined %}
                {{ form_widget(formSearchBar.facetFilters) }}
            {% endif %}

            <div class="element input-text">
                {{ form_widget(formSearchBar.text, {'id': 'input-search' }) }}
                <div id="autocomplete-list" class="hide">
                </div>
            </div>
            <div class="element-button">
                <button type="submit" id="search_bar_form_searchSubmit">
                    <svg class="Icon">
                        <use xlink:href="#icon-search"></use>
                    </svg>
                </button>
            </div>
            {{ form_end(formSearchBar) }}
        </div>
    </div>
{% endif %}
<script type="text/javascript">
    window.UI.Select.init();
    var mainReference = null;
    var currentEntryAutocomplete = -1;

    // Defer the behaviour to let the time to the homepage remove the header searchbar
    $(document).ready(function() {
        document.getElementById('input-search').oninput = function () {
            let value = $(this).val();
            if (value.length >= 3) {
                let searchType = $('#select-search-type').val();
                $.ajax({
                    method: 'GET',
                    url: '{{ path('front.search.autocomplete') }}',
                    data: {
                        text: value,
                        searchType: searchType
                    },
                    success: function (data) {
                        fillAutoComplete(data);
                    },
                    error: function (XMLHttpRequest, textStatus, errorThrown) {
                        console.log(errorThrown);
                    }
                });
            } else {
                hideAutocomplete();
            }
        };

        document.getElementById("autocomplete-list").addEventListener("mouseover", function() {
            $('.option').removeClass('active');
            currentEntryAutocomplete = -1;
        });

        var listenerKeyDown = function(e) {
            var results = (document.getElementById("autocomplete-list")).getElementsByTagName("div");
            if (e.keyCode == 40) { // DOWN
                currentEntryAutocomplete++;
                addActive(results);
            } else if (e.keyCode == 38) { // UP
                currentEntryAutocomplete--;
                addActive(results);
            } else if (e.keyCode == 13) { // ENTER
                if (currentEntryAutocomplete > -1) {
                    results[currentEntryAutocomplete].click();
                }
            }
        };

        document.getElementById('input-search').onkeydown = listenerKeyDown;

        $(window).click(function(event) {
            let target = $(event.target);
            if (target.closest('#autocomplete-list').length === 0) {
                hideAutocomplete();
            }
        });


        function addActive(results) {
            $('.option').removeClass('active');
            if (currentEntryAutocomplete >= results.length) {
                currentEntryAutocomplete = 0;
            }
            if (currentEntryAutocomplete < 0) {
                currentEntryAutocomplete = (results.length - 1);
            }
            results[currentEntryAutocomplete].classList.add("active");
        };
    });

    var fillAutoComplete = function(data) {

        let offerAutocompleteData = function(data) {
            mainReference = "";

            let autoComplete = $('#autocomplete-list');
            cleanAutocomplete();

            let mainOffer = $('<div class="option" onclick="fillInput(this)"></div>');
            mainOffer.text(data.mainOfferTitle);
            autoComplete.append(mainOffer);

            if(data.mainOfferCategory) {
                let category = $('<div class="option category"></div>');
                let url = '{{ path('front.search') }}?'+['category='+data.mainOfferCategory].join('&');
                category.attr('data-id-category', data.mainOfferCategory);
                category.html('<a href="'+url+'"> ...' + data.mainOfferCategoryName + '</a>');
                autoComplete.append(category);
            }

            for (let i = 0; i < data.offerTitles.length; i++) {
                let option = $('<div class="option" onclick="fillInput(this)"></div>');
                option.text(data.offerTitles[i]);
                autoComplete.append(option);
            }
        };

        let querySuggestionAutocompleteData = function(data) {
            let autoComplete = $('#autocomplete-list');
            cleanAutocomplete();

            for (let i = 0; i < data.length; i++) {
                let option = $('<div class="option" onclick="fillInput(this)"></div>');
                option.text(data[i]);
                autoComplete.append(option);
            }
        };

        if(data && data.mainOfferTitle) {
            offerAutocompleteData(data);
            showAutocomplete();
            return;
        }

        if(data && data.length > 0) {
            querySuggestionAutocompleteData(data);
            showAutocomplete();
            return;
        }

        hideAutocomplete();
    };

    var fillInput = function(element) {
        let inputText = $('#input-search');
        inputText.val($(element).text());
        hideAutocomplete();
        submitForm();
    };

    var submitForm = function() {
        $('#js-search-form').submit();
    };

    var cleanAutocomplete = function() {
        let autoComplete = $('#autocomplete-list');
        autoComplete.children('div').each(function(e) {
            $(this).remove();
        });
    };

    var hideAutocomplete = function() {
        let autoComplete = $('#autocomplete-list');
        if(!autoComplete.hasClass('hide')) {
            autoComplete.addClass('hide');
        }
    };

    var showAutocomplete = function() {
        let autoComplete = $('#autocomplete-list');
        if(autoComplete.hasClass('hide')) {
            autoComplete.removeClass('hide');
        }
    };

    var showCustomSearch = function() {
        window.UI.Modal.showCustomSearch();
    };
</script>

{% if hasCatalog is defined and hasCatalog %}
    <script type="text/template" id="js-custom-search-modal-tpl">
        <div class="Modal-header">
            {{ 'company_catalog.custom_search.title'|trans }}
        </div>
        <div class="Modal-body">
            {{ form_start(formSearchBar, {'attr':{'id':'js-search-form-mobile', 'autocomplete': 'off', 'class': 'search-mobile'}, 'action': path('front.search')}) }}

            {% if formSearchBar.searchType is defined %}
                <div class="hidden-element">
                    {# hack for mobile search form widget not working #}
                    <select id="select-search-type-mobile" name="searchType" class="has-text">
                        <option value="search.searchbar.in_catalog" selected="selected">-</option>
                    </select>
                </div>
            {% endif %}

            <div class="element input-text">
                {# hack for mobile search form widget not working #}
                <input type="text" id="input-search-mobile" name="text" placeholder="{{ 'search.searchbar.mobile_placeholder'|trans }}">
            </div>

            <button type="submit">{{ 'search.title'|trans }}</button>

            {{ form_end(formSearchBar) }}
        </div>
        <div class="Modal-footer">
        </div>
    </script>
{% endif %}

