<li>
    <div class="footer-section">
        <div class="title-container">
            <h4 class="section-title">{{ title }}</h4>
            {% if id is defined %}
                <div id="cross-{{ id }}" onclick="window.UI.Footer.onClickCross('{{ id }}')" class="cross mobile-only">
                    <div class="horizontal"></div>
                    <div class="vertical"></div>
                </div>
            {% endif %}
        </div>
        <div {% if id is defined %} id="section-{{ id }}" class="section-content hide-mobile" {% else %} class="section-content" {% endif %}>
            <div class="section-column">
            {% for link in links %}
                {% set targetBlank = (link.targetBlank is defined and link.targetBlank) ? 'target="_blank"' : '' %}

                {% if link.URL is defined %}
                    <a class="footer-link" {{ targetBlank }} href="{{ link.URL }}">{{ link.name }}</a>
                {% else %}
                    <a class="footer-link">{{ link.name }}</a>
                {% endif %}
            {% endfor %}
            </div>
        </div>
    </div>
</li>
