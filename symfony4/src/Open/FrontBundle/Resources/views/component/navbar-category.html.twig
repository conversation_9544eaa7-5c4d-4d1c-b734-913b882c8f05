{% set count = 0 %}
{% set maxItems = 0 %}
{% for cat in category.children %}
    {% set maxItems = cat.children|length + 1 > maxItems ? cat.children|length + 1 : maxItems %}
    {% set count = count + cat.children|length + 1 %}
{% endfor %}

{% set nbItemsByColumn = (count / 5)|round(0, 'ceil') %}
{% if nbItemsByColumn < 5 %}
    {% set nbItemsByColumn = 5 %}
{% endif %}

{% if maxItems > nbItemsByColumn %}
    {% set nbItemsByColumn = maxItems %}
{% endif %}

<div class="category" style="height: {{ nbItemsByColumn * 30 + 40}}px">
    {% if category.children is defined %}
        {% for subCategory in category.children %}
                <div class="subcategory">
                    <div class="content">
                        <div class="subcategory-icon">
                            {% if subCategory.image is defined and subCategory.image is not null%}
                                <img src="{{ subCategory.image }}">
                            {% endif %}
                        </div>
                        <div class="subcategory-content">
                            <a href="{{ path('front.search', {'category': subCategory.id}) }}" class="subcategory-title">{{ subCategory.name }}</a>
                            {% for nestedCategory in subCategory.children %}
                                <a href="{{ path('front.search', {'category': nestedCategory.id}) }}" class="nestedCategory">{{ nestedCategory.name }}</a>
                            {% endfor %}
                        </div>
                    </div>
                </div>
        {% endfor %}
    {% endif %}
</div>
