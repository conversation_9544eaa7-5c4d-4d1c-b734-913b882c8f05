{% extends '@OpenFront/base.html.twig' %}
{% trans_default_domain 'AppBundle' %}

{% block title %}{{ 'company_catalog.title'|trans }}{% endblock %}

{% block meta_description %}{{ 'company_catalog.description'|trans }}{% endblock %}

{% block body %}
    <div class="Page-inner">
        <h1>{{ 'company_catalog.title'|trans }}</h1>

        {% if importInProgress %}

            <p class="infos">{{ 'company_catalog.import_in_progress'|trans }}</p>
            <div id="totalImported">
            </div>

        {% else %}

            <div class="infos-title">{{ 'company_catalog.instruction_title'|trans }}</div>
            <p class="infos">{{ 'company_catalog.instructions'|trans({'%link%':catalog_csv_template_download}) |raw }}</p>

            <div class="catalog-upload">
                {{ form_start(formUpload, {'attr': {'id': 'form-catalog'}}) }}
                {{ form_row(formUpload.upload, {'attr': {'class': 'btn-primary'}}) }}
                {{ form_end(formUpload) }}
                {% if hasCatalog %}

                    <p class="infos">{{ 'company_catalog.import.total_matching_references'|trans }} <b>{{ matchingReferences }}</b></p>
                    <p class="infos">{{ 'company_catalog.import.total_mismatching_references'|trans }} <b>{{ mismatchingReferences }}</b></p>

                    <button class="btn-primary" onclick="onDelete()">{{ 'company_catalog.delete'|trans }}</button>
                    <a href="{{ path('front.company_catalog.export') }}">
                        <button class="btn-primary">{{ 'company_catalog.export'|trans }}</button>
                    </a><br>
                    <a href="{{ path('front.company_catalog.export.mismatching') }}">
                        <button class="btn-primary">{{ 'company_catalog.export_mismatching'|trans }}</button>
                    </a>
                    <a href="{{ path('front.company_catalog.export.matching') }}">
                        <button class="btn-primary">{{ 'company_catalog.export_matching'|trans }}</button>
                    </a>
                {% endif %}
            </div>

        {% endif %}

    </div>
{% endblock %}

{% block javascripts %}
    <script type="text/javascript">

        var loadingModal = null;
        var confirmModal = null;
        var deleteModal = null;
        var hasCatalog = '{{ hasCatalog }}';

        var closeLoading = function() {
            if(loadingModal !== null) {
                loadingModal.close();
            }
        };

        $("#company_catalog_form_upload").on('change', function(){uploadFile()});

        var importReferenceCatalogue = function()
        {
            $('#form-catalog').submit(function(event) {
                event.preventDefault();

                var $form = $(this);
                var url = $form.attr('action');
                var data = new FormData($form.get(0));

                $.ajax({
                    url: url,
                    data: data,
                    cache: false,
                    processData: false,
                    contentType: false,
                    type: 'POST',
                    success: function () {
                        window.location = '{{ path('front.company_catalog') }}';
                    }
                });
            }).submit();
        };

        function uploadFile() {
            if(hasCatalog) {
                confirmModal = window.UI.Modal.confirm('', "{{ 'company_catalog.overwrite'|trans([], 'AppBundle')|raw }}", function() {
                    loadingModal = window.UI.Modal.showLoading();
                    importReferenceCatalogue();
                }, function() {
                    confirmModal.close();
                    confirmModal = null;
                    $('#company_catalog_form_upload').val("");
                });
            } else {
                loadingModal = window.UI.Modal.showLoading();
                importReferenceCatalogue();
            }
        }

        function onDelete() {
            deleteModal = window.UI.Modal.confirm("", "{{ 'company_catalog.delete_confirm'|trans({}, 'AppBundle')|raw }}",
                function () {
                    loadingModal = window.UI.Modal.showLoading();
                    $.ajax({
                        type: 'DELETE',
                        url: '{{ path('front.company_catalog.delete') }}',
                        success: function () {
                            window.location.href = '{{ path('front.company_catalog') }}';
                        },
                        error: function (response, textStatus, errorThrown) {
                            closeLoading();
                            loadingModal = window.UI.Modal.alert('{{ 'company_catalog.delete_error'|trans|raw }}');
                        }
                    });
                }, function() {
                    deleteModal.close();
                });
        }

        var checkCatalogueImportProgression = function() {
            $.ajax({
                type: 'GET',
                url: '{{ path('front.company_catalog.import_checker') }}',
                success: function(data) {
                    let status = data.status;
                    let totalImported = data.totalImported;

                    refreshTotalCatalogueReferencesImported(totalImported);

                    if (status === 'finished') {
                        window.location = '{{ path('front.company_catalog') }}';
                    } else {
                        setTimeout(checkCatalogueImportProgression, 1000);
                    }
                }
            });
        };

        var refreshTotalCatalogueReferencesImported = function(total) {
            var totalImported = $('#totalImported');

            totalImported.html(
                '<p class="infos"><span>'+total+'</span> {{ 'company_catalog.imported_references'|trans }}</p>'
            );
        };

        {% if importInProgress %}
            checkCatalogueImportProgression();
        {% endif %}
    </script>
{% endblock %}
