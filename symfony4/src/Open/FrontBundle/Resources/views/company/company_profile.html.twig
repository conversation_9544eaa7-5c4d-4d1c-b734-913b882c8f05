{% trans_default_domain 'AppBundle' %}
{% extends '@OpenFront/menu/buyer_menu.html.twig' %}
{% form_theme form 'Form/appli_layout.html.twig' %}

{% block title %}
    {{ 'menu.desktop.profile'|trans }}
{% endblock %}

{% block body %}
<div class="Page-inner">

    <div class="Form CompanyProfileForm">

        {{ form_start(form) }}
        <div class="form-title"><h1>{{ 'company.form.profile'|trans }}</h1></div>
        <div class="flex-container">
            {{ form_row(form.firstname) }}
            {{ form_row(form.lastname) }}
        </div>
        <div class="flex-container">
            {{ form_row(form.mainPhoneNumber) }}
            {{ form_row(form.email, {'attr':{'disabled':'disabled'}}) }}
        </div>

        <div class="flex-container">
            <div class="form-row padding-lang">
                <div class="js-select-wrapper select-wrapper has-text">
                    {{ form_widget(form.locale) }}
                    {{ form_label(form.locale)}}
                </div>
                <small class="error">{{ form_errors(form.locale) }}</small>
            </div>
        </div>

        <div class="form-row CompanyProfilePasswordChange">
            <a href="{{ path('fos_user_change_password') }}" class="CompanyProfilePasswordLink"><span>{{ 'company.form.password.change'|trans }}</span></a>
        </div>
        {{ form_end(form) }}
    </div>
</div>
<script>
    'use strict';
    document.addEventListener('DOMContentLoaded', function() {
      {{ form_jquery_validation(form) }}
    });

    window.UI.Select.init();
</script>
{% endblock %}
