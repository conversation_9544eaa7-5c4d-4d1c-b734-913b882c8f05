{% extends '@OpenFront/menu/buyer_menu.html.twig' %}
{% form_theme form 'Form/appli_layout.html.twig' %}
{% trans_default_domain 'AppBundle' %}
{% block title %}
    {{ 'payment_mode.title' | trans }}
{% endblock %}

{% block body %}

    <div class="Form CompanyForm PaymentModeForm{{ showBuyerMenu is defined and showBuyerMenu != true or showBuyerMenu is not defined ? ' user-active-background' }}">
        {{ form_start(form, {'attr':{'id':'js-company-form'}}) }}
            <div class="form-title"><h1>{{ 'payment_mode.title'|trans }}</h1></div>

            <p class="info-payment">
                {{ 'payment_mode.info'|trans }}</br>
                {% if company.termpaymentMoneyTransfertEnabled %}
                    {{ 'payment_mode.enabled'|trans }}
                {% endif %}
                {%  if not company.termpaymentMoneyTransfertPending and not company.termpaymentMoneyTransfertEnabled %}
                    {{ 'payment_mode.click_button'|trans }}
                {% endif %}
            </p>

            <div class="invisible-checkboxes">
                <div class="form-row-container">
                    {{ form_row(form.prepaymentCreditcardEnabled) }}
                </div>
                <div class="form-row-container">
                    {{ form_row(form.prepaymentMoneyTransfertEnabled) }}
                </div>
                <div class="form-row-container">
                    {{ form_row(form.termpayment_moneytransfert_enabled) }}
                </div>
            </div>

            {% if company.termpaymentMoneyTransfertPending %}
                <h2>{{ 'payment_mode.pending'|trans }}</h2>
            {% endif %}

            {% if not company.termpaymentMoneyTransfertEnabled %}
                <div class="form-row Buttons">
                    <div class="col-md-12">
                        {% if disabled %}
                            <p>{{ 'payment.form.not_authorized' | trans }}</p>
                        {% elseif not company.termpaymentMoneyTransfertPending and not company.termpaymentMoneyTransfertEnabled %}
                            {{ form_row(form.submit) }}
                        {% endif %}
                    </div>
                </div>
            {% endif %}

        {% if form.businessRegistration is defined and app.user.company.step == 3 %}
            <div id="businessRegistrationDiv ">
                <div id="js-upload-bizreg" style="padding-top: 30px;" class="DocumentForm-uploadField" data-target-list-id="js-businessRegistration-docs">
                    {{ form_row(form.businessRegistration) }}
                </div>

                <div id="js-businessRegistration-nodocs" class="Messages DocumentForm-nodocs"  {% if (company.documents|length) > 0 %} style="display:none"  {% endif %}>
                    <div class="Message-item Message--error">
                        {{ 'document.noDoc'|trans }}
                    </div>
                </div>

                <div id="js-businessRegistration-upload-message" class="Messages DocumentForm-nodocs" style="display: none;">
                    <div class="Message-item Message--error">
                    </div>
                </div>

                <ul id="js-businessRegistration-docs" class="DocumentForm-docs">
                    {% for document in company.documents %}
                        <li>
                            <a href="{{ path('front.document.view',{id:document.id}) }}" target="_blank">{{ document.filename }}</a>

                            {% if not disabled %}
                                <svg class="IconMax js-doc-remove" data-doc-id="{{ document.id }}" data-company-id="{{ company.id }}" data-doc-type="businessRegistration">
                                    <use xlink:href="#icon-cancel"></use>
                                </svg>
                            {% endif %}
                        </li>
                    {% endfor %}
                </ul>

                <div id="progressBusinessRegistration" class = "Message--progress"></div>
                <button type="button" class="js-upload-file-mobile Button Button--upload Button--uploadMobile" data-target="js-upload-bizreg">
                    <svg class="Icon">
                        <use xlink:href="#icon-upload"></use>
                    </svg>
                    {{ 'document.upload.title'|trans }}
                </button>
            </div>
        {% endif %}

        {{ form_row(form._token) }}

        {{ form_end(form, {'render_rest':false}) }}

        {% if showBuyerMenu is defined and showBuyerMenu == true %}
            <div class="Form-group Buttons-group">
                <a href="{{ path('front.company.users') }}" class="button grey-button">{{ 'company.form.back'|trans }}</a>
                <a href="{{ path('homepage') }}" class="button">{{ 'company.form.finish'|trans }}</a>
            </div>
        {% endif %}
    </div>

    <script type="text/javascript">
        'use strict';

        document.addEventListener('DOMContentLoaded', function() {

            var $form = $('#js-company-form');

            var $businessRegistration = $('#js-businessRegistration-docs');
            var $businessRegistrationUploadButton = $('#js-button-company_payment_modes_form_businessRegistration');
            var $businessRegistrationAuth = $('#company_payment_modes_form_businessRegistration');

            // enable/disable RequestAuthorization for term-payment button
            var requestAuthorizationForTermPaymentButton = {
                businessDocumentContainer : $businessRegistration,
                button: $('#company_payment_modes_form_submit'),

                init: function() {
                    let requestAuthorizationForTermPaymentButton = this;
                    requestAuthorizationForTermPaymentButton.toggle();

                    // listen when businessDocumentContainer change
                    // inspired by https://developer.mozilla.org/en-US/docs/Web/API/MutationObserver
                    const observer = new MutationObserver(function() {
                        requestAuthorizationForTermPaymentButton.toggle();
                    });

                    observer.observe(this.businessDocumentContainer.get(0), { childList: true })
                },

                toggle: function() {
                    let hasDocuments = ($('li', this.businessDocumentContainer).length > 0);
                    (hasDocuments) ? this.enable() : this.disable();
                },

                enable: function() {
                    this.button.removeAttr('disabled').removeClass('btn-disabled');
                },

                disable: function() {
                    this.button.attr('disabled', 'disabled').addClass('btn-disabled');
                }
            };

            requestAuthorizationForTermPaymentButton.init();

            // Bind click events on remove buttons
            $form.on('click', '.js-doc-remove', function (ev) {
                var $target = $(ev.currentTarget);
                var docType = $target.data('docType');
                var docId = parseInt($target.data('docId'), 0);
                var companyId = parseInt($target.data('companyId'), 0);

                UI.Modal.confirm('', '{{ 'document.upload.delete'|trans({}, 'AppBundle') }}', function () {
                        var $parent = $target.parent();
                        var $cont = $parent.parent();

                        $.ajax(
                            Routing.generate('front.document.remove'),
                            {
                                type: 'POST',
                                dataType: 'json',
                                data: {
                                    docId: docId,
                                    companyId: companyId,
                                    docType: docType
                                },
                                success: function (data) {
                                    $parent.fadeOut('fast', function () {
                                        $parent.remove();

                                        $('#' + 'js-' + docType + '-upload-message').css('display', 'none');

                                        if ($cont.find('li').length === 0) {
                                            $('#' + 'js-' + docType + '-nodocs').css('display', 'block');

                                            $cont.hide();

                                            // Reset file counter for the validation
                                            $cont.parent().find('input[type=file]').data('count', 0);
                                        }
                                    });
                                },
                                error: function (response, textStatus, errorThrown) {
                                    alert('{{ 'document.upload.deleteError'|trans }}');
                                    console.log('Error [' + errorThrown + '] : ' + response.responseText); //Todo: flash user about the error
                                }
                            }
                        );
                    },
                    function () {
                        console.log('Confirmation canceled');
                    });
            });

            $businessRegistrationUploadButton.click(function () {
                $(this).parent().find('label').click();
            });

            // Initialize file upload
            $businessRegistrationAuth.fileupload(
                UI.UploadDocument.manageUpload(
                    '{{ path(  'company.upload.businessRegistration'  , {id:company.id}) }}',
                    $('#progressBusinessRegistration'),
                    $('#js-businessRegistration-upload-message'),
                    $businessRegistration,
                    $('#js-businessRegistration-nodocs'),
                    'businessRegistration',
                    $businessRegistrationUploadButton,
                    '{{ company.id }}',
                    '{{ typeError }}',
                    '{{ sizeMax }}',
                    '{{ sizeError }}'
                )
            );

            // Tweak validation of the sites checkbox (1 site checked required unless role is admin)
            $('input[name="company_payment_modes_form[identification]"]').rules(
                'add',
                {
                    'company-identification' : true,
                    'messages' : {
                        'company-identification': '{{ 'form.company.ident_number.invalid'|trans({}, 'validators') }}'
                    }
                }
            );

            // Handle clicks on fake upload buttons
            $('.js-upload-file-mobile').on('click', function (ev) {
                var $target = $(ev.currentTarget);
                $('#' + $target.data('target')).find('.js-button-upload').click();
            });


            $('input[type=file]').each(function (index, el) {
                var $el = $(el)
                var $parent = $el.parent().parent();
                var $list = $('#' + $parent.data('targetListId'));

                var nbFiles = $list.find('li').length;

                $el.data('count', nbFiles);

                if (nbFiles <= 0) {
                    $list.hide();
                }
            });

            {{ form_jquery_validation(form) }}

            var uploadRequiredRules = {
                required: false,
                uploadRequired: true,
                'messages': {
                    'uploadRequired': '{{ 'form.company.fileupload.invalid'| trans({}, 'validators') }}'
                }
            };

            {% if form.businessRegistration is defined %}
            $('input[name="company_document_form[businessRegistration][]"]').rules('add', uploadRequiredRules);
            {% endif %}

        });

    </script>

{% endblock %}

