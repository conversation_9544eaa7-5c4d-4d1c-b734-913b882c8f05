{% trans_default_domain 'AppBundle' %}
{% extends '@OpenFront/menu/menu.html.twig' %}
{% form_theme form 'Form/appli_layout.html.twig' %}

{% block title %}
    {{ 'menu.desktop.contact' | trans }}
{% endblock %}

{% block body %}
<div class="Page-header">

</div>
<div class="Page-inner">

    <div class="Form CompanyContactForm">

        {{ form_start(form, {'attr':{'id':'js-company-contact-form'}}) }}
        <div class="form-title"><h2>{{ 'company.form.contact.main'|trans }}</h2></div>
        {% do form.mainContact.check.setRendered %}
        <div class="flex-container">
        {{ form_row(form.mainContact.firstName) }}
        {{ form_row(form.mainContact.lastName) }}
        {{ form_row(form.mainContact.email) }}
        {{ form_row(form.mainContact.phone1) }}
        {{ form_row(form.mainContact.phone2) }}
        {{ form_row(form.mainContact.function) }}
        </div>

        <div class="form-title">
            <h2>{{ 'company.form.contact.billing'|trans }}</h2>
        </div>
        <div class="form-check-row">
            {{ form_row(form.billingContact.check,{ 'label' : 'company.form.contact.check.billing'}) }}
        </div>
        <div id="js-billing-address" class="CompanyContactSuppForm">

            <div class="flex-container">
            {{ form_row(form.billingContact.firstName) }}
            {{ form_row(form.billingContact.lastName) }}
            {{ form_row(form.billingContact.email) }}
            {{ form_row(form.billingContact.phone1) }}
            {{ form_row(form.billingContact.phone2) }}
            {{ form_row(form.billingContact.function) }}
            </div>

        </div>

        <div class="form-title">
            <h2>{{ 'company.form.contact.adv'|trans }}</h2>
        </div>
        <div class="form-check-row">
            {{ form_row(form.advContact.check,{ 'label' : 'company.form.contact.check.adv' }) }}
        </div>

        <div id="js-adv-address" class="CompanyContactSuppForm">
            <div class="flex-container">
            {{ form_row(form.advContact.firstName) }}
            {{ form_row(form.advContact.lastName) }}
            {{ form_row(form.advContact.email) }}
            {{ form_row(form.advContact.phone1) }}
            {{ form_row(form.advContact.phone2) }}
            {{ form_row(form.advContact.function) }}
            </div>
        </div>

        <div class="form-title">
            <h2>{{ 'company.form.contact.logistic'|trans }}</h2>
            <h3>{{ 'company.form.contact.logistic_subtitle'|trans }}</h3>
        </div>
        <div class="form-check-row">
            {{ form_row(form.logisticContact.check,{ 'label' : 'company.form.contact.check.logistic' }) }}
        </div>

        <div id="js-logistic-address" class="CompanyContactSuppForm">
            <div class="flex-container">
            {{ form_row(form.logisticContact.firstName) }}
            {{ form_row(form.logisticContact.lastName) }}
            {{ form_row(form.logisticContact.email) }}
            {{ form_row(form.logisticContact.phone1) }}
            {{ form_row(form.logisticContact.phone2) }}
            {{ form_row(form.logisticContact.function) }}
            </div>
        </div>


        {% if not isDisabled %}
            <div class="Form-group Buttons-group">
                {{ form_row(form.save) }}
            </div>
        {% endif %}

        {{ form_end(form) }}

        <script type="text/javascript">
            'use strict';

            document.addEventListener('DOMContentLoaded', function() {

                UI.OpenDiv.createCheckAction({
                    "divId": "#js-billing-address",
                    "checkId": "#company_contact_form_billingContact_check"
                });

                UI.OpenDiv.createCheckAction({
                    "divId": "#js-adv-address",
                    "checkId": "#company_contact_form_advContact_check"
                });

                UI.OpenDiv.createCheckAction({
                    "divId": "#js-logistic-address",
                    "checkId": "#company_contact_form_logisticContact_check"
                });

              {{ form_jquery_validation(form) }}

            });

        </script>
    </div>
</div>
{% endblock %}
