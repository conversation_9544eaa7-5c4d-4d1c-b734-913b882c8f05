{% extends '@OpenFront/menu/buyer_menu.html.twig' %}
{% trans_default_domain 'AppBundle' %}

{% block title %}
    {{ 'menu.desktop.wishlist' | trans }}
{% endblock %}

{% block body %}
    <div class="Page-inner">
        <div class="wishlist-container">
            {% if list|length > 0 %}
                {% for wishList in list %}
                    <div class="wishlist">
                        <div class="details">
                            <a href="{{ path('front.wishlist.details', {'wishlistId':wishList.id}) }}">
                                <h1>{{ wishList.name }}</h1>
                            </a>
                        </div>
                        <div class="actions">
                            <button onclick="onDelete({{ wishList.id }})">
                                <svg class="Icon">
                                    <use xlink:href="#icon-site-delete"></use>
                                </svg>
                            </button>
                        </div>
                    </div>
                {% endfor %}
            {% else %}
                <div class="empty-wishlist">
                    <h1>{{ 'wishlist.none'|trans }}</h1>
                    <img class="cart-empty-img desktop-only" src="{{ asset('images/empty-cart-temporary.png') }}">
                </div>
            {% endif %}
        </div>
    </div>
{% endblock %}

{% block javascripts %}
    <script type="text/javascript">
        function onDelete(wishListId) {
            deleteModal = window.UI.Modal.confirm("", "{{ 'wishlist.delete_confirm'|trans({}, 'AppBundle') }}",
            function () {
                loadingModal = window.UI.Modal.showLoading();
                $.ajax({
                    type: 'DELETE',
                    url: '{{ path('wishlist.delete') }}',
                    data: {
                        'wishListId' : wishListId
                    },
                    success: function () {
                        location.reload();
                        messageModal = window.UI.Modal.alert("{{ 'wishlist.delete.success'|trans({}, 'AppBundle') }}");
                    },
                    error: function (response, textStatus, errorThrown) {
                        messageModal = window.UI.Modal.alert("{{ 'wishlist.delete.error'|trans({}, 'AppBundle') }}");
                    }
                });
            }, function() {
             deleteModal.close();
            });
        }
    </script>
{% endblock %}
