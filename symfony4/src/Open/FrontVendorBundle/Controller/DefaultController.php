<?php

namespace Open\FrontVendorBundle\Controller;

use AppBundle\Controller\MkoController;
use <PERSON><PERSON>\Bundle\FrameworkExtraBundle\Configuration\Security;
use Symfony\Component\Routing\Annotation\Route;

class DefaultController extends MkoController
{
    #[\Symfony\Component\Routing\Attribute\Route(path: '/front/vendor/dashboard/', name: 'vendor_homepage', options: ['i18n' => false])]
    #[\Symfony\Component\Routing\Attribute\Route(path: '/front/vendor/specific-prices/', name: 'vendor_specific_prices_page', options: ['i18n' => false])]
    #[\Symfony\Component\Routing\Attribute\Route(path: '/front/vendor/catalog/', name: 'vendor_catalog_page', options: ['i18n' => false])]
    public function indexAction()
    {
        return $this->render('@OpenFrontVendor/default/index.html.twig', ["user" => $this->getUser()]);
    }

    #[\Symfony\Component\Routing\Attribute\Route(path: '/front/vendor/logout/info', name: 'vendor.logout.page', options: ['i18n' => false])]
    public function logoutInfoAction()
    {
        return $this->render('@OpenFrontVendor/default/logout-info.html.twig', []);
    }
}
