<?php

namespace Open\FrontVendorBundle\Model;

class BuyerSpecificPrice
{
    /** @var string $companyCode */
    private $companyCode;

    /** @var string $companyName */
    private $companyName;

    /** @var string $lastUpdate */
    private $lastUpload;

    /** @var int $nbProducts */
    private $nbProduct;


    public function getCompanyCode(): string
    {
        return $this->companyCode;
    }

    public function setCompanyCode(string $companyCode): BuyerSpecificPrice
    {
        $this->companyCode = $companyCode;
        return $this;
    }

    public function getCompanyName(): string
    {
        return $this->companyName;
    }

    public function setCompanyName(string $companyName): BuyerSpecificPrice
    {
        $this->companyName = $companyName;
        return $this;
    }

    public function getLastUpload(): string
    {
        return $this->lastUpload;
    }

    public function setLastUpload(string $lastUpload): BuyerSpecificPrice
    {
        $this->lastUpload = $lastUpload;
        return $this;
    }

    public function getNbProduct(): int
    {
        return $this->nbProduct;
    }

    public function setNbProduct(int $nbProduct): BuyerSpecificPrice
    {
        $this->nbProduct = $nbProduct;
        return $this;
    }


}
