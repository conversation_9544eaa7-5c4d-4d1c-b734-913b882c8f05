<?php

namespace Open\WebhelpBundle;

use Exception;
use Symfony\Component\HttpKernel\Exception\HttpException;

class ApiException extends HttpException
{
    /**
     * @var array $externalContext additional key/value information for this error
     */
    private $externalContext = [];

    /**
     * @var string $apiCall
     */
    private $apiCall;

    /**
     * the izberg error code
     * @var integer $izbergCode
     */
    private $webhelpCode = null;

    /**
     * the error message from Webhelp
     * @var string $izbergMessage the Webhelp message
     */
    private $webhelpMessage;

    //define your how exception so the message is not optional
    public function __construct($message, $code = 500, Exception $previous = null)
    {
        parent::__construct($code, $message, $previous);
    }

    public function __toString()
    {
        return __CLASS__ . ": [{$this->code}]: {$this->message}\n";
    }

    public function getContext()
    {
        $context = [];
        $context['httpStatusCode'] = $this->getStatusCode();
        $context['webhelpMessage'] = $this->getWebhelpMessage();
        $context['webhelpCode'] = $this->getWebhelpCode();
        $context['apiCall'] = $this->getApiCall();
        return array_merge($context, $this->externalContext);
    }

    /**
     * @return array
     */
    public function getExternalContext(): array
    {
        return $this->externalContext;
    }

    /**
     * @param array $externalContext
     */
    public function setExternalContext(array $externalContext): void
    {
        $this->externalContext = $externalContext;
    }

    /**
     * @return string
     */
    public function getApiCall(): string
    {
        return $this->apiCall;
    }

    /**
     * @param string $apiCall
     */
    public function setApiCall(string $apiCall): void
    {
        $this->apiCall = $apiCall;
    }

    public function getWebhelpCode()
    {
        return $this->webhelpCode;
    }

    /**
     * @param int $webhelpCode
     */
    public function setWebhelpCode($webhelpCode): void
    {
        $this->webhelpCode = $webhelpCode;
    }

    public function getWebhelpMessage(): ?string
    {
        return $this->webhelpMessage;
    }

    /**
     * @param string $webhelpMessage
     */
    public function setWebhelpMessage(string $webhelpMessage): void
    {
        $this->webhelpMessage = $webhelpMessage;
    }
}
