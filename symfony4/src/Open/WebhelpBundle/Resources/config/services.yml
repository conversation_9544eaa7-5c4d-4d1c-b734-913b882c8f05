services:
    # default configuration for services in *this* file
    _defaults:
        autowire: true      # Automatically injects dependencies in your services.
        autoconfigure: true # Automatically registers your services as commands, event subscribers, etc.

    Open\WebhelpBundle\Controller\:
        resource: '../../Controller/'
        tags: [ 'controller.service_arguments' ]

    Open\WebhelpBundle\Api\CustomerApi:
        class: Open\WebhelpBundle\Api\CustomerApi
        tags: [ 'webhelp.api' ]

    Open\WebhelpBundle\Api\TransactionApi:
        class: Open\WebhelpBundle\Api\TransactionApi
        tags: [ 'webhelp.api' ]

    Open\WebhelpBundle\Api\MerchantApi:
        class: Open\WebhelpBundle\Api\MerchantApi
        tags: [ 'webhelp.api' ]
