<?php

namespace Open\WebhelpBundle\Api;

class ApiConnection
{
    /** @var string  */
    private $domain;

    /** @var string  */
    private $scheme;

    /** @var string  */
    private $username;

    /** @var string  */
    private $password;

    public function __construct(string $domain, string $scheme, string $username, string $password)
    {
        $this->domain = $domain;
        $this->scheme = $scheme;
        $this->username = $username;
        $this->password = $password;
    }

    public function getDomain(): string
    {
        return $this->domain;
    }

    public function getScheme(): string
    {
        return $this->scheme;
    }

    public function getUsername(): string
    {
        return $this->username;
    }

    public function getPassword(): string
    {
        return $this->password;
    }
}
