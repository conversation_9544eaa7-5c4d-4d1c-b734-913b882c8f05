<?php

namespace Open\WebhelpBundle\Api;

use Open\WebhelpBundle\Api;

/***
 * Class CustomerApi
 *
 * @package Open\WebhelpBundle\Api
 */
class CustomerApi extends Api
{
    const CREATE_CUSTOMER = "createCustomer";
    const CALL_SUCCESS = "WEBHELP API Call Successfull";

    public function createCustomer($code, $email, $corporateName, $vatNumber, $language, $billingCity, $billingCountryCode, $billingStreet, $billingZipcode)
    {
        $countryCode = $billingCountryCode;
        //fix here for country code: Izberg use UK code while WPS use GB
        //https://fr.wikipedia.org/wiki/ISO_3166-1
        if (strtolower($billingCountryCode) === "uk") {
            $countryCode = "GB";
        }

        $data = [
            'code' => $code,
            'email' => $email,
            'corporateName' => $corporateName,
            'language' => $language,
            'billingAddress' => [
                'city' => $billingCity,
                'countryCode' => $countryCode,
                'street' => $billingStreet,
                'zipCode' => $billingZipcode,
            ],
        ];

        // On n'envoi le vat number que s'il n'est pas null
        if (!is_null($vatNumber) && !empty($vatNumber)) {
            $data['vatNumber'] = $vatNumber;
        }

        $response = $this->sendApiRequest(
            self::HTTP_POST_OPERATION,
            self::CREATE_CUSTOMER,
            $data
        );

        $response = json_decode($response->raw_body, true);

        if ($response === null) { // WRONG JSON
            $this->throwGenericError(
                'WRONG JSON RESPONSE',
                __CLASS__ . '::' . __METHOD__
            );
            return null;
        }

        // Log this
        $this->writeGenericInfoLog(
            self::CALL_SUCCESS,
            __CLASS__ . '::' . __METHOD__,
            $data,
            $response
        );

        return $response['idCustomerWPS'];
    }
}
