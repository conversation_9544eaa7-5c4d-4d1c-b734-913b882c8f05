<?php

namespace Open\WebhelpBundle\Api;

use Open\WebhelpBundle\Api;
use Open\WebhelpBundle\Model\SubTransactionResponse;
use Open\WebhelpBundle\ApiException;
use Open\WebhelpBundle\Model\TransactionResponse;

class TransactionApi extends Api
{
    const createTransaction = "createTransaction";
    const createSubTransaction = "createSubTransaction";
    const cancelSubTransaction = "cancelSubTransaction";
    const charge = "charge";
    const refund = "refund";

    const CALL_SUCCESS = "WEBHELP API Call Successfull";

    const STATUS_CREATED = 'CREATED';
    const STATUS_CAPTURED = 'CAPTURED';
    const STATUS_CANCELLED = 'CANCELLED';
    const STATUS_CHARGED = 'CHARGED';

    const PAYMENT_CONDITION_PP_CB = 'PP_CB';
    const PAYMENT_CONDITION_PE_SEPA = 'PE_SEPA';
    const PAYMENT_CONDITION_PE_VIREMENT = 'PE_VIREMENT';

    /***
     * @param $amount
     * @param $codeSubTransactionWps
     * @param $invoiceNumberWPS
     *
     * @return string
     * @throws ApiException
     */
    public function refund($amount, $codeSubTransactionWps, $invoiceNumberWPS = null): ?string
    {
        $data = array(
            'amount' => (string)$amount,
            'codeSubTransactionWps' => $codeSubTransactionWps,
            'invoiceNumberWPS' => $invoiceNumberWPS
        );

        $response = $this->sendApiRequest(
            self::HTTP_POST_OPERATION,
            self::refund,
            $data
        );

        $payload = json_decode($response->raw_body, true);

        if ($payload === null) { // WRONG JSON
            $this->throwGenericError(
                'WRONG JSON RESPONSE',
                __CLASS__ . '::' . __METHOD__,
                $response->raw_body
            );

            return null;
        }

        $this->writeGenericInfoLog(
            self::CALL_SUCCESS,
            __CLASS__ . '::' . __METHOD__,
            $data,
            $payload
        );

        return $payload['codeRefundWPS'];
    }


    /***
     *
     *  charge a sub-transaction if payment with card.
     *
     * @param $amount
     * @param $codeSubTransactionWps
     *
     * @return \Open\WebhelpBundle\Api\ChargeResponse
     */
    public function charge($amount, $codeSubTransactionWps): ?ChargeResponse
    {
        $data = [
            'amount' => (string)$amount,
            'codeSubTransactionWps' => $codeSubTransactionWps,
        ];

        $response = $this->sendApiRequest(
            self::HTTP_POST_OPERATION,
            self::charge,
            $data
        );

        $payload = json_decode($response->raw_body, true);

        if ($payload === null) { // WRONG JSON
            $this->throwGenericError(
                'WRONG JSON RESPONSE',
                __CLASS__ . '::' . __METHOD__,
                $response->raw_body
            );
            return null;
        }

        // should return CHARGED
        return new ChargeResponse($payload);
    }


    /***
     *
     * cancel a sub-transaction if payment with card
     *
     * @param $amount
     * @param $codeTransactionWps
     *
     * @return SubTransactionResponse
     */
    public function cancelSubTransaction($amount, $codeTransactionWps): ?SubTransactionResponse
    {
        $data = [
            'amount' => (string)$amount,
            'codeSubTransactionWps' => $codeTransactionWps,
        ];

        $response = $this->sendApiRequest(
            self::HTTP_POST_OPERATION,
            self::cancelSubTransaction,
            $data
        );

        $payload = json_decode($response->raw_body, true);

        if ($payload === null) { // WRONG JSON
            $this->throwGenericError(
                'WRONG JSON RESPONSE',
                __CLASS__ . '::' . __METHOD__,
                $response->raw_body
            );
            return null;
        }

        return new SubTransactionResponse($payload);
    }


    /***
     *
     * Create a sub-transaction : a part of the transaction, one for each merchant. Charge if payment without card.
     *
     * @param $amount
     * @param string $code Must be unique per transaction, will not be used elsewhere. (max length 255)
     * @param string $codeTransactionWps The transaction code used for the communication with WPS. (length max 35, alphanumeric)
     * @param string $idMerchantWps The merchant code used for the communication with WPS
     * @param string $orderId The order id. (max length 255)
     *
     * @return SubTransactionResponse
     */
    public function createSubTransaction(
        $amount,
        $code,
        $codeTransactionWps,
        $idMerchantWps,
        $orderId
    ): ?SubTransactionResponse
    {
        $data = [
            'amount' => (string)$amount,
            'code' => $code,
            'codeTransactionWps' => $codeTransactionWps,
            'idMerchantWps' => $idMerchantWps,
        ];

        if ($orderId !== null) {
            $data['orderId'] = $orderId;
        }

        $response = $this->sendApiRequest(
            self::HTTP_POST_OPERATION,
            self::createSubTransaction,
            $data
        );

        $payload = json_decode($response->raw_body, true);

        if ($payload === null) { // WRONG JSON
            $this->throwGenericError(
                'WRONG JSON RESPONSE',
                __CLASS__ . '::' . __METHOD__,
                $response->raw_body
            );

            return null;
        }

        return new SubTransactionResponse($payload);
    }

    /**
     *
     * Create a transaction corresponding to the customer order.
     *
     * @param $amount
     * @param $code
     * @param $currency
     * @param $idCustomerWPS
     * @param $customerLanguage
     * @param array $installments (could be an array of DateTime)
     * @param $paymentCondition (PE_SEPA, PP_CB, PE_VIREMENT)
     * @param $subTransationsNumber (minimum 1)
     * @param string|null $normalReturnUrl The url to which the customer will be redirected after a payment with SIPS, required if CB
     *
     * @return TransactionResponse
     * @throws ApiException
     */
    public function createTransaction(
        $amount,
        $code,
        $currency,
        $idCustomerWPS,
        $customerLanguage,
        array $installments,
        $paymentCondition,
        $subTransationsNumber,
        ?string $normalReturnUrl = null
    ): ?TransactionResponse
    {
        $cancelRule = true;

        $data = [
            'amount' => (string)$amount,
            'cancelRule' => $cancelRule,
            'code' => $code,
            'currency' => $currency,
            'idCustomerWPS' => $idCustomerWPS,
            'customerLanguage' => $customerLanguage,
            'paymentCondition' => $paymentCondition,
            'subTransationsNumber' => $subTransationsNumber,
            'installmentsNumber' => 0,
        ];

        $formattedInstallments = $this->array2Installments($installments);

        if (count($formattedInstallments) >= 1) {
            $data['installmentsNumber'] = count($formattedInstallments);
            $data['installments'] = $formattedInstallments;
        }

        if ($paymentCondition == self::PAYMENT_CONDITION_PP_CB) {
            $data['normalReturnUrl'] = $normalReturnUrl;
        }

        $response = $this->sendApiRequest(
            self::HTTP_POST_OPERATION,
            self::createTransaction,
            $data
        );

        $payload = json_decode($response->raw_body, true);

        if ($payload === null) { // WRONG JSON
            $this->throwGenericError(
                'WRONG JSON RESPONSE',
                __CLASS__ . '::' . __METHOD__,
                $response->raw_body
            );

            return null;
        }

        return new TransactionResponse($payload);
    }

    /**
     * conversion vers le format demandé
     * @param $elem
     *
     * @return string
     */
    private function toInstallment($elem)
    {
        if ($elem instanceof \DateTime) {
            return $elem->format("Y-m-d");
        }

        // Assume it is a string (well formatted)
        return $elem;
    }

    /***
     * Avec ça on pourra prendre un tableau de date ou de string
     * @param $tab
     *
     * @return array
     */
    private function array2Installments($tab): array
    {
        $tab = (is_array($tab)) ? $tab : [$tab];

        return array_map(
            function($elem) {
                return $this->toInstallment($elem);
            },
            $tab
        );
    }
}
