<?php

namespace Open\WebhelpBundle\Api;

class ChargeResponse
{
    private $response;

    public function __construct($response)
    {
        $this->response = $response;
    }

    /***
     * @return mixed
     */
    public function getAmountReconciled()
    {
        return $this->response['amountReconciled'];
    }

    /***
     * @return mixed The subtransaction code used for the communication with WPS. (unique, length max 35, alphanumeric)
     */
    public function getCodeSubTransactionWps()
    {
        return $this->response['codeSubTransactionWps'];
    }

    /***
     *
     * @return mixed An explanation if a failure occurs
     */
    public function getReason()
    {
        return $this->response['reason'];
    }

    /***
     * @return mixed subtransaction creation date. ISO 8601 format, UTC (ex: 2018-01-01T00:00:00.000+0000)
     */
    public function getCreationDate()
    {
        return $this->response['creationDate'];
    }

    /**
     * @return bool|\DateTime subtransaction creation date
     */
    public function getCreationDateTime()
    {
        return \DateTime::createFromFormat('Y-m-d\TH:i:sO', $this->getCreationDate());
    }

    /***
     * @return mixed subtransaction reconciliation (the bank operation for this transaction is done) date. ISO 8601 format, UTC (ex: 2018-01-01T00:00:00.000+0000)
     */
    public function getReconciliationDate()
    {
        return $this->response['reconciliationDate'];
    }

    /***
     * @return bool|\DateTime subtransaction reconciliation
     */
    public function getReconciliationDateTime()
    {
        return \DateTime::createFromFormat('Y-m-d\TH:i:sO', $this->getReconciliationDate());
    }

    /***
     * @return mixed [ PENDING, REFUSED, CANCELLED, CAPTURED, CHARGED, REFUND, RECONCILED, UNPAID, RECONCILIATION_ERROR ]
     */
    public function getStatus()
    {
        return $this->response['status'];
    }


    /***
     * @return mixed the status of the parent transaction of this subtransaction  [REFUSED, CREATED, ABORTED, CANCELLED, CAPTURED
     */
    public function getStatusTransaction()
    {
        return $this->response['statusTransaction'];
    }


    /**
     * @return mixed Cancel code (unique)
     */
    public function getCodeCancelSubTransactionWPS()
    {
        return $this->response['codeCancelSubTransactionWPS'];
    }
}
