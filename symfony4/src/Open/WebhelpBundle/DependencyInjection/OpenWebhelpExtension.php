<?php

namespace Open\WebhelpBundle\DependencyInjection;

use AppBundle\Services\SecurityService;
use Open\WebhelpBundle\Api\ApiConnection;
use Symfony\Component\DependencyInjection\ContainerBuilder;
use <PERSON>ymfony\Component\Config\FileLocator;
use Symfony\Component\DependencyInjection\Reference;
use Symfony\Component\HttpKernel\DependencyInjection\Extension;
use Symfony\Component\DependencyInjection\Loader;

/**
 * This is the class that loads and manages your bundle configuration.
 *
 * @link http://symfony.com/doc/current/cookbook/bundles/extension.html
 */
class OpenWebhelpExtension extends Extension
{

    /**
     * {@inheritdoc}
     */
    public function load(array $configs, ContainerBuilder $container)
    {
        $configuration = new Configuration();
        $config = $this->processConfiguration($configuration, $configs);

        $container->register(ApiConnection::class, ApiConnection::class)
            ->addArgument($config['domain'])
            ->addArgument($config['scheme'])
            ->addArgument($config['username'])
            ->addArgument($config['password']);

        $loader = new Loader\YamlFileLoader($container, new FileLocator(__DIR__.'/../Resources/config'));
        $loader->load('services.yml');

        $taggedServices = $container->findTaggedServiceIds('webhelp.api');
        foreach($taggedServices as $id => $tags) {
            $definition = $container->findDefinition($id);
            $definition
                ->setArguments(
                    [
                        new Reference(ApiConnection::class),
                        new Reference(SecurityService::class),
                    ]
                );
        }
    }
}
