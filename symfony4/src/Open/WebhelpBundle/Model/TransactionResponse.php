<?php

namespace Open\WebhelpBundle\Model;

class TransactionResponse
{
  private $response;

  public function __construct($response)
  {
    $this->response = $response;
  }

  /***
   * @return mixed The transaction code used for the communication with WPS. (unique, length max 35, alphanumeric)
   */
  public function getCodeTransactionWps()
  {
    return $this->response['codeTransactionWps'];
  }

  /***
   *
   * @return mixed An explanation if a failure occurs
   */
  public function getReason()
  {
    return $this->response['reason'];
  }

  /***
   * @return mixed
   */
  public function getReconciliationKey()
  {
    return $this->response['reconciliationKey'];
  }

  /***
   * @return mixed If payment with debit card, contains the url to which redirect the customer so as he can proceed to payment

   */
  public function getRedirectionCardUrl()
  {
    return $this->response['redirectionCardUrl'];
  }

  /***
   * @return mixed [ REFUSED, CREATED, ABORTED, CANCELLED, CAPTURED ]
   */
  public function getStatus()
  {
    return $this->response['status'];
  }

  /***
   * @return mixed If payment with debit card, contains transaction informations
   */
  public function getTicketCard()
  {
    return $this->response['ticketCard'];
  }

}
