<?php

namespace Open\IzbergBundle;

class ApiRequestParameter
{
    /**
     * @var string
     */
    private $method;

    /**
     * @var string
     */
    private $route;

    /**
     * @var array
     */
    private $data;

    /**
     * @var array
     */
    private $headers;

    /** @var int */
    private $startTime;

    /** @var int */
    private $endTime;

    /** @var array */
    private $attributes;

    public function __construct(string $method, string $route, array $data = [], array $headers = [])
    {
        $this->method = $method;
        $this->route = $route;
        $this->data = $data;
        $this->headers = $headers;
        $this->startTime = 0;
        $this->endTime = 0;
        $this->attributes = [];
    }

    public function getMethod(): string
    {
        return $this->method;
    }

    public function getRoute(): string
    {
        return $this->route;
    }

    public function getData(): array
    {
        return $this->data;
    }

    public function getHeaders(): array
    {
        return $this->headers;
    }

    public function startRequest()
    {
        $this->startTime = intval(microtime(true));
    }

    public function endRequest()
    {
        $this->endTime = intval(microtime(true));
    }

    public function executionTime()
    {
        if (!$this->startTime || !$this->endTime) {
            return false;
        }

        return $this->endTime - $this->startTime;
    }

    public function setAttribute(string $attributeName, string $attributeValue)
    {
        $this->attributes[$attributeName] = $attributeValue;
    }

    public function getAttribute(string $attributeName)
    {
        return $this->attributes[$attributeName] ?? null;
    }
}
