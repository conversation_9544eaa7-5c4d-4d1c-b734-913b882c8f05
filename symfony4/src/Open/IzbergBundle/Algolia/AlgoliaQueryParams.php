<?php

namespace Open\IzbergBundle\Algolia;

class AlgoliaQueryParams
{
    /**
     * @var array
     */
    private $restrictedSearchableAttributes = [];

    /**
     * @var array
     */
    private $facetFilters = [];

    /**
     * @var array
     */
    private $filters = [];

    /**
     * @var array
     */
    private $notFilters = [];

    /**
     * @var array
     */
    private $disjunctiveFilters = [];

    /**
     * @var array
     */
    private $facets = [];

    /**
     * @var int|null
     */
    private $offset = null;

    /**
     * @var int|null
     */
    private $length = null;

    /**
     * @var bool|null
     */
    private $typoTolerance = null;

    /**
     * @var array
     */
    private $numericFilters = [];

    private ?string $orQuery = null;

    public function addFacets(string ...$facets): self
    {
        $this->facets = array_unique(
            array_merge(
                $this->facets,
                $facets
            )
        );

        return $this;
    }

    public function addNumericFilters(...$numericFilters): self
    {
        $this->numericFilters = array_values(
            array_merge(
                $this->numericFilters,
                $numericFilters
            )
        );

        return $this;
    }

    public function addFacetFilters(...$facetFilters): self
    {
        $this->facetFilters = array_values(
            array_merge(
                $this->transformNumKeysToAssocKeys($this->facetFilters),
                $this->transformNumKeysToAssocKeys($facetFilters)
            )
        );

        return $this;
    }

    public function addDisjunctiveFilters(...$filters): self
    {
        foreach($filters as $filter) {
            [$filterName, $filterValue] = explode(':', $filter);

            if (!isset($this->disjunctiveFilters[$filterName])) {
                $this->disjunctiveFilters[$filterName] = [];
            }

            if (!in_array($filter, $this->disjunctiveFilters[$filterName])) {
                $this->disjunctiveFilters[$filterName][] = $filter;
            }
        }

        return $this;
    }

    public function addFilters(...$filters): self
    {
        $this->filters = array_values(
            array_merge(
                $this->transformNumKeysToAssocKeys($this->filters),
                $this->transformNumKeysToAssocKeys($filters)
            )
        );

        return $this;
    }

    public function addNotFilters(string ...$notFilters): self
    {
        $this->notFilters = array_values(
            array_merge(
                $this->transformNumKeysToAssocKeys($this->notFilters),
                $this->transformNumKeysToAssocKeys($notFilters)
            )
        );

        return $this;
    }

    public function resetFilters(): self
    {
        $this->notFilters = [];
        $this->filters = [];

        return $this;
    }

    public function addRestrictSearchableAttributes(string ...$restrictedSearchableAttributes): self
    {
        $this->restrictedSearchableAttributes = array_unique(
            array_merge(
                $this->restrictedSearchableAttributes,
                $restrictedSearchableAttributes
            )
        );

        return $this;
    }

    public function offset(int $offset): self
    {
        $this->offset = $offset;

        return $this;
    }

    public function length(int $length): self
    {
        $this->length = $length;

        return $this;
    }

    public function typoTolerance(bool $typoTolerance): self
    {
        $this->typoTolerance = $typoTolerance;

        return $this;
    }

    public function orQuery(?string $orQuery): self
    {
        $this->orQuery = $orQuery;

        return $this;
    }

    public function getRestrictedSearchableAttributes(): array
    {
        return $this->restrictedSearchableAttributes;
    }

    public function getFacetFilters(): array
    {
        return $this->facetFilters;
    }

    public function getFacets(): array
    {
        return $this->facets;
    }

    public function getFilters(): array
    {
        return $this->filters;
    }

    public function hasDisjunctiveFilters(): bool
    {
        return (count($this->disjunctiveFilters) >= 1);
    }

    public function disjunctiveArray(): array
    {
        $array = $this->toArray();

        if (\count($this->disjunctiveFilters)) {
            $array['facetFilters'] = array_keys($this->disjunctiveFilters);
        }

        return $array;
    }

    public function toArray(): array
    {
        $queryParams = [];

        if (null !== $this->typoTolerance) {
            $queryParams['typoTolerance'] = $this->typoTolerance;
        }

        if (null !== $this->orQuery) {
            $queryParams['optionalWords'] = $this->orQuery;
        }

        if (null !== $this->offset) {
            $queryParams['offset'] = $this->offset;
        }

        if (null !== $this->length) {
            $queryParams['length'] = $this->length;
        }

        if (count($this->numericFilters)) {
            $queryParams['numericFilters'] = $this->numericFilters;
        }

        if (count($this->restrictedSearchableAttributes)) {
            $queryParams['restrictSearchableAttributes'] = $this->restrictedSearchableAttributes;
        }

        if (count($this->filters)) {
            $queryParams['filters'] = $this->arrayToAndOrString($this->filters);
        }

        if (count($this->notFilters)) {
            $queryParams['filters'] = $this->arrayToAndNotString($queryParams['filters'] ?? '', $this->notFilters);
        }

        if (count($this->facetFilters)) {
            $queryParams['facetFilters'] = $this->facetFilters;
        }

        if (count($this->disjunctiveFilters)) {
            $queryParams['facetFilters'] =
                array_values(
                    array_merge(
                        $this->facetFilters,
                        $this->disjunctiveFilters
                    )
            );
        }

        if (count($this->facets)) {
            $queryParams['facets'] = $this->facets;
        }

        return $queryParams;
    }

    private function transformNumKeysToAssocKeys(array $array): array
    {
        $assocKeys = array_map(function($value) {
            return json_encode($value);
        }, $array);

        return array_combine($assocKeys, $array);
    }

    private function arrayToAndOrString(array $array, $isNumericFilters = false): string
    {
        $sanatizeValue = function($value) use ($isNumericFilters) {
            if (!$isNumericFilters) {
                [$attribute, $value] = explode(':', $value);
                $value = sprintf('%s:"%s"', $attribute, $value);
            }

            return $value;
        };

        $array = array_map(function($arrayValue) use ($sanatizeValue) {
            if (is_array($arrayValue)) {
                $arrayValue = array_map($sanatizeValue, $arrayValue);
                $wrap = (count($arrayValue) > 1);
                $arrayValue = implode(' OR ', $arrayValue);
                $arrayValue = ($wrap) ? '(' . $arrayValue . ')' : $arrayValue;
            } else {
                $arrayValue = call_user_func($sanatizeValue, $arrayValue);
            }
            return $arrayValue;
        }, $array);

        return implode(' AND ', $array);
    }

    private function arrayToAndNotString(string $filterString, array $array): string
    {
        $array = array_map(function($arrayValue) {
            if (is_array($arrayValue)) {
                $arrayValue = array_map(
                    function($value) {
                        [$attribute, $value] = explode(':', $value);
                        return sprintf('%s:"%s"', $attribute, $value);
                    },
                    $arrayValue
                );
                $wrap = (count($arrayValue) > 1);
                $arrayValue = implode(' OR NOT ', $arrayValue);
                $arrayValue = ($wrap) ? '(' . $arrayValue . ')' : $arrayValue;
            } else {
                [$attribute, $value] = explode(':', $arrayValue);
                $arrayValue = sprintf('%s:"%s"', $attribute, $value);
            }
            return $arrayValue;
        }, $array);

        if (!empty($filterString)) {
            return $filterString . ' AND NOT ' . implode(' AND NOT ', $array);
        }

        return 'NOT ' . implode(' AND NOT ', $array);
    }
}
