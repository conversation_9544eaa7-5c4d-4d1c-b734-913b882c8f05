<?php

namespace Open\IzbergBundle\Algolia;

class AlgoliaField
{
    public const CURRENCY = 'currency';
    public const DESCRIPTION = 'description';
    public const NAME = 'name';
    public const SKU = 'sku';
    public const STATUS = 'status';
    public const STOCK = 'stock';

    public const MERCHANT_NAME = 'merchant.name';
    public const MERCHANT_STATUS = 'merchant.status';
    public const MERCHANT_ID = 'merchant.id';

    public const PRODUCT_CATEGORY = 'product.application_categories';
    public const PRODUCT_CATEGORY_NAME = 'product.application_categories_dict.name';
    public const PRODUCT_GTIN = 'product.gtin';
    public const PRODUCT_KEYWORDS = 'product.keywords';
    public const PRODUCT_MADE_IN = 'product.made_in';
    public const PRODUCT_MANUFACTURER = 'product.manufacturer';
    public const PRODUCT_STATUS = 'product.status';
}
