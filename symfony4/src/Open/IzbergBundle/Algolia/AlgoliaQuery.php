<?php

namespace Open\IzbergBundle\Algolia;

class AlgoliaQuery
{
    /**
     * @var string
     */
    private $search;

    /**
     * @var AlgoliaQueryParams
     */
    private $queryParams;

    /**
     * @var bool
     */
    private $active;

    public function __construct(string $search, AlgoliaQueryParams $queryParams)
    {
        $this->search = $search;
        $this->queryParams = $queryParams;
        $this->active = true;
    }

    /**
     * @return string
     */
    public function getSearch(): string
    {
        return $this->search;
    }

    /**
     * @param string $search
     */
    public function setSearch(string $search): void
    {
        $this->search = $search;
    }

    /**
     * @return AlgoliaQueryParams
     */
    public function getQueryParams(): AlgoliaQueryParams
    {
        return $this->queryParams;
    }

    /**
     * @param AlgoliaQueryParams $queryParams
     */
    public function setQueryParams(AlgoliaQueryParams $queryParams): void
    {
        $this->queryParams = $queryParams;
    }

    public function isActive(): bool
    {
        return $this->active;
    }

    public function disable()
    {
        $this->active = false;
    }

    public function enable()
    {
        $this->active = true;
    }
}
