<?php

namespace Open\IzbergBundle\Algolia;

use Algolia\AlgoliaSearch\SearchClient;
use Algolia\AlgoliaSearch\SearchIndex;

abstract class QuerySuggestionService
{
    /**
     * @var SearchIndex
     */
    private SearchIndex $algoliaQuerySuggestionIndex;

    /**
     * @throws \Exception
     */
    public function __construct(string $applicationId, string $apiKey, string $querySuggestionIndex)
    {
        $algoliaClient = SearchClient::create($applicationId, $apiKey);
        $this->algoliaQuerySuggestionIndex = $algoliaClient->initIndex($querySuggestionIndex);
    }

    public function search(string $query, array $args = []): array
    {
        $defaultArgs = [
            'hitsPerPage' => 10,
            'page' => 0,
            'attributesToRetrieve' => '*'
        ];

        $args = $defaultArgs + $args;

        $response = $this->algoliaQuerySuggestionIndex->search($query, $args);
        $hits = $response['hits'] ?? [];

        return array_column($hits, 'query');
    }
}
