<?php

namespace Open\IzbergBundle\DependencyInjection;

use Symfony\Component\Config\Definition\Builder\ArrayNodeDefinition;
use Symfony\Component\Config\Definition\Builder\TreeBuilder;
use Symfony\Component\Config\Definition\ConfigurationInterface;

/**
 * This is the class that validates and merges configuration from your app/config files.
 *
 * To learn more see {@link http://symfony.com/doc/current/cookbook/bundles/configuration.html}
 */
final class Configuration implements ConfigurationInterface
{
    public function getConfigTreeBuilder(): TreeBuilder
    {
        $treeBuilder = new TreeBuilder('open_izberg');
        $rootNode = $treeBuilder->getRootNode();

        $this->addDefaultSection($rootNode);
        $this->addOtherConnectionSection($rootNode);

        return $treeBuilder;
    }

    public function addDefaultSection(ArrayNodeDefinition $node)
    {
        $node
            ->children()
            ->arrayNode('default_connection')
                ->children()
                    ->scalarNode('domain')->defaultNull()->end()
                    ->scalarNode('protocol')->defaultValue('http')->end()
                    ->integerNode('version')->defaultValue(1)->end()
                    ->scalarNode('access_token')->defaultNull()->end()
                    ->scalarNode('application_id')->defaultNull()->end()
                    ->scalarNode('application_namespace')->defaultNull()->end()
                    ->scalarNode('secret_key')->defaultNull()->end()
                    ->scalarNode('email')->defaultNull()->end()
                    ->scalarNode('username')->defaultNull()->end()
                    ->scalarNode('first_name')->defaultNull()->end()
                    ->scalarNode('last_name')->defaultNull()->end()
                    ->scalarNode('jwt_secret')->defaultNull()->end()
                    ->scalarNode('domain_seller')->defaultNull()->end()
                    ->scalarNode('seller_email_domain')->defaultNull()->end()
                    ->scalarNode('create_merchant_url')->defaultNull()->end()
                    ->scalarNode('identity_api_url')->defaultNull()->end()
                    ->scalarNode('client_id')->defaultNull()->end()
                    ->scalarNode('client_secret')->defaultNull()->end()
                    ->scalarNode('domain_id')->defaultNull()->end()
                    ->scalarNode('audience')->defaultNull()->end()
                ->end()
            ->end();
    }

    public function addOtherConnectionSection(ArrayNodeDefinition $node)
    {
        $node
            ->children()
            ->arrayNode('other_connections')
                ->useAttributeAsKey('configuration_name')
                ->arrayPrototype()
                    ->children()
                        ->scalarNode('domain')->end()
                        ->scalarNode('protocol')->end()
                        ->integerNode('version')->end()
                        ->scalarNode('access_token')->end()
                        ->scalarNode('application_id')->end()
                        ->scalarNode('application_namespace')->end()
                        ->scalarNode('secret_key')->end()
                        ->scalarNode('email')->end()
                        ->scalarNode('username')->end()
                        ->scalarNode('first_name')->end()
                        ->scalarNode('last_name')->end()
                        ->scalarNode('jwt_secret')->end()
                        ->scalarNode('domain_seller')->end()
                        ->scalarNode('seller_email_domain')->end()
                        ->scalarNode('create_merchant_url')->defaultNull()->end()
                        ->scalarNode('identity_api_url')->defaultNull()->end()
                        ->scalarNode('client_id')->defaultNull()->end()
                        ->scalarNode('client_secret')->defaultNull()->end()
                        ->scalarNode('domain_id')->defaultNull()->end()
                        ->scalarNode('audience')->defaultNull()->end()
                    ->end()
                ->end()
            ->end();
    }
}
