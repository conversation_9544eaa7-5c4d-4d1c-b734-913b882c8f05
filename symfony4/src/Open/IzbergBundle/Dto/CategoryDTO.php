<?php
/**
 * Created by PhpStorm.
 * User: PPH04712
 * Date: 23/04/2018
 * Time: 14:59
 */

namespace Open\IzbergBundle\Dto;


class CategoryDTO
{
    /**
     * @var int $id
     */
    private $id;

    /**
     * @var null|string $external_id
     */
    private $external_id;

    /**
     * url to the image
     * @var string $image
     */
    private $image;

    /**
     * @var array $children
     */
    private $children;

    /**
     * @var int $order
     */
    private $order;

    /**
     * @var string $name
     */
    private $name;

    /**
     * @return int
     */
    public function getId(): int
    {
        return $this->id;
    }

    /**
     * @param int $id
     */
    public function setId(int $id): void
    {
        $this->id = $id;
    }

    /**
     * @return string|null
     */
    public function getExternalId(): ?string
    {
      return $this->external_id;
    }

    /**
     * @param string|null $external_id
     */
    public function setExternalId(?string $external_id): void
    {
      $this->external_id = $external_id;
    }

    /**
     * @return string
     */
    public function getImage(): ?string
    {
        return $this->image;
    }

    /**
     * @param string $image
     */
    public function setImage(?string $image): void
    {
        $this->image = $image;
    }

    /**
     * @return array
     */
    public function getChildren(): ?array
    {
        return $this->children;
    }

    /**
     * @param array $children
     */
    public function setChildren(?array $children): void
    {
        $this->children = $children;
    }

    public function addChildren($children){
        if ($this->children === null){
            $this->children = [];
        }
        $this->children []= $children;
    }

    /**
     * @return int
     */
    public function getOrder(): int
    {
        return $this->order;
    }

    /**
     * @param int $order
     */
    public function setOrder(int $order): void
    {
        $this->order = $order;
    }

    /**
     * @return string
     */
    public function getName(): ?string
    {
        return $this->name;
    }

    /**
     * @param string $name
     */
    public function setName(?string $name): void
    {
        $this->name = $name;
    }





}
