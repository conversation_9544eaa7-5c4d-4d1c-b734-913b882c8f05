<?php


namespace Open\IzbergBundle\Dto;


class RedisAttributeDTO
{
    /**
     * @var string
     */
    private $key;
    /**
     * @var int|null
     */
    private $merchantOrderId;

    /**
     * RedisAttributeDTO constructor.
     * @param string $key
     * @param int|null $merchantOrderId
     */
    public function __construct(string $key, ?int $merchantOrderId = null)
    {
        $this->key = $key;
        $this->merchantOrderId = $merchantOrderId;
    }

    /**
     * @return string
     */
    public function getKey(): string
    {
        return $this->key;
    }

    /**
     * @return int|null
     */
    public function getMerchantOrderId(): ?int
    {
        return $this->merchantOrderId;
    }

}
