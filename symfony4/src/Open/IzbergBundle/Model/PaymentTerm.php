<?php

namespace Open\IzbergBundle\Model;

class PaymentTerm
{
    private $localizedName;
    private $days;
    private $termType;
    private $id;

    /**
     * @return mixed
     */
    public function getLocalizedName()
    {
        return $this->localizedName;
    }

    /**
     * @param mixed $localizedName
     */
    public function setLocalizedName($localizedName): void
    {
        $this->localizedName = $localizedName;
    }

    /**
     * @return mixed
     */
    public function getDays()
    {
        return $this->days;
    }

    /**
     * @param mixed $days
     */
    public function setDays($days): void
    {
        $this->days = $days;
    }

    /**
     * @return mixed
     */
    public function getId()
    {
        return $this->id;
    }

    /**
     * @param mixed $id
     */
    public function setId($id): void
    {
        $this->id = $id;
    }

    /**
     * @return mixed
     */
    public function getTermType()
    {
        return $this->termType;
    }

    /**
     * @param mixed $termType
     */
    public function setTermType($termType): void
    {
        $this->termType = $termType;
    }





}
