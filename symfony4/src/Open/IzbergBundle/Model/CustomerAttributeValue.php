<?php

namespace Open\IzbergBundle\Model;

class CustomerAttributeValue
{
    public function __construct(
        private int $id,
        private string $value
    ) {
    }

    /**
     * @return int
     */
    public function getId(): int
    {
        return $this->id;
    }

    /**
     * @return string
     */
    public function getValue(): string
    {
        return $this->value;
    }
}
