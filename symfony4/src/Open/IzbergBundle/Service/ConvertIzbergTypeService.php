<?php

declare(strict_types=1);

namespace Open\IzbergBundle\Service;

use Open\IzbergBundle\Attribute\IzbergTypeProxy;
use Open\IzbergBundle\Dto\AttributeTypeDto;

final class ConvertIzbergTypeService
{
    public static function convertValue(AttributeTypeDto $attribute): mixed
    {
        $value = $attribute->value;
        $type = IzbergTypeProxy::toPhp($attribute->valueType);
        try {
            settype($value, $type);
        } catch (\ValueError) {
            throw new \Exception(sprintf(
                'The %s service can\'t convert the value to %s type.', self::class, $attribute->valueType)
            );
        }
        return $value;
    }
}
