<?php

declare(strict_types=1);

namespace Open\IzbergBundle\Builder\MerchantUser;

final class MerchantUserBuilder
{
    /**
     * Build an izberg merchant user

     * @return array the merchant to create
     */
    public static function buildIzbergMerchantUser(
        string $email,
        string $password,
        string $first_name,
        string $last_name,
        int $merchantId
    ): array {
        return [
            'email' => $email,
            'first_name' => $first_name,
            'last_name' => $last_name,
            'merchant_scopes' => [
                $merchantId => [
                    '*' => "admin"
                ]
            ],
            'password' => $password
        ];
    }
}
