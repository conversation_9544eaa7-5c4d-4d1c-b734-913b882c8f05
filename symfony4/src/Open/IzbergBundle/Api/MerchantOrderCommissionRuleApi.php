<?php

namespace Open\IzbergBundle\Api;

use Open\IzbergBundle\Api;
use Open\IzbergBundle\Model\MerchantOrderCommissionRule;
use Open\LogBundle\Utils\EventNameEnum;
use Open\LogBundle\Utils\LogUtil;

final class MerchantOrderCommissionRuleApi extends Api
{
    public function getUri()
    {
        return 'order_item_commission_rule';
    }

    public function getItemClass()
    {
        return MerchantOrderCommissionRule::class;
    }

    /**
     * @param int $id
     * @param bool $isOperator
     * @return MerchantOrderCommissionRule|null
     */
    public function find(int $id, bool $isOperator = false): ?MerchantOrderCommissionRule
    {
        try {
            return parent::find($id, $isOperator);
        } catch (ApiException $apiException){
            $this->logger->error(
                sprintf('Cannot find fees operator with id: %s', $id),
                LogUtil::buildContext([
                    LogUtil::EVENT_NAME => EventNameEnum::SHIPPING_BUILD_ERROR
                ])
            );
        }

        return null;
    }
}
