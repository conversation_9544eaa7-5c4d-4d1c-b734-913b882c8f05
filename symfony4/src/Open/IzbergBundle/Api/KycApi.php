<?php

namespace Open\IzbergBundle\Api;

use Open\IzbergBundle\Api;

/**
 * Provide Wrapper for Kyc API
 * Class KycApi
 * @package Open\IzbergBundle\Api
 */
class KycApi extends Api
{
    private const KYC_SLUG = 'kyc/information/';
    private const SPECIFIC_PRICE_ID = 'private-price-file';

    public function  getUri(){
        return null;
    }

    public function getItemClass(){
        return null;
    }

    public function getAllKyc()
    {
        $ret = [];
        $requestUrl = $this->getApiUrl() . self::KYC_SLUG;

        do {
            $response = $this->sendApiRequest(
                self::HTTP_GET_OPERATION,
                $requestUrl
            );

            foreach ($response->body->objects as $kyc) {
                $ret[] = $kyc;
            }

            $requestUrl = $response->body->meta->next;

        } while ($requestUrl !== null);
        return $ret;
    }

    public function findAllSpecificPriceKyc(): \Generator
    {
        $specificPriceKycTypeId = $this->getKycTypeIdByExternalId(self::SPECIFIC_PRICE_ID);
        $requestUrl = $this->getApiUrl() . '/kyc/information/?kyc_type=' . $specificPriceKycTypeId;

        do {
            $response = $this->sendApiRequest(
                self::HTTP_GET_OPERATION,
                $requestUrl
            );

            foreach ($response->body->objects as $kyc) {
                yield $kyc;
            }

            $requestUrl = $response->body->meta->next;

        } while ($requestUrl !== null);
    }

    public function getKycById($id)
    {
        $response = $this->sendApiRequest(
            self::HTTP_GET_OPERATION,
            self::KYC_SLUG . $id . '/'
        );

        return $response->body;
    }

    public function getKycPartById($id)
    {
        $response = $this->sendApiRequest(
            self::HTTP_GET_OPERATION,
            self::KYC_SLUG . '/part/' . $id . '/',
            null,
            [],
            [
                self::OPTION_IS_OPERATOR => true,
            ]
        );

        return $response->body;
    }

    public function getKycByMerchantId($merchant_id)
    {
        $response = $this->sendApiRequest(
            self::HTTP_GET_OPERATION,
            self::KYC_SLUG . '?merchant=' . $merchant_id,
            null,
            [],
            [
                self::OPTION_IS_OPERATOR => true,
            ]
        );

        return $response->body;
    }

    /**
     * @param $url
     * @return mixed|null
     */
    public function getKycType($url)
    {
        $response = $this->sendApiRequest(
            self::HTTP_GET_OPERATION,
            $url,
            null,
            [],
            [
                self::OPTION_IS_OPERATOR => true,
            ]
        );

        return $response->body->external_id;
    }

    public function getKycTypeIdByExternalId(string $externalId): ?string
    {
        $requestUrl = $this->getApiUrl() . 'kyc/type/?external_id=' . $externalId;

        $response = $this->sendApiRequest(
            self::HTTP_GET_OPERATION,
            $requestUrl,
            null,
            [],
            [
                self::OPTION_IS_OPERATOR => true,
            ]
        );

        return ($kycType = array_shift($response->body->objects)) ? $kycType->id : null;
    }

    public function findMerchantGeneralSalesCondition(int $merchantId): ?array
    {
        $generalSalesConditionTypeId = $this->getKycTypeIdByExternalId('general-sales-condition');

        $params = [
            'merchant' => $merchantId,
            'kyc_type' => $generalSalesConditionTypeId,
        ];

        $requestUrl = $this->getApiUrl() . 'kyc/information/?' . http_build_query($params);

        $response = $this->sendApiRequest(
            self::HTTP_GET_OPERATION,
            $requestUrl,
            null,
            [],
            [
                self::OPTION_IS_OPERATOR => true,
            ]
        );

        return $response->body->objects;
    }

    private function extractFilenamefromURI($uri)
    {
        $t = explode("?", $uri);
        if (count($t) == 2) {
            $tt = explode('/', $t[0]);
            if (count($tt) >= 2) {
                return $tt[count($tt) - 1];
            }
        }

        return '';
    }

    public function getFile($url)
    {
        $response = $this->sendApiRequest(self::HTTP_GET_OPERATION, $url);

        $ret = new \stdClass();
        $ret->blob = explode("\n", file_get_contents($response->body->part));
        $ret->id = $response->body->id;
        $ret->filename = $this->extractFilenamefromURI($response->body->part);

        return $ret;
    }

    public function getMerchantId($url)
    {
        $response = $this->sendApiRequest(self::HTTP_GET_OPERATION, $url);

        return $response->body->id;
    }

    public function deleteKycFileById($id)
    {
        $response = $this->sendApiRequest(
            self::HTTP_DELETE_OPERATION,
            $this->getApiUrl() . '/kyc/information/part/' . $id . '/',
            null,
            [],
            [
                self::OPTION_NO_DATA_RETURN => true,
            ]
        );

        return ($response->code == 204);
    }
}
