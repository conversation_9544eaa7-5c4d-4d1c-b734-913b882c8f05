<?php

namespace Open\IzbergBundle\Api;

use AppBundle\Entity\Country;
use AppBundle\Entity\User;
use AppBundle\Services\CountryService;
use Open\IzbergBundle\Api;
use Open\IzbergBundle\Dto\AttributeTypeDto;
use Open\IzbergBundle\Model\Merchant;
use Open\IzbergBundle\Service\ConvertIzbergTypeService;
use Open\IzbergBundle\Service\ConvertTypeServiceInterface;
use stdClass;

/**
 * Provide Wrapper for Merchant API
 * Class MerchantApi
 * @package Open\IzbergBundle\Api
 */
class MerchantApi extends Api
{
    private const MERCHANT = 'merchant';
    private const MERCHANT_SLUG = 'merchant/';
    private const ERROR_UNKNOWN_COUNTRY = '"Unknwon Country: "';

    /**
     * @var CountryService
     */
    private $countryService;

    private ConvertTypeServiceInterface $convertTypeService;

    public function  getUri(): string
    {
        return self::MERCHANT;
    }

    public function getItemClass(): string
    {
        return Merchant::class;
    }

    public function setCountryService(CountryService $countryService): void
    {
        $this->countryService = $countryService;
    }

    public function setConvertTypeService(ConvertTypeServiceInterface $convertTypeService): void
    {
        $this->convertTypeService = $convertTypeService;
    }

    /**
     * Return Merchant entiry Schema
     * @link https://documenter.getpostman.com/view/653426/izberg-api-official-collection/Jsix#bc4faf-411f-0a23-e6f5-163dac395249
     * @return mixed
     */
    public function getSchema()
    {
        $response = $this->sendApiRequest(
            self::HTTP_GET_OPERATION,
            'merchant/schema/',
            null,
            [],
            [
                self::OPTION_IS_OPERATOR => true,
            ]
        );

        return $response->body;
    }

    public function activateMerchant($merchantId)
    {
        $this->sendApiRequest(
            self::HTTP_POST_OPERATION,
            self::MERCHANT_SLUG . $merchantId . '/activate/',
            '{}',
            [],
            [
                self::OPTION_IS_OPERATOR => true,
                self::OPTION_NO_DATA_RETURN => true,
            ]
        );
    }

    /**
     * create and assign an address to a merchant
     */
    public function addMerchantAddress(
        $merchantId,
        $billing_address,
        $return_address,
        $first_name,
        $last_name,
        $address,
        $city,
        $country_id,
        $contact_email,
        $phone,
        $social_reason
    )
    {
        // Get selected country to find the code
        /**
         * @var Country $country
         */
        $country = $this->countryService->getCountryById($country_id);
        if ($country === null){
            /**
             * @var User $user
             */
            $this->writeGenericErrorLog(self::ERROR_UNKNOWN_COUNTRY . $country_id, []);
            throw new ApiException(self::ERROR_UNKNOWN_COUNTRY . $country_id);
        }


        $data = [
            self::MERCHANT => "/v1/merchant/".$merchantId."/",
            "billing_address" => $billing_address,
            "return_address" => $return_address,
            "country" => "/v1/country/" . $country->getIzbergId() . "/",
            "contact_first_name" => $first_name,
            "contact_last_name" => $last_name,
            "address" => $address,
            "city" => $city,
            "contact_email" => $contact_email,
            "phone" => $phone,
            "contact_social_reason" => $social_reason,
        ];

        $this->sendApiRequest(
            self::HTTP_POST_OPERATION,
            'merchant_address/',  //don't forget the ending slash
            $data,
            [],
            [
                self::OPTION_IS_OPERATOR => true,
                self::OPTION_NO_DATA_RETURN => true,
            ]
        );
    }

    /**
     * add default transport assignment for merchant
     * @param int $merchantId the identifier of the merchant
     * @param int $carrier
     * @param int $shipping_provider
     * @param int $zone
     * @throws ApiException
     */
    public function addDefaultTransportAssignment(
        $merchantId,
        $carrier,
        $shipping_provider,
        $zone
    )
    {
        $data = [
            'rank' => "1",
            self::MERCHANT => "/v1/merchant/".$merchantId."/",
            'options' => array(
                "collection_within_hours" => 24,
                "delivery_within_hours" => 48,
                "infinite" => null,
                "limits" => []
            ),
            "status" => "enabled",
            "application_category" => null,
            "brand" => null,
            "carrier" => "/v1/carrier/" . $carrier . "/",
            "provider" => "/v1/shipping_provider/" . $shipping_provider . "/",
            "zone" => "/v1/zone/" . $zone . "/"
        ];

        $this->sendApiRequest(
            self::HTTP_POST_OPERATION,
            'shipping_provider_assignment/',  //don't forget the ending slash
            $data,
            [],
            [
                self::OPTION_IS_OPERATOR => true,
                self::OPTION_NO_DATA_RETURN => true,
            ]
        );
    }

    /**
     * Get list of merchants
     * @link https://documenter.getpostman.com/view/653426/izberg-api-official-collection/Jsix#fbc0e19-4ec1-7d8f-21de-47fca0288e4e
     * @return mixed
     */
    public function getMerchants()
    {
        $application_id = $this->getApplicationId();

        $response = $this->sendApiRequest(
            self::HTTP_GET_OPERATION,
            'application/' . $application_id . '/merchants/',
            null,
            [],
            [
                self::OPTION_IS_OPERATOR => true,
            ]
        );

        return $response->body;
    }

    /**
     * Get list of merchants
     * @link https://documenter.getpostman.com/view/653426/izberg-api-official-collection/Jsix#fbc0e19-4ec1-7d8f-21de-47fca0288e4e
     * @param array $options
     * @return mixed
     */
    public function getMerchantsWithParams(array $params = [])
    {
        $options['application'] = $this->getApplicationId();

        $response = $this->sendApiRequest(
            self::HTTP_GET_OPERATION,
            self::MERCHANT_SLUG . '?' . http_build_query($params),
            null,
            [],
            [
                self::OPTION_IS_OPERATOR => true,
            ]
        );

        return $response->body;
    }

    /**
     * set the merchant status to disable to pending
     * @param integer $id merchant id
     */
    public function checkActivation($id)
    {
        $this->sendApiRequest(
            self::HTTP_GET_OPERATION,
            'merchant/' . $id . '/check_activation/',
            null,
            [],
            [
                self::OPTION_IS_OPERATOR => true,
                self::OPTION_NO_DATA_RETURN => true,
            ]
        );
    }


    /**
     * Fetch a merchant by it's id
     * @link https://documenter.getpostman.com/view/653426/izberg-api-official-collection/Jsix#beb75d6-78f8-7bac-30d0-4da8555ab4ac
     * @param $id
     * @return null|\stdClass
     * @throws ApiException
     */
    public function getMerchant($id): ?\stdClass
    {
        $response = $this->sendApiRequest(
            self::HTTP_GET_OPERATION,
            self::MERCHANT_SLUG . $id . '/',
            null,
            [],
            [
                self::OPTION_IS_OPERATOR => true,
            ]
        );

        return $response->body;
    }

    /**
     * Fetch a merchant store bank account by it's id
     * @throws ApiException
     */
    public function getMerchantStoreBankAccount(int $merchantId): ?\stdClass
    {
        $response = $this->sendApiRequest(
            self::HTTP_GET_OPERATION,
            sprintf('store_bank_account/?merchant=%d', $merchantId),
            null,
            [],
            [
                self::OPTION_IS_OPERATOR => true,
            ]
        );

        return $response->body;
    }

    public function getAddress($addressId)
    {
        $response = $this->sendApiRequest(
            self::HTTP_GET_OPERATION,
            'merchant_address/' . $addressId  . '/',
            null,
            [],
            [
                self::OPTION_IS_OPERATOR => true,
            ]
        );

        return $response->body;
    }

    public function getMerchantByIdent($ident)
    {
        $response = $this->sendApiRequest(
            self::HTTP_GET_OPERATION,
            self::MERCHANT_SLUG . '/?identification=' . $ident,
            null,
            [],
            [
                self::OPTION_IS_OPERATOR => true,
            ]
        );
        return $response->body;
    }

    /**
     * fetch custom attributes of a merchant
     * @param $id
     * @param $key
     * @return mixed
     */
    public function getMerchantAllCustomAttributes($id)
    {
        $response = $this->sendApiRequest(
            self::HTTP_GET_OPERATION,
            self::MERCHANT_SLUG . $id . '/attributes/?limit=100',
            null,
            [],
            [
                self::OPTION_IS_OPERATOR => true,
            ]
        );

        $attrs = new stdClass();

        foreach ($response->body->objects as $attr) {
            $attrs->{$attr->key} = $attr->value;
        }

        return $attrs;
    }

    /**
     * fetch custom attributes of a merchant
     * @param $id
     * @param $key
     * @return mixed
     */
    public function getMerchantCompany($id)
    {
        $response = $this->sendApiRequest(
            self::HTTP_GET_OPERATION,
            self::MERCHANT_SLUG . $id . '/company/',
            null,
            [],
            [
                self::OPTION_IS_OPERATOR => true,
            ]
        );

        return $response->body;
    }

    /**
     * fetch custom attributes of a merchant
     * @param $id
     * @param $key
     * @return mixed
     */
    public function getMerchantCustomAttribute($id, $key)
    {
        $response = $this->sendApiRequest(
            self::HTTP_GET_OPERATION,
            self::MERCHANT_SLUG . $id . '/attributes/?key='.$key,
            null,
            [],
            [
                self::OPTION_IS_OPERATOR => true,
            ]
        );

        $attributes = $response->body->objects;
        foreach($attributes as $attribute){
            if ($attribute->key === $key){
                return ConvertIzbergTypeService::convertValue(AttributeTypeDto::createWithIzbergData($attribute));
            }
        }
        //if attribute not found, we return null
        return null;
    }

    /**
     * just create a merchant on izberg (without user)
     * @return null|\stdClass
     */
    public function createMerchant($name, $countryCode, $offerType): ?\stdClass
    {
        $data = [
            "name" => $name,
            "region" => $countryCode,
            "offer_type" => $offerType,
        ];

        $response = $this->sendApiRequest(
            method: self::HTTP_POST_OPERATION,
            route: self::MERCHANT_SLUG,
            data: $data,
            options: [
                self::OPTION_IS_OPERATOR => true,
                self::OPTION_USE_M2M_AUTH => true,
            ]
        );

        return $response->body;
    }

    public function updateMerchantCompany(int $companyId, int $countryId, string $vatNumber): void
    {
        $data = [
            'country' => sprintf('/v1/country/%d/', $countryId),
            'vat_number' => $vatNumber,
        ];

        $this->sendApiRequest(
            self::HTTP_PATCH_OPERATION,
            sprintf('company/%d/', $companyId),
            $data,
            [],
            [
                self::OPTION_IS_OPERATOR => true,
            ]
        );
    }

    /**
     * @param \stdClass $merchant
     * @param array $supportedLanguages
     * @return string detected merchant language or "en" if not found
     */
    public static function getMerchantLanguage ($merchant, $supportedLanguages){
        if (property_exists($merchant, "prefered_language") && !empty($merchant->prefered_language) && in_array($merchant->prefered_language, $supportedLanguages)){
            return $merchant->prefered_language;
        }
        //return en as default
        return "en";
    }

    public function updateMerchantCurrency($merchantId, $currency){
        $data = array(
            'currencies' => ["/v1/currency/".$currency."/"],
            'default_currency' => $currency,

        );

        $this->updateMerchant($merchantId, $data);
    }

    public function updateMerchant($merchantId, $data)
    {
        $this->sendApiRequest(
            self::HTTP_PATCH_OPERATION,
            self::MERCHANT_SLUG . $merchantId . '/',
            $data,
            [],
            [
                self::OPTION_IS_OPERATOR => true,
                self::OPTION_NO_DATA_RETURN => true,
            ]
        );
    }

    public function updateMerchantTaxRate(int $merchantId, int $taxRate)
    {
        $commissionSettingsId = null;
        $response = $this->sendApiRequest(
            self::HTTP_GET_OPERATION,
            self::MERCHANT_SLUG . $merchantId . '/commission_settings/',
            null,
            [],
            [
                self::OPTION_IS_OPERATOR => true,
            ]
        );

        $commissionSettingsId = $response->body->id ?? null;

        if (!$commissionSettingsId) {
            throw new ApiException(
                sprintf(
                    'Cannot found commission settings id for merchant %s',
                    $merchantId
                )
            );
        }

        $this->sendApiRequest(
            self::HTTP_PUT_OPERATION,
            'commission_settings/' . $commissionSettingsId . '/',
            [
                'applicable_tax_rate' => $taxRate,
            ],
            [],
            [
                self::OPTION_IS_OPERATOR => true,
                self::OPTION_NO_DATA_RETURN => true,
            ]
        );
    }
}
