<?php
namespace Open\IzbergBundle\Api;

use Exception;
use Symfony\Component\HttpKernel\Exception\HttpException;

class ApiException extends HttpException
{
    /**
     * @var array $externalContext additional key/value information for this error
     */
    private $externalContext = [];

    /**
     * @var string $apiCall
     */
    private $apiCall;

    /**
     * @var string $applicationId
     */
    private $applicationId;

    /**
     * the izberg error code
     * @var integer $izbergCode
     */
    private $izbergCode = null;

    /**
     * the error message from izberg
     * @var string $izbergMessage the izberg message
     */
    private $izbergMessage;

    //define your how exception so the message is not optional
    public function __construct($message, $code = 500, Exception $previous = null)
    {
        parent::__construct($code, $message, $previous);
    }

    public function __toString()
    {
        return __CLASS__ . ": [{$this->code}]: {$this->message}\n";
    }

    /**
     * @return int|null
     */
    public function getIzbergCode(): ?int
    {
        return $this->izbergCode;
    }

    /**
     * @param int|null $izbergCode
     */
    public function setIzbergCode(?int $izbergCode)
    {
        $this->izbergCode = $izbergCode;
    }

    /**
     * @return string the izberg error message
     */
    public function getIzbergMessage()
    {
        return $this->izbergMessage;
    }

    /**
     * @param string $izbergMessage the izberg message
     */
    public function setIzbergMessage(?string $izbergMessage)
    {
        $this->izbergMessage = $izbergMessage;
    }

    public function getApplicationId(): string
    {
        return $this->applicationId;
    }

    public function setApplicationId(string $applicationId): void
    {
        $this->applicationId = $applicationId;
    }

    public function getContext(){
        $context = [];
        $context['applicationId'] = $this->getApplicationId();
        $context['httpStatusCode'] = $this->getStatusCode();
        $context['izbergMessage'] = $this->getIzbergMessage();
        $context['izbergCode'] = $this->getIzbergCode();
        $context['apiCall'] = $this->getApiCall();
        return array_merge($context, $this->externalContext);
    }

    /**
     * @return array
     */
    public function getExternalContext(): array
    {
        return $this->externalContext;
    }

    /**
     * @param array $externalContext
     */
    public function setExternalContext(array $externalContext): void
    {
        $this->externalContext = $externalContext;
    }


    /**
     * @return string
     */
    public function getApiCall(): string
    {
        return $this->apiCall;
    }

    /**
     * @param string $apiCall
     */
    public function setApiCall(string $apiCall): void
    {
        $this->apiCall = $apiCall;
    }
}
