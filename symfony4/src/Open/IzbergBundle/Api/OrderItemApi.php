<?php

namespace Open\IzbergBundle\Api;

use Open\IzbergBundle\Api;
use Open\IzbergBundle\Model\OrderItem;

final class OrderItemApi extends Api
{
    public function getUri()
    {
        return 'order_item';
    }

    public function getItemClass()
    {
        return OrderItem::class;
    }

    public function patch(int $orderItemId, $fields)
    {
        $this->sendApiRequest(
            self::HTTP_PATCH_OPERATION,
            $this->getUri() . '/' . $orderItemId . '/',
            json_encode($fields),
            [],
            [
                self::OPTION_NO_DATA_RETURN => true,
            ]
        );
    }

    public function process(int $orderItemId)
    {
        $this->sendApiRequest(
            self::HTTP_POST_OPERATION,
            $this->getUri() . '/' . $orderItemId . '/process/',
            null,
            [],
            [
                self::OPTION_NO_DATA_RETURN => true,
            ]
        );
    }

}
