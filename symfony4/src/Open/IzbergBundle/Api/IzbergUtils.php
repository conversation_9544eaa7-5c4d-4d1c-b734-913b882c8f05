<?php

namespace Open\IzbergBundle\Api;

class IzbergUtils
{
    public static function buildQueryUriFromFilters (array $filters)
    {
        if (count($filters)) {
            return '?' . implode('&', array_map(
                    function ($filter, $value) {
                        return $filter . '=' . $value;
                    },
                    array_keys($filters),
                    array_values($filters)
                ));
        }

        return "";
    }

    /**
     * parse an izberg resource and return its id
     * @param string $resource
     * @return bool|string|null
     */
    public static function parseIzbergResourceAndGetId (string $resource = null)
    {
        if (!$resource) {
            return null;
        }

        $result = substr($resource, 0, -1);
        $substring = substr($result, strrpos($result, '/') + 1);
        return substr($result, strrpos($result, '/') + 1);
    }

    /**
     * validate and parse a date from izberg
     */
    public static function parseIzbergDate(?string $dateString, String $datePattern): ?\DateTime
    {
        if (!$dateString) {
            return null;
        }

        return \DateTime::createFromFormat($datePattern, $dateString);
    }
}
