<?php

namespace Open\IzbergBundle\Api;

final class ApiConfiguratorFactory
{
    public static function createApiConfigurator(array $config): ApiConfigurator
    {
        $defaultConfiguration = self::buildConfiguration('default', $config['default_connection']);
        $apiConfigurator = new ApiConfigurator($defaultConfiguration);

        foreach ($config['other_connections'] as $configurationName => $settings) {
            $apiConfigurator->addConfiguration(self::buildOtherConfiguration($defaultConfiguration, $configurationName, $settings));
        }

        return $apiConfigurator;
    }

    private static function buildConfiguration(string $configName, $config): ApiConfiguration
    {
        return (new ApiConfiguration())
            ->setDomain($config['domain'])
            ->setDomainSeller($config['domain_seller'])
            ->setVersion($config['version'])
            ->setProtocol($config['protocol'])
            ->setAccessToken($config['access_token'])
            ->setApplicationId($config['application_id'])
            ->setApplicationNamespace($config['application_namespace'])
            ->setSecretKey($config['secret_key'])
            ->setEmail($config['email'])
            ->setUsername($config['username'])
            ->setFirstName($config['first_name'])
            ->setLastName($config['last_name'])
            ->setJwtSecret($config['jwt_secret'])
            ->setSellerEmailDomain($config['seller_email_domain'])
            ->setCreateMerchantUrl($config['create_merchant_url'] ?? '')
            ->setIdentityApiUrl($config['identity_api_url'] ?? '')
            ->setClientId($config['client_id'] ?? '')
            ->setClientSecret($config['client_secret'] ?? '')
            ->setDomainId($config['domain_id'] ?? '')
            ->setAudience($config['audience'] ?? '')
            ->setConfigurationName($configName);
    }

    private static function buildOtherConfiguration(ApiConfiguration $defaultConfiguration, string $configurationName, array $settings): ApiConfiguration
    {
        return self::buildConfiguration($configurationName, $settings + $defaultConfiguration->toConfigArray());
    }
}
