<?php
namespace Open\IzbergBundle\Api;

use Open\IzbergBundle\Api;
use Open\IzbergBundle\Model\AuthResponse;
use Open\LogBundle\Utils\EventNameEnum;
use Open\LogBundle\Utils\LogUtil;
use stdClass;
use Unirest;

/**
 * Provide Wrapper around Auth API
 * Class AuthenticationApi
 * @package Open\IzbergBundle\Api
 */
class AuthenticationApi extends Api
{
    public function  getUri(){
        return null;
    }

    public function getItemClass(){
        return null;
    }

    /**
     * Authenticate a user
     * @link https://documenter.getpostman.com/view/653426/izberg-api-official-collection/Jsix#b24d-3c71-bb38-ce98-4754bbe49efa
     * @param string|null $email
     * @param string|null $username
     * @param string|null $firstName
     * @param string|null $lastName
     * @param string|null $fromSession
     * @return mixed
     */
	public function authenticateUser(?string $email, ?string $username, ?string $firstName, ?string $lastName, ?string $fromSession = null)
	{
		// Epoch time
		$timestamp = time();
		$authRaw = implode(';', [ $email, $firstName, $lastName, $timestamp]);

		// Encrypt the signature with our secret key
		$auth = hash_hmac('sha1', $authRaw, $this->getSecretKey());

		// Build query string
		$query = array(
			'message_auth' => $auth,
			'application' => $this->getApplicationNamespace(),
			'timestamp' => $timestamp,
			'is_staff' => false,
			'username' => $username,
			'first_name' => $firstName,
			'last_name' => $lastName,
			'email' => $email,
		);

		if ($fromSession !== null){
            $query['from_session_id'] = $fromSession;
        }

        $anonymousSession = null;
		$authHeaders = [];
		if ($email === null) {
		    $anonymousSession = uniqid();
		    $authHeaders = [
                Api::HEADER_CONTENT_TYPE => Api::CONTENT_JSON,
                Api::HEADER_AUTHORIZATION => $this->getApplicationNamespace(). ":" . $anonymousSession,
            ];
        }

		// Do API request
        $response = $this->sendApiRequest(
            self::HTTP_GET_OPERATION,
            'user/sso/',
            $query,
            $authHeaders,
            [
                'dataArray' => true
            ]
        );

		/** @var AuthResponse $authResponse */
		$authResponse = $this->serializer->deserialize($response->raw_body, AuthResponse::class, 'json');
        $authResponse->setSession($anonymousSession);

		// return response without handling errors
		return $authResponse;
	}

    /**
     * authenticate a anonymous user
     * @throws ApiException
     */
	public function authenticateAnonymousUser (){
	    return $this->authenticateUser(null, null, null, null);
    }

    /**
     * Get current User
     * @link https://documenter.getpostman.com/view/653426/izberg-api-official-collection/Jsix#a543cd-164b-a5c5-afcf-f2c7f0fda322
     * @return mixed
     */
	public function getCurrentUser()
	{
		// Do Request
        $response = $this->sendApiRequest(
            self::HTTP_GET_OPERATION,
            'user/me/'
        );

		// If response code is not ok throw an error
		if ($response->code < 200 || $response->code >= 300) {
			$this->throwGenericError($response->code, 'user/me/', $response->raw_body);
		}

		// Return response
		return $response->body;
	}

    /**
     * Get current authenticated Application
     * @link https://documenter.getpostman.com/view/653426/izberg-api-official-collection/Jsix#edeaf9e-0185-3e4b-78e4-76d22960f068
     * @return mixed
     */
	public function getCurrentApplication()
	{
	    $response = $this->sendApiRequest(
	        self::HTTP_GET_OPERATION,
            'application/mine/'
        );

		// If response code is not ok throw an error
		if ($response->code < 200 || $response->code >= 300) {
			$this->throwGenericError($response->code, 'application/mine/', $response->raw_body);
		}

		// Return response
		return $response->body;
	}

	/**
	 * @param $email
	 * @param $password
     * @return stdClass the response from izberg
	 */
	public function AuthenticateOperator($email, $password)
	{
		$header = [
			"alg" => "HS256",
			"typ" => "JWT",
        ];

		$data = [
			"email" => $email,
			"password" => $password,
			"application_id" => $this->getApplicationId(),
			"environment" => "sandbox",
		];

		$json_header = json_encode($header);
		$b64_header = $this->base64url_encode($json_header);

		$json_data = json_encode($data, JSON_UNESCAPED_UNICODE);
		$b64_data = $this->base64url_encode($json_data);

		$token = $b64_header . "." . $b64_data;

		$msg = hash_hmac('sha256', $token, utf8_encode($this->getJWTSecret()), true);

		$signature = $this->base64url_encode($msg);

		$signed_token = $token . "." . $signature;

		$signed_data = array(
			'jwt_token' => $signed_token
		);

		$body = Unirest\Request\Body::form($signed_data);

		// Talk to the API
        $requestUrl = 'https://operator-prod.izberg-marketplace.com:443/jwt_signin/';

        $this->logger->info(
            sprintf('Request Izberg API %s - Method: %s', $requestUrl, self::HTTP_POST_OPERATION),
            LogUtil::buildContext([
                LogUtil::EVENT_NAME => EventNameEnum::IZBERG_API
            ])
        );

		$response = Unirest\Request::post(
			$requestUrl,
			[
				Api::HEADER_CONTENT_TYPE => 'application/x-www-form-urlencoded'
            ],
			$body
		);

        $this->logger->info(
            sprintf('Response Izberg API %s - Method: %s', $this->getApiUrl() . $requestUrl, self::HTTP_POST_OPERATION),
            LogUtil::buildContext([
                LogUtil::EVENT_NAME => EventNameEnum::IZBERG_API
            ])
        );

		$r = new stdClass();

		if ($response->code == 200 && isset($response->body->application)) {

			$r->success = true;
			$r->user = $response->body->user;

		} else {

			$r->success = false;
			$r->message = $response->body->__all__[0];// Get error message
		}

		return $r;
	}

	/**
	 * Custom base64 encoding for URL
	 * @param $source
	 * @return mixed|string
	 */
	private function base64url_encode($source)
	{
		// Encode in classical base64
		$encodedSource = base64_encode($source);

		// Remove padding equal characters
		$encodedSource = preg_replace('/=+$/', '', $encodedSource);

		// Replace characters according to base64url specifications
		$encodedSource = str_replace('+', '-', $encodedSource);

		return str_replace('/', '_', $encodedSource);
	}
}
