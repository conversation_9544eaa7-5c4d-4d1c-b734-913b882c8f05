<?php

namespace Open\IzbergBundle\Api;

use Open\IzbergBundle\Api;
use Open\IzbergBundle\Model\CartNotification;

class CartNotificationApi extends Api
{
    public function getUri()
    {
        return 'cart_notification';
    }

    public function getItemClass()
    {
        return CartNotification::class;
    }

    public function getUnreadNotifications(int $izbergCartId): array
    {
        $notifications = [];

        $params = [
            'cart' => $izbergCartId,
            'status' => 'initial',
        ];

        $requestUrl = $this->getUri() . '/?' . http_build_query($params);

        $response = $this->sendApiRequest(
            self::HTTP_GET_OPERATION,
            $requestUrl
        );

        foreach ($response->body->objects as $object) {
            $notifications[] = $this->serializer->deserialize(json_encode($object), $this->getItemClass(), 'json');
        }

        return $notifications;
    }

    public function readNotification(int $izbergCartNotificationId)
    {
        $requestUrl = $this->getUri() . '/' . $izbergCartNotificationId . '/read/';

        $this->sendApiRequest(
            self::HTTP_POST_OPERATION,
            $requestUrl,
            null,
            [],
            [
                self::OPTION_NO_DATA_RETURN => true,
            ]
        );
    }
}
