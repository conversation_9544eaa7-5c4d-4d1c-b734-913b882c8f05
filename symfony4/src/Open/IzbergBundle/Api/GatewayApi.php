<?php

namespace Open\IzbergBundle\Api;

use Open\IzbergBundle\Api;
use Unirest\Request;

class GatewayApi extends Api
{
    private const GATEWAY_PATH = 'psp/gateway/';

    public const TYPE_TERM_PAYMENT = "TERM_PAYMENT";
    public const TYPE_PREPAYMENT = "PREPAYMENT";
    public const TYPE_REFUND = "REFUND";

    public const QUALIFIER_MERCHANT_ORDER = "MERCHANT_ORDER";
    public const QUALIFIER_INVOICE = "INVOICE";
    public const QUALIFIER_REFUND = "REFUND";

    public function  getUri(){
        return null;
    }

    public function getItemClass(){
        return null;
    }

    /**
     * create a gateways
     * @param string $externalId external id of the transaction
     * @param string $gatewayType must be PREPAYMENT or TERM_PAYMENT or REFUND
     * @param string $qualifier must be INVOICE or MERCHANT_ORDER
     * @param string $value the value to save in the gateway
     * @return null|string the identifier of the created gateway
     * @throws ApiException
     */
    public function createGateway (string $externalId,
        string $gatewayType,
        string $qualifier,
        string $value
    ): ?string{

        $data = [];
        if ($qualifier === self::QUALIFIER_INVOICE){
            $data["invoice"] = "/v1/customer_invoice/".$value."/";
        }else if ($qualifier === self::QUALIFIER_MERCHANT_ORDER){
            $data["merchant_order"] = "/v1/merchant_order/".$value."/";
        }
        else if ($qualifier === self::QUALIFIER_REFUND){
            $data["refund"] = "/v1/refund/".$value."/";
        }
        else{
            $this->throwGenericError(400, self::GATEWAY_PATH, "createGateway: Invalid value for qualifier: ".$qualifier, []);
        }

        $data["external_id"] = $externalId;
        $data["application"] = "/v1/application/".$this->getApplicationId()."/";
        $data["gateway_type"] = $gatewayType;


        $response = $this->sendApiRequest(
            self::HTTP_POST_OPERATION,
            self::GATEWAY_PATH,
            $data,
            [],
            [
                self::OPTION_IS_OPERATOR => true,
            ]
        );

        return $response->body->id;

    }

    /**
     * fetch a gateway from external_id
     * @param $external_id
     * @return mixed|null
     */
    public function getGateway($external_id)
    {
        $response = $this->sendApiRequest(
            self::HTTP_GET_OPERATION,
            self::GATEWAY_PATH . $external_id .'/',
            '{}',
            [],
            [
                self::OPTION_IS_OPERATOR => true,
            ]
        );
        return $response->body;
    }

    /**
     * @param $customerInvoiceId
     * @return \stdClass|null
     */
    public function getGatewayByCustomerInvoiceId($customerInvoiceId)
    {
        //must return a unique element
        $result = [];
        foreach ($this->getGatewayByFilters(["invoice" => $customerInvoiceId]) as $gateway){
            $result []= $gateway;
        }

        if (count($result) === 1){
            return $result[0];
        }
        return null;
    }

    /**
     * @param $merchantOrderId
     * @return \stdClass|null
     */
    public function getGatewayByMerchantOrderId($merchantOrderId)
    {
        //must return a unique element
        $result = [];
        foreach ($this->getGatewayByFilters(["merchant_order" => $merchantOrderId]) as $gateway){
            $result []= $gateway;
        }

        if (count($result) === 1){
            return $result[0];
        }
        return null;
    }

    /**
     * get gateways by specifying filters. Return generator of stdclass
     * @param array $filters
     * @return \Generator
     */
    public function getGatewayByFilters(array $filters){
        //build query uri
        $queryUri = IzbergUtils::buildQueryUriFromFilters($filters);

        $requestUrl = $this->getApiUrl() . self::GATEWAY_PATH . $queryUri;
        do {

            $response = $this->sendApiRequest(
                self::HTTP_GET_OPERATION,
                $requestUrl,
                '{}',
                [],
                [
                    self::OPTION_IS_OPERATOR => true,
                ]
            );

            foreach ($response->body->objects as $gateway){
                yield $gateway;
            }

            $requestUrl = $response->body->meta->next;

        } while($requestUrl !== null);
    }

    /**
     * Pay an amount on a psp gateway
     * @param $external_id
     * @param float|null $amount
     * @return mixed
     */
    public function payGateway($external_id, ?float $amount = null)
    {
        $data = array();
        $body = "{}";
        if ($amount != null){
            $data["amount"] = (string)$amount;
            $body = $this->formatObjectToJson($data);
        }

        $response = $this->sendApiRequest(
            self::HTTP_POST_OPERATION,
            self::GATEWAY_PATH . $external_id .'/pay/',
            $body,
            [],
            [
                self::OPTION_IS_OPERATOR => true,
            ]
        );

        return $response->body;
    }

    public function changeGatewayExtraData($external_id, $data)
    {
        $response = $this->sendApiRequest(
            self::HTTP_PATCH_OPERATION,
            self::GATEWAY_PATH . $external_id .'/',
            $data,
            [],
            [
                self::OPTION_IS_OPERATOR => true
            ]
        );

        return $response->body;
    }
}
