<?php

namespace Open\IzbergBundle\Api;

use Open\IzbergBundle\Api;
use Open\IzbergBundle\Model\CreditNote;
use Open\IzbergBundle\Model\FetchCreditNotesResponse;
use Unirest;

class CreditNoteApi extends Api
{
    private const CUSTOMER_CREDIT_NOTE_SLUG = 'credit_note/';
    private const STATUS_EMITTED = "emitted";

    private const PARAM_STATUS = "?status=";
    private const PARAM_EXTERNAL_STATUS = "&external_status=";

    public function getUri()
    {
        return 'credit_note';
    }

    public function getItemClass()
    {
        return CreditNote::class;
    }

    /**
     * fetch a customer credit note from its ids
     * @param string $creditNoteId
     * @return \stdClass|null the izberg customer credit note
     */
    public function fetchCustomerCreditNote($creditNoteId):?\stdClass
    {
        $response = $this->sendApiRequest(
            self::HTTP_GET_OPERATION,
            self::CUSTOMER_CREDIT_NOTE_SLUG . strval($creditNoteId)
        );

        return $response->body;
    }

    public function fetchCustomerCreditNotePdf($creditNoteId): ?string
    {
        $response = $this->sendApiRequest(
            self::HTTP_GET_OPERATION,
            self::CUSTOMER_CREDIT_NOTE_SLUG . strval($creditNoteId) . '/?only=pdf_file'
        );

        return (property_exists($response->body, 'pdf_file')) ? $response->body->pdf_file : null;
    }


    /**
     * find invoices by statutes and externalStatus (optional)
     * @param string $status
     * @param null|string $externalStatus
     * @return array list of invoices with the matching statutes
     */
    public function findCreditNoteByStatutes(string $status, string $externalStatus = null)
    {
        $ret = [];
        $query = self::PARAM_STATUS . $status;
        if ($externalStatus !== null) {
            $query .= self::PARAM_EXTERNAL_STATUS . $externalStatus;
        }

        $requestUrl = $this->getApiUrl() . self::CUSTOMER_CREDIT_NOTE_SLUG . $query;
        do {
            $response = $this->sendApiRequest(
                self::HTTP_GET_OPERATION,
                $requestUrl
            );

            $ret = array_merge($ret, $response->body->objects);

            $requestUrl = $response->body->meta->next;
        } while ($requestUrl !== null);

        return $ret;
    }

    /**
     * get emitted credit note
     * @return array
     */
    public function getEmittedCreditNote()
    {
        return $this->findCreditNoteByStatutes(self::STATUS_EMITTED, self::STATUS_EMITTED);
    }


    /**
     * path an credit note
     * @param string $id the identifier of the credit note
     * @param array $fields key/value array of fields that need to be updated
     */
    public function patchCreditNote($id, $fields)
    {
        $this->sendApiRequest(
            self::HTTP_PATCH_OPERATION,
            self::CUSTOMER_CREDIT_NOTE_SLUG . $id . '/',
            $fields,
            [],
            [
                self::OPTION_NO_DATA_RETURN => true,
            ]
        );
    }

    /**
     * update external status of a credit note to sent
     * @param $id
     */
    public function changeCustomerEmittedCreditNoteToSend($id)
    {
        $this->patchCreditNote(
            $id,
            [
                'external_status' => 'sent',
            ]
        );
    }

    public function getCreditNote($id)
    {
        $route = self::CUSTOMER_CREDIT_NOTE_SLUG . $id . '/';

        $response = $this->sendApiRequest(
            self::HTTP_GET_OPERATION,
            $route
        );

        $fetchResponse = $this->serializer->deserialize($response->raw_body, CreditNote::class, 'json');

        if (is_null($fetchResponse)) {
            throw new ApiException(" $route - unable to deserialize response from Izberg into a CreditNote object. Content was: " . $response->raw_body);
        }

        return $fetchResponse;
    }

    public function fetchAllCreditNotes(int $offset = 0, int $limit = 0): FetchCreditNotesResponse
    {
        $response = $this->sendApiRequest(
            self::HTTP_GET_OPERATION,
            self::CUSTOMER_CREDIT_NOTE_SLUG . '?offset=' . $offset . '&limit=' . $limit . '&full_order_item=true'
        );

        $fetchCreditNotesResponse = $this->serializer->deserialize($response->raw_body, FetchCreditNotesResponse::class, 'json');

        if (!$fetchCreditNotesResponse instanceof FetchCreditNotesResponse) {
            throw new ApiException('Failed fetching credit notes', 500);
        }

        return $fetchCreditNotesResponse;
    }
}
