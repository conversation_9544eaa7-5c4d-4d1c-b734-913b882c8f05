<?php

namespace Open\IzbergBundle\Api;

use Open\IzbergBundle\Api;
use Open\IzbergBundle\Model\Permission;
use Unirest\Request;

class PermissionApi extends Api
{
    public function getUri(): string
    {
        return 'permission';
    }

    public function getItemClass(): string
    {
        return Permission::class;
    }

    public function setPermission($merchantId, $userId): void
    {
        $data = [
            "merchant" => "/v1/merchant/".$merchantId."/",
            "user" => "/v1/user/".$userId."/",
            "full_access" => true
        ];

        $this->sendApiRequest(
            self::HTTP_POST_OPERATION,
            $this->getUri() . "/",
            $data,
            [],
            [
                self::OPTION_IS_OPERATOR => true,
                self::OPTION_NO_DATA_RETURN => true,
            ]
        );
    }
}
