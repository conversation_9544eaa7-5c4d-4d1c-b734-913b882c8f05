<?php

namespace Open\IzbergBundle\Api;

use Open\IzbergBundle\Api;
use Open\IzbergBundle\Model\InvoiceLine;

final class InvoiceLineApi extends Api
{
    public function getUri()
    {
        return 'invoice_line';
    }

    public function getItemClass()
    {
        return InvoiceLine::class;
    }

    public function createInvoiceLine(int $invoiceId, int $orderItemId)
    {
        $data = [
            "invoice" => $this->getApiUrl() . 'customer_invoice/' . $invoiceId . '/',
            "order_item" => '/v1/order_item/'.$orderItemId.'/',
        ];

        $this->sendApiRequest(
            self::HTTP_POST_OPERATION,
            $this->getUri() . '/',
            $data,
            [],
            [
                self::OPTION_NO_DATA_RETURN => true,
            ]
        );
    }
}
