<?php
namespace Open\IzbergBundle\Api;

use Open\IzbergBundle\Api;
use Open\IzbergBundle\Model\IzbergUser;
use Unirest;

/**
 * Wrapper for Izber User API
 * Class UserApi
 * @package Open\IzbergBundle\Api
 */
class UserApi extends Api
{
    public function  getUri(){
        return 'user';
    }

    public function getItemClass(){
        return IzbergUser::class;
    }

    /**
     * Get user by Id
     * @param $id
     * @return IzbergUser
     */
    public function getUser($id) :IzbergUser
    {
        $response = $this->sendApiRequest(
            self::HTTP_GET_OPERATION,
            'user/' . $id . '/'
        );

        $izbergUser = $this->serializer->deserialize($response->raw_body,IzbergUser::class, 'json');

        if (!$izbergUser instanceof IzbergUser) {
            throw new ApiException('Failed getting izberg user', 500);
        }

        return $izbergUser;
    }
}
