<?php

declare(strict_types=1);

namespace Open\IzbergBundle\Api;

use Open\IzbergBundle\Api;
use Open\IzbergBundle\Model\Merchant;

final class CreateMerchantUserApi extends Api
{
    public function createMerchantUser(array $merchantUserData): void
    {
        $this->sendApiRequest(
            method: self::HTTP_POST_OPERATION,
            route: $this->getCreateMerchantUserUrl(),
            data: $merchantUserData,
            options: [
                self::OPTION_IS_OPERATOR => true,
                self::OPTION_USE_M2M_AUTH => true,
            ]
        );
    }

    public function getUri(): string
    {
        return 'merchant';
    }

    public function getItemClass(): string
    {
        return Merchant::class;
    }
}
