<?php

namespace Open\IzbergBundle\Api;

use AppBundle\Entity\Company;
use Open\IzbergBundle\EventSubscriber\AuthenticationSubscriber;
use Open\IzbergBundle\Exception\InvalidApiConfigurationNameException;

final class ApiConfigurator
{
    public const CONNECTION_DEFAULT = 'default';
    public const CONNECTION_WEBHOOK = 'webhook';
    public const CONNECTION_CONSOLE = 'console';
    public const CONNECTION_OPERATOR = 'operator';
    public const CONNECTION_ASYNC_USER_ACTION = 'async-user-action';

    private const SUPPORTED_CONNECTION = [
        self::CONNECTION_DEFAULT,
        self::CONNECTION_CONSOLE,
        self::CONNECTION_WEBHOOK,
        self::CONNECTION_OPERATOR,
        self::CONNECTION_ASYNC_USER_ACTION,
    ];

    /**
     * @var ApiConfiguration[]
     */
    private array $configurations = [];

    public function __construct(ApiConfiguration $defaultConfiguration)
    {
        $this->addConfiguration(configuration: $defaultConfiguration);
    }

    public function addConfiguration(ApiConfiguration $configuration): void
    {
        $configurationName = $configuration->getConfigurationName();
        if (!in_array($configurationName, self::SUPPORTED_CONNECTION)) {
            throw new InvalidApiConfigurationNameException(sprintf(
                'The %s api configuration name is not supported in (%s)',
                $configurationName,
                join(' | ', self::SUPPORTED_CONNECTION)
            ));
        }
        $this->configurations[$configurationName] = $configuration;
    }

    public function configure(ApiClientManager $apiClientManager): void
    {
        $apiClientManager->setConfiguration($this->findConfiguration($apiClientManager->inUseConnection()));
    }

    public function findConfiguration(string $connection): ApiConfiguration
    {
        return $this->configurations[$connection] ?? $this->configurations[self::CONNECTION_DEFAULT];
    }

    public function generateAsyncUserConfiguration(Company $company, AuthenticationApi $authenticationAPI)
    {
        $configuration = $this->findConfiguration(self::CONNECTION_CONSOLE);
        $configuration->setUserUsername($company->getIzbergUsername());

        $response = $authenticationAPI->authenticateUser(
            $company->getIzbergEmail(),
            $company->getIzbergUsername(),
            AuthenticationSubscriber::escapeCompanyName($company->getName()),
            " "
        );

        $configuration->setUserAccessToken($response->getAccessToken());
        $configuration->setConfigurationName(self::CONNECTION_ASYNC_USER_ACTION);
        $this->addConfiguration($configuration);
    }

    public function getConfigurations(): array
    {
        return $this->configurations;
    }
}
