<?php

namespace Open\IzbergBundle\Api;

final class ApiConfiguration
{
    /**
     * @var string
     */
    private $configurationName;

    private $domain;
    private $domainSeller;
    private $application_id;
    private $application_namespace;
    private $version;
    private $protocol;
    private $secret_key;
    private $email;
    private $first_name;
    private $last_name;
    private $username;
    private $jwtSecret;
    private $sellerEmailDomain;
    private $accessToken;
    private $userUsername;
    private $userAccessToken;

    private ?string $createMerchantUrl = null;

    private ?string $identityApiUrl = null;

    private ?string $clientId = null;

    private ?string $clientSecret = null;

    private ?string $domainId = null;

    private ?string $audience = null;

    public function getConfigurationName(): string
    {
        return $this->configurationName;
    }

    public function setConfigurationName(string $configurationName): self
    {
        $this->configurationName = $configurationName;

        return $this;
    }

    public function getAccessToken()
    {
        return $this->accessToken;
    }

    public function setAccessToken($accessToken): self
    {
        $this->accessToken = $accessToken;

        return $this;
    }

    public function getDomain()
    {
        return $this->domain;
    }

    public function setDomain($domain): self
    {
        $this->domain = $domain;

        return $this;
    }

    public function getDomainSeller()
    {
        return $this->domainSeller;
    }

    public function setDomainSeller($domainSeller): self
    {
        $this->domainSeller = $domainSeller;

        return $this;
    }

    public function getApiUrl()
    {
        return $this->protocol . '://' . $this->domain . '/v' . $this->version . '/';
    }

    public function getSellerApiUrl()
    {
        return $this->protocol . '://' . $this->domainSeller . '/';
    }

    public function getApplicationId()
    {
        return $this->application_id;
    }

    public function setApplicationId($application_id): self
    {
        $this->application_id = $application_id;

        return $this;
    }

    public function getApplicationNamespace()
    {
        return $this->application_namespace;
    }

    public function setApplicationNamespace($application_namespace): self
    {
        $this->application_namespace = $application_namespace;

        return $this;
    }

    public function getVersion()
    {
        return $this->version;
    }

    public function setVersion($version): self
    {
        $this->version = $version;

        return $this;
    }

    public function getProtocol()
    {
        return $this->protocol;
    }

    public function setProtocol($protocol): self
    {
        $this->protocol = $protocol;

        return $this;
    }

    public function getSecretKey()
    {
        return $this->secret_key;
    }

    public function setSecretKey($secret_key): self
    {
        $this->secret_key = $secret_key;

        return $this;
    }

    public function getEmail()
    {
        return $this->email;
    }

    public function setEmail($email): self
    {
        $this->email = $email;

        return $this;
    }

    public function getFirstName()
    {
        return $this->first_name;
    }

    public function setFirstName($first_name): self
    {
        $this->first_name = $first_name;

        return $this;
    }

    public function getLastName()
    {
        return $this->last_name;
    }

    public function setLastName($last_name): self
    {
        $this->last_name = $last_name;

        return $this;
    }

    public function getUsername()
    {
        return $this->username;
    }

    public function setUsername($username): self
    {
        $this->username = $username;

        return $this;
    }

    public function getJwtSecret()
    {
        return $this->jwtSecret;
    }

    public function setJwtSecret($jwtSecret): self
    {
        $this->jwtSecret = $jwtSecret;

        return $this;
    }

    public function getSellerEmailDomain()
    {
        return $this->sellerEmailDomain;
    }

    public function setSellerEmailDomain($sellerEmailDomain): self
    {
        $this->sellerEmailDomain = $sellerEmailDomain;

        return $this;
    }

    public function getUserUsername()
    {
        return $this->userUsername;
    }

    public function setUserUsername($userUsername): self
    {
        $this->userUsername = $userUsername;

        return $this;
    }

    public function getUserAccessToken()
    {
        return $this->userAccessToken;
    }

    public function setUserAccessToken($userAccessToken): self
    {
        $this->userAccessToken = $userAccessToken;

        return $this;
    }

    public function toConfigArray(): array
    {
        return [
            'domain' => $this->getDomain(),
            'domain_seller' => $this->getDomainSeller(),
            'version' => $this->getVersion(),
            'protocol' => $this->getProtocol(),
            'access_token' => $this->getAccessToken(),
            'application_id' => $this->getApplicationId(),
            'application_namespace' => $this->getApplicationNamespace(),
            'secret_key' => $this->getSecretKey(),
            'email' => $this->getEmail(),
            'username' => $this->getUsername(),
            'first_name' => $this->getFirstName(),
            'last_name' => $this->getLastName(),
            'jwt_secret' => $this->getJwtSecret(),
            'seller_email_domain' => $this->getSellerEmailDomain(),
            'user_username' => $this->getUserUsername(),
            'user_access_token' => $this->getUserAccessToken(),
            'createMerchantUrl' => $this->getCreateMerchantUrl(),
            'clientId' => $this->getClientId(),
            'domainId' => $this->getDomainId(),
            'audience' => $this->getAudience(),
        ];
    }

    public function getCreateMerchantUrl(): ?string
    {
        return $this->createMerchantUrl;
    }

    public function setCreateMerchantUrl(string $createMerchantUrl): self
    {
        $this->createMerchantUrl = $createMerchantUrl;

        return $this;
    }

    public function getClientId(): ?string
    {
        return $this->clientId;
    }

    public function setClientId(string $clientId): self
    {
        $this->clientId = $clientId;

        return $this;
    }

    public function getClientSecret(): ?string
    {
        return $this->clientSecret;
    }

    public function setClientSecret(string $clientSecret): self
    {
        $this->clientSecret = $clientSecret;

        return $this;
    }

    public function getDomainId(): ?string
    {
        return $this->domainId;
    }

    public function setDomainId(string $domainId): self
    {
        $this->domainId = $domainId;

        return $this;
    }

    public function getAudience(): ?string
    {
        return $this->audience;
    }

    /**
     * @param string $audience
     */
    public function setAudience(string $audience): self
    {
        $this->audience = $audience;

        return $this;
    }

    public function getIdentityApiUrl(): ?string
    {
        return $this->identityApiUrl;
    }

    public function setIdentityApiUrl(?string $identityApiUrl): self
    {
        $this->identityApiUrl = $identityApiUrl;

        return $this;
    }
}
