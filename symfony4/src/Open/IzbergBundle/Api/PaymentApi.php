<?php

namespace Open\IzbergBundle\Api;

use Open\IzbergBundle\Api;
use Open\IzbergBundle\Model\Payment;
use Open\IzbergBundle\Model\PaymentTerm;
use Unirest;

class PaymentApi extends Api
{
    private const PAYMENT_PATH = 'payment/';
    private const PAYMENT_TERM_PATH = 'payment_term/';
    private const PAYMENT_LINES_PATH = '/lines/';

    public function  getUri(){
        return 'payment';
    }

    public function getItemClass(){
        return Payment::class;
    }

    public function authorizePayment(int $paymentId): ?Unirest\Response
    {
        $data = "{}";
        $timestamp = time();

        //get default headers for authenticated user
        $headers = [];

        //add signature to the request
        $toEncode = $data . ':' . $timestamp;
        $message_auth = hash_hmac('sha1', $toEncode, $this->getSecretKey());
        $headers['Application-signature'] = $message_auth;
        $headers['Application-nonce'] = $timestamp;

        $response = $this->sendApiRequest(
            self::HTTP_POST_OPERATION,
            self::PAYMENT_PATH . $paymentId . '/authorize/',
            $data,
            $headers
        );

        return $response;
    }

    public function getPayment(int $paymentId, bool $operator = false): ?Payment
    {
        $response = $this->sendApiRequest(
            self::HTTP_GET_OPERATION,
            self::PAYMENT_PATH . $paymentId . '/',
            null,
            [],
            [self::OPTION_IS_OPERATOR => $operator]
        );

        $payment = $this->serializer->deserialize($response->raw_body, Payment::class, 'json');

        return ($payment instanceof Payment) ? $payment : null;
    }

    /**
     * find payments
     * @param array|null $searchTerms a key/value array: key is the name of the parameter, value is the... value
     * @return array the list of payments
     * @throws ApiException if an error occurred while fetching the specified payment
     */
    public function findPayments(?array $searchTerms){
        $result = array();
        $query = '';
        if ($searchTerms !== null){
            foreach ($searchTerms as $param => $paramValue){
                $query .= $param . "=" . $paramValue . "&";
            }
        }
        $requestUrl = $this->getApiUrl() . self::PAYMENT_PATH . '?' . $query;

        do {
            $response = $this->sendApiRequest(
                self::HTTP_GET_OPERATION,
                $requestUrl
            );

            $result = array_merge($result, $response->body->objects);
            $requestUrl = $response->body->meta->next;

        } while($requestUrl !== null);

        return $result;
    }

    /**
     * update a payment object
     * @param $paymentId
     * @param array data data to be updated
     */
    public function updatePayment ($paymentId, $data)
    {
        $this->sendApiRequest(
            self::HTTP_PATCH_OPERATION,
            self::PAYMENT_PATH . $paymentId . '/',
            json_encode($data),
            [],
            [
                self::OPTION_IS_OPERATOR => true,
                self::OPTION_NO_DATA_RETURN => true,
            ]
        );
    }

    /**
     * set a payment as pending
     * @param $paymentId
     */
    public function setPaymentAsPending($paymentId)
    {
        $this->sendApiRequest(
            self::HTTP_POST_OPERATION,
            self::PAYMENT_PATH . $paymentId . '/pending_authorization/',
            json_encode([]),
            [],
            [
                self::OPTION_IS_OPERATOR => true,
                self::OPTION_NO_DATA_RETURN => true,
            ]
        );
    }

    /**
     * get list of supported payment terms
     * @param string $locale the locale to use to get data
     * @return PaymentTerm the payment term configured on izberg
     */
    public function getPaymentTerms($locale = "en")
    {
        $headers = [Api::HEADER_ACCEPT_LANGUAGE => $locale];

        $response = $this->sendApiRequest(
            self::HTTP_GET_OPERATION,
            self::PAYMENT_TERM_PATH,
            null,
            $headers,
            [
                self::OPTION_IS_OPERATOR => true,
            ]
        );

        /**
         * @var \stdClass $body
         */
        $body = $response->body;

        $paymentTerm = new PaymentTerm();
        $paymentTerm->setId($body->objects[0]->id);
        $paymentTerm->setLocalizedName($body->objects[0]->name);

        //need to fetch payment lines
        $response = $this->sendApiRequest(
            self::HTTP_GET_OPERATION,
            self::PAYMENT_TERM_PATH . $paymentTerm->getId().self::PAYMENT_LINES_PATH,
            null,
            $headers
        );

        /**
         * @var \stdClass $body
         */
        $bodyLines = $response->body;
        $paymentTerm->setDays($bodyLines->objects[0]->number_of_days);
        $paymentTerm->setTermType($bodyLines->objects[0]->term_type);

        return $paymentTerm;
    }
}
