<?php

namespace Open\IzbergBundle\Api;

final class ApiClientManager
{
    private string $connection;

    private ApiConfiguration $configuration;

    public function __construct()
    {
        $this->connection = ApiConfigurator::CONNECTION_DEFAULT;
    }

    public function getConfiguration(): ApiConfiguration
    {
        return $this->configuration;
    }

    public function setConfiguration(ApiConfiguration $configuration): void
    {
        $this->configuration = $configuration;
    }

    public function inUseConnection(): string
    {
        return $this->connection;
    }

    public function useConnection(string $connection): void
    {
        $this->connection = $connection;
    }
}
