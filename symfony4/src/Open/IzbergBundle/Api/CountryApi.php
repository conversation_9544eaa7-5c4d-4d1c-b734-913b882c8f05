<?php
namespace Open\IzbergBundle\Api;

use Doctrine\Common\Collections\ArrayCollection;
use Open\IzbergBundle\Api;
use Unirest;
use Open\IzbergBundle\Model\Country;

/**
 * Provide Wrapper for Country API
 * Class CountryApi
 * @package Open\IzbergBundle\Api
 */
class CountryApi extends Api
{
    public function  getUri(){
        return 'country';
    }

    public function getItemClass(){
        return Country::class;
    }

    /**
     * Return a collection of Open\IzbergBundle\Model\Country
     * @return ArrayCollection
     */
    public function getAllCountries()
    {
        $response = $this->sendApiRequest(
            self::HTTP_GET_OPERATION,
            'country/all/'
        );

        $countries = new ArrayCollection();

        foreach ($response->body as $country_json) {
            $country = $this->serializer->deserialize(json_encode($country_json), Country::class, 'json');
            /**
             * @psalm-suppress InvalidArgument
             */
            $countries->add($country);
        }

        return $countries;
    }

    /**
     * Fetch a unique country by its Id
     * @param $country_id integer
     * @return Country
     */
    public function getCountry($country_id)
    {
        $response = $this->sendApiRequest(
            self::HTTP_GET_OPERATION,
            'country/' . intval($country_id) . '/'
        );

        $country = $this->serializer->deserialize($response->raw_body, Country::class, 'json');

        if (!$country instanceof Country) {
            throw new ApiException('Failed getting country', 500);
        }

        return $country;
    }
}
