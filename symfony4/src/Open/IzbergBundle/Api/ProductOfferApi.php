<?php

namespace Open\IzbergBundle\Api;

use Open\IzbergBundle\Api;
use Open\IzbergBundle\ApiRequestParameter;
use Open\IzbergBundle\Model\FetchProductOfferAssignedImagesResponse;
use Open\IzbergBundle\Model\ProductOffer;
use Open\IzbergBundle\Model\Product;

/**
 * Provide Wrapper for the Product and Product Offer API
 * Class ProductOfferApi
 * @package Open\IzbergBundle\Api
 */
class ProductOfferApi extends Api
{
    private const PRODUCT_OFFER_PATH = 'productoffer';

    public function  getUri(){
        return 'productoffer';
    }

    public function getItemClass(){
        return ProductOffer::class;
    }

    /**
     * @param int $productId
     * @param int $merchantId
     * @param string $name
     * @param float $price
     * @return ProductOffer
     */
    public function createProductOffer(int $productId, int $merchantId, string $name, float $price, string $externalId): ProductOffer
    {
        $data = [
            'product' => '/v1/product/'. $productId . '/',
            'merchant' => '/v1/merchant/' . $merchantId . '/',
            'name' => $name,
            'price' => $price,
            'external_id' => $externalId,
        ];

        $defaultData = [
            'sku' => $externalId,
            'stock' => 1000,
            'attributes' => [
                $this->customAttributes->getSkuUnit() => 'Item(s)',
                $this->customAttributes->getCountryOfDelivery() => 'France',
                $this->customAttributes->getStockAvailability() => 'On Stock',
                $this->customAttributes->getStockManagement() => 'No',
                $this->customAttributes->getBatchSize() => 1,
                $this->customAttributes->getManufacturerReference() => 'manufacturer reference',
                $this->customAttributes->getTotalDelayForCustomer() => 1,
                $this->customAttributes->getVendorReference() => 'vendor reference',
                $this->customAttributes->getIncoTerm() => 'FCA',
                $this->customAttributes->getDeliveryTime() => 1,
                $this->customAttributes->getMadeIn() => 'FRA',
                $this->customAttributes->getQuantityPerSku() => 1,
                $this->customAttributes->getCustomTariffCode() => 'custom tariff code',
                $this->customAttributes->getMoq() => 1,
                $this->customAttributes->getDelayBeforeShipping() => 1,
            ]
        ];

        $data = $data + $defaultData;

        $response = $this->sendApiRequest(
            self::HTTP_POST_OPERATION,
            self::PRODUCT_OFFER_PATH . '/',
            $data,
            [
                Api::HEADER_ACCEPT_LANGUAGE => 'en',
            ],
            [
                Api::OPTION_IS_OPERATOR => true,
            ]
        );

        $productOffer = $this->serializer->deserialize($response->raw_body, ProductOffer::class, 'json');

        if (!$productOffer instanceof ProductOffer) {
            throw new ApiException('Failed creating izberg product offer', 500);
        }

        return $productOffer;
    }

    /**
     * Get a product offer by its Id
     * @param $id
     * @return ProductOffer
     */
    public function getProductOffer($id)
    {
        $response = $this->sendApiRequest(
            self::HTTP_GET_OPERATION,
            self::PRODUCT_OFFER_PATH . '/' . intval($id) . '/'
        );

        $productOffer = $this->serializer->deserialize($response->raw_body, ProductOffer::class, 'json');

        if (!$productOffer instanceof ProductOffer) {
            throw new ApiException('Failed getting product offer', 500);
        }

        return $productOffer;
    }


    /**
     * Get product by Id
     * @param $id
     * @return Product
     */
    public function getProduct($id): Product
    {
        $response = $this->sendApiRequest(
            self::HTTP_GET_OPERATION,
            'product/' . intval($id) . '/'
        );

        $product = $this->serializer->deserialize($response->raw_body, Product::class, 'json');

        if (!$product instanceof Product) {
            throw new ApiException('Failed getting product', 500);
        }

        return $product;
    }

    /**
     * Get all product offers for the given category
     * @param $category_id
     * @return mixed the ProductOffer object
     */
    public function getProducOffersByCategory($category_id)
    {
        $response = $this->sendApiRequest(
            self::HTTP_GET_OPERATION,
            'product/?where=application_categories=' . intval($category_id)
        );

        return $this->serializer->deserialize($response->raw_body, ProductOffer::class, 'json');
    }

    /**
     * @param int $productOfferId
     * @return array
     */
    public function getProductOfferImages(int $productOfferId): array
    {
        $response = $this->sendApiRequest(
            self::HTTP_GET_OPERATION,
            self::PRODUCT_OFFER_PATH . '/' . intval($productOfferId) . '/?only=assigned_images',
        );

        $productOfferAssignedImagesResponse = $this->serializer->deserialize($response->raw_body, FetchProductOfferAssignedImagesResponse::class, 'json');

        if (!$productOfferAssignedImagesResponse instanceof FetchProductOfferAssignedImagesResponse) {
            throw new ApiException('Failed getting product offer assigned images', 500);
        }

        if ($productOfferAssignedImagesResponse->getAssignedImages() === null) {
            return [];
        }

        return $productOfferAssignedImagesResponse->getAssignedImages()->toArray();
    }

    public function unassignImagesFromProductOffer(int $productOfferId, array $imageIds)
    {
        $this->sendConcurrentApiRequest(
            array_map(
                function($imageId) use ($productOfferId){
                    return new ApiRequestParameter(
                        self::HTTP_POST_OPERATION,
                        self::PRODUCT_OFFER_PATH . '/' . intval($productOfferId) . '/unassign_image/',
                        [
                            'image_id' =>  $imageId,
                        ]
                    );
                },
                $imageIds
            ),
            true,
            5,
            [
                self::OPTION_NO_DATA_RETURN => true,
            ]
        );
    }

    /**
     * @param $id
     * @param $data
     */
    public function patchProductOffer($id, $data)
    {
        $this->sendApiRequest(
            self::HTTP_PATCH_OPERATION,
            self::PRODUCT_OFFER_PATH . '/' . intval($id) . '/',
            $data,
            [],
            [
                self::OPTION_NO_DATA_RETURN => true,
            ]
        );
    }

    /**
     * Set product Departement
     * @param $id
     * @param $departement
     */
    public function setProductDepartement($id, $departement)
    {
        $this->patchProductOffer($id, array("attributes" => array("departement-depart" => $departement)));
    }


    /**
     * Deactivate a product OFFER via izberg API
     */
    public function deactivateProduct($id)
    {
        $this->sendApiRequest(
            self::HTTP_POST_OPERATION,
            self::PRODUCT_OFFER_PATH . '/' . intval($id) . '/deactivate/',
            null,
            [],
            [
                self::OPTION_NO_DATA_RETURN => true,
            ]
        );

        return true;
    }

    /**
     * @param int $id
     * @return bool
     */
    public function trashProduct(int $id): bool
    {
        $this->sendApiRequest(
            self::HTTP_POST_OPERATION,
            self::PRODUCT_OFFER_PATH . '/' . intval($id) . '/trash/',
            null,
            [],
            [
                self::OPTION_NO_DATA_RETURN => true,
            ]
        );

        return true;
    }

    public function removeOfferProduct(int $id)
    {
        $this->sendApiRequest(
            self::HTTP_DELETE_OPERATION,
            self::PRODUCT_OFFER_PATH . '/' . intval($id) . '/',
            null,
            [],
            [
                self::OPTION_NO_DATA_RETURN => true,
            ]
        );

        return true;
    }

    /**
     * activate a product OFFER via izberg API
     * @param $id
     * @return bool
     */
    public function activateProduct($id)
    {
        $this->sendApiRequest(
            self::HTTP_POST_OPERATION,
            self::PRODUCT_OFFER_PATH . '/' . intval($id) . '/activate/',
            null,
            [],
            [
                self::OPTION_NO_DATA_RETURN => true,
            ]
        );

        return true;
    }
}

