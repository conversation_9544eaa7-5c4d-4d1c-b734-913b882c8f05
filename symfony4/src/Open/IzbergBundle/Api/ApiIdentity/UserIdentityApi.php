<?php

declare(strict_types=1);

namespace Open\IzbergBundle\Api\ApiIdentity;

use Exception;
use Open\IzbergBundle\Api;
use Open\IzbergBundle\Api\IzbergUtils;
use Open\IzbergBundle\Model\UserIdentity;
use Open\LogBundle\Utils\LogUtil;

final class UserIdentityApi extends AbstractApiIdentity
{
    public function getUri(): string
    {
        return 'user';
    }

    public function getItemClass(): string
    {
        return UserIdentity::class;
    }

    public function fetchUserByEmail(string $email): iterable
    {
        $response = null;

        do {
            try {
                $filterString = IzbergUtils::buildQueryUriFromFilters([
                    'email' => urlencode($email),
                    'domain_id' => $this->getDomainId()
                ]);

                $response = $this->sendApiRequest(
                    method: self::HTTP_GET_OPERATION,
                    route: $this->getUri() . $filterString,
                    options: [
                        self::OPTION_IS_OPERATOR => true,
                        self::OPTION_USE_M2M_AUTH => true,
                    ]
                );

                foreach ($response->body->objects as $object) {
                    yield $this->serializer->deserialize(json_encode($object), $this->getItemClass(), 'json');
                }

                $requestUrl = $response->body->meta->next;
            } catch (Exception $exception) {
                $this->logger->error(
                    "an unexpected error occurred while invoking izberg API",
                    LogUtil::buildContext([
                        LogUtil::EVENT_NAME => Api::LOG_IZBERG_API_ERROR,
                        "message" => $exception->getMessage(),
                    ])
                );
                $this->throwGenericError(
                    ($response) ? $response->code : 0,
                    $this->getUri()
                );
            }
        } while ($requestUrl !== null);
    }

    public function attachWithMerchant(string $uuid, int $merchantId, array $merchantScopes = []): void
    {
        $merchantScopes[$merchantId] = ['*' => 'admin'];

        $this->sendApiRequest(
            method: self::HTTP_PATCH_OPERATION,
            route: sprintf('%s/%s', $this->getUri(), $uuid),
            data: ['merchant_scopes' => $merchantScopes],
            options: [
                self::OPTION_IS_OPERATOR => true,
                self::OPTION_USE_M2M_AUTH => true,
            ]
        );
    }
}
