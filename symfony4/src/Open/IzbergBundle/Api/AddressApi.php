<?php
namespace Open\IzbergBundle\Api;

use Doctrine\Common\Collections\ArrayCollection;
use Open\IzbergBundle\Api;
use Open\IzbergBundle\Model\Address;
use Unirest;

/**
 * Wrapper around Address API
 * Class AddressApi
 * @package Open\IzbergBundle\Api
 */
class AddressApi extends Api
{
    const ADDRESS_SLUG = 'address/';

    public function  getUri(){
        return 'address';
    }

    public function getItemClass(){
        return Address::class;
    }

    /**
     * @param Address $address
     * @param bool $operator whether this is a operator (true) call or a user call (false)
     * @return mixed
     */
    public function createAddress(Address $address)
    {
        $data = array(
            'name' => $address->getName(),
            'address' => $address->getAddress(),
            'address2' => $address->getAddress2(),
            'zipcode' => $address->getZipcode(),
            'city' => $address->getCity(),
            'country' => $address->getCountry()->getResourceUri(),
            'application' => $this->getApiUrl() . "application/" . $this->getApplicationId() . "/"
        );



        $response = $this->sendApiRequest(
            self::HTTP_POST_OPERATION,
            self::ADDRESS_SLUG,
            $data
        );

        // return address id if success
        return $response->body->id;
    }

    /**
     * Update an existing address
     * @param $id
     * @param Address $address
     * @param bool $operator whether this is a operator (true) call or a user call (false)
     * @return string|null
     */
    public function updateAddress($id, Address $address): ?string
    {
        // we need the country resource uri
        $country_resource_uri = $address->getCountry()->getResourceUri();

        // This is crappy
        $data = json_decode($this->serializer->serialize($address, 'json'));

        // Api is expecting are resource uri not a country
        // so we hack the country property (cannot do it on the Address entity because setCountry expect a Country
        // entity
        $data->country = $country_resource_uri;

        $response = $this->sendApiRequest(
            self::HTTP_PUT_OPERATION,
            self::ADDRESS_SLUG . intval($id) . '/',
            $data
        );

        // any 2xx code should be considered success
        return $response->body->id;
    }
}
