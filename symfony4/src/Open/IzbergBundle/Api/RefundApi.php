<?php

namespace Open\IzbergBundle\Api;

use Open\IzbergBundle\Api;
use Open\IzbergBundle\Model\Refund;

class RefundApi extends Api
{

    private const REFUND_SLUG = 'refund/';

    public function  getUri(){
        return 'refund';
    }

    public function getItemClass(){
        return Refund::class;
    }

    public function findRefundByOrderNumber($orderNumber){
        $response = $this->sendApiRequest(
            self::HTTP_GET_OPERATION,
            self::REFUND_SLUG . '?order_id_number=' . $orderNumber
        );

        return $this->serializer->deserialize($response->raw_body, Refund::class, 'json');
    }

    public function findRefundsByMerchantOrderId($merchantOrderId){
        $response = $this->sendApiRequest(
            self::HTTP_GET_OPERATION,
            self::REFUND_SLUG . '?merchant_order=' . $merchantOrderId . '&customer_invoice=none&status=complete'
        );

        return $this->serializer->deserialize($response->raw_body, Refund::class, 'json');
    }
}

