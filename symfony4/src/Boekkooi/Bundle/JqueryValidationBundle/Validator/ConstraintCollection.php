<?php
namespace <PERSON><PERSON><PERSON><PERSON>\Bundle\JqueryValidationBundle\Validator;

use Boekkooi\Bundle\JqueryValidationBundle\Exception\InvalidArgumentException;
use Doctrine\Common\Collections\ArrayCollection;
use Symfony\Component\Validator\Constraint;

/**
 * <AUTHOR> <<EMAIL>>
 * @deprecated since symfony 4
 */
class ConstraintCollection extends ArrayCollection
{
    /**
     * {@inheritDoc}
     *
     * @param Constraint $value A Constraint instance
     * @psalm-suppress MoreSpecificImplementedParamType
     */
    public function set($key, $value)
    {
        $this->assertConstraintInstance($value);

        parent::set($key, $value);
    }

    /**
     * {@inheritDoc}
     *
     * @param Constraint $value A Constraint instance
     * @psalm-suppress MoreSpecificImplementedParamType
     */
    public function add($element)
    {
        $this->assertConstraintInstance($element);

        return parent::add($element);
    }

    /**
     * Adds a constraint collection at the end of the current set by appending all
     * constraint of the added collection.
     *
     * @param ConstraintCollection $collection A ConstraintCollection instance
     */
    public function addCollection(ConstraintCollection $collection)
    {
        foreach ($collection as $constraint) {
            $this->add($constraint);
        }
    }

    /**
     * @param $value
     */
    private function assertConstraintInstance($value)
    {
        if (!$value instanceof Constraint) {
            throw new InvalidArgumentException(sprintf(
                'Expected a "%s" instance',
                Constraint::class
            ));
        }
    }
}
