{% macro field_dependency(config) %}
    {%- set condition = config[0] -%}
    {%- set rule = config[1] -%}
    {%- set isRequired = rule.name is same as('required') -%}

    {%- if isRequired -%}
        var dep = form.find("[name=\"{{ condition.field|e('js') }}\"]")[0];
    {%- endif -%}

    if (
    {%- if condition.condition is same as('!') -%}!{%- endif -%}
    (
        {%- if isRequired -%}
            !$.validator.methods.required.call(validator, validator.elementValue(dep), dep, true) || {% endif -%}
        "{{ condition.field|e('js') }}" in validator.errorMap || "{{ condition.field|e('js') }}" in validator.invalid)) {
        return false;
    }
{% endmacro %}

{% macro field_value_dependency(config) %}
    {%- set condition = config[0] -%}
    {%- set rule = config[1] -%}
    var dep = form.find("[name=\"{{ condition.field|e('js') }}\"]");
    if(dep.length > 1 || dep.is(':checkbox')) {
        dep = dep.filter(':checked')
    } else if(dep.is('select')) {
        dep = dep.find(':selected');
    }

    if(
    {%- if condition.condition is same as('!=') -%}!{%- endif -%}
        $.inArray(
            "{{ condition.value|e('js') }}",
            $.map(dep, function(elt) { return $(elt).val(); })
        )
    ) {
        return false;
    }
{% endmacro %}
