{% import "@BoekkooiJqueryValidation/Form/macros.js.twig" as gen %}

{%- autoescape false -%}
(function($) {
    "use strict";
    var form = $("{{- gen.form_jquery_selector(form) -}}");
    var validator = form.validate({
        rules: {
            {{- gen.form_rules(fields, enforce_validation_groups) -}}
        },
        messages: {
            {{- gen.form_messages(fields) -}}
        }
    });

    {%- if enforce_validation_groups -%}
        validator.settings.validation_groups = { {{- gen.validation_groups(validation_groups, enabled_validation_groups) -}} };
    {%- endif -%}

    {%- for button in buttons -%}
        {%- if button.cancel -%}
            form.find("*[name=\"{{ button.name|e('js') }}\"]").addClass("cancel");
        {%- elseif enforce_validation_groups -%}
            form.find("*[name=\"{{ button.name|e('js') }}\"]").click(function() {
                validator.settings.validation_groups = { {{- gen.validation_groups(validation_groups, button.validation_groups) -}} };
            });
        {%- endif -%}
    {%- endfor %}
})(jQuery);
{%- endautoescape -%}
