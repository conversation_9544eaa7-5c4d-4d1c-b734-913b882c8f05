<?php
namespace Boek<PERSON><PERSON>\Bundle\JqueryValidationBundle\Form\Util;

/**
 * Class FormViewRecursiveIterator
 * @package Boekkooi\Bundle\JqueryValidationBundle\Form\Util
 * @deprecated since symfony 4
 */
class FormViewRecursiveIterator extends \IteratorIterator implements \RecursiveIterator
{
    /**
     * {@inheritdoc}
     */
    public function getChildren()
    {
        return new static($this->current());
    }

    /**
     *{@inheritdoc}
     */
    public function hasChildren()
    {
        return $this->current()->count() > 0;
    }
}
