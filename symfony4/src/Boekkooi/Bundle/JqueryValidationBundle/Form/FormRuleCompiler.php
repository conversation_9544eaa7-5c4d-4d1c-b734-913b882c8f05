<?php
namespace Boek<PERSON><PERSON>\Bundle\JqueryValidationBundle\Form;

/**
 * <AUTHOR> <<EMAIL>>
 * @deprecated since symfony 4
 */
class FormRuleCompiler implements FormRuleCompilerInterface
{
    /**
     * @var FormRuleCompilerInterface[]
     */
    protected $compilers = array();

    public function __construct(array $compilers)
    {
        foreach ($compilers as $compiler) {
            $this->addCompiler($compiler);
        }
    }

    /**
     * @param FormRuleContextBuilder $formRuleFormRuleFormRuleContext
     */
    public function compile(FormRuleContextBuilder $formRuleContext)
    {
        foreach ($this->compilers as $compiler) {
            $compiler->compile($formRuleContext);
        }
    }

    private function addCompiler(FormRuleCompilerInterface $compiler)
    {
        $this->compilers[] = $compiler;
    }
}
