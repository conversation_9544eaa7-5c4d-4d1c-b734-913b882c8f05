<?php
namespace <PERSON><PERSON><PERSON><PERSON>\Bundle\JqueryValidationBundle\Form\Rule\Mapping;

use <PERSON>ek<PERSON><PERSON>\Bundle\JqueryValidationBundle\Exception\LogicException;
use Boekkooi\Bundle\JqueryValidationBundle\Form\Rule\ConstraintRule;
use <PERSON><PERSON><PERSON><PERSON>\Bundle\JqueryValidationBundle\Form\Rule\ConstraintMapperInterface;
use <PERSON><PERSON><PERSON><PERSON>\Bundle\JqueryValidationBundle\Form\RuleCollection;
use <PERSON><PERSON><PERSON><PERSON>\Bundle\JqueryValidationBundle\Form\RuleMessage;
use Symfony\Component\Form\FormInterface;
use Symfony\Component\Validator\Constraint;
use Symfony\Component\Validator\Constraints\NotBlank;
use Symfony\Component\Validator\Constraints\NotNull;

/**
 * <AUTHOR> Bo<PERSON> <<EMAIL>>
 * @deprecated since symfony 4
 */
class RequiredRule implements ConstraintMapperInterface
{
    const RULE_NAME = 'required';

    /**
     * {@inheritdoc}
     */
    public function resolve(Constraint $constraint, FormInterface $form, RuleCollection $collection)
    {
        if (!$this->supports($constraint, $form)) {
            throw new LogicException();
        }

        /** @var \Symfony\Component\Validator\Constraints\NotBlank | \Symfony\Component\Validator\Constraints\NotNull $constraint */
        $collection->set(
            self::RULE_NAME,
            new ConstraintRule(
                self::RULE_NAME,
                true,
                new RuleMessage($constraint->message),
                $constraint->groups
            )
        );
    }

    public function supports(Constraint $constraint, FormInterface $form)
    {
        return in_array(
            get_class($constraint),
            array(
                NotBlank::class,
                NotNull::class,
            ),
            true
        );
    }
}
