<?php
namespace <PERSON><PERSON><PERSON><PERSON>\Bundle\JqueryValidationBundle\Form\Rule\Mapping;

use <PERSON>ek<PERSON><PERSON>\Bundle\JqueryValidationBundle\Exception\LogicException;
use Boekkooi\Bundle\JqueryValidationBundle\Form\Rule\ConstraintRule;
use <PERSON><PERSON><PERSON><PERSON>\Bundle\JqueryValidationBundle\Form\Rule\ConstraintMapperInterface;
use <PERSON><PERSON><PERSON><PERSON>\Bundle\JqueryValidationBundle\Form\RuleCollection;
use <PERSON><PERSON><PERSON><PERSON>\Bundle\JqueryValidationBundle\Form\RuleMessage;
use Symfony\Component\Form\Extension\Core\Type\CheckboxType;
use Symfony\Component\Form\FormInterface;
use Symfony\Component\Validator\Constraint;
use Symfony\Component\Validator\Constraints\IsFalse;
use Symfony\Component\Validator\Constraints\IsTrue;

/**
 * A Rule mapper for IsTrue and IsFalse.
 *
 * <AUTHOR> <<EMAIL>>
 * @deprecated since symfony 4
 */
class IsBooleanRule implements ConstraintMapperInterface
{
    /**
     * @var bool
     */
    private $useAdditional;

    public function __construct($useAdditional)
    {
        $this->useAdditional = $useAdditional;
    }

    /**
     * {@inheritdoc}
     */
    public function resolve(Constraint $constraint, FormInterface $form, RuleCollection $collection)
    {
        if (!$this->supports($constraint, $form)) {
            throw new LogicException();
        }

        $boolType = $constraint instanceof IsTrue;

        // If the isTrue is applied and it is a checkbox then just make it required
        if ($boolType && $form->getConfig()->getType()->getInnerType() instanceof CheckboxType) {
            /**
             * @var IsTrue $constraint
             */
            $collection->set(
                RequiredRule::RULE_NAME,
                new ConstraintRule(
                    RequiredRule::RULE_NAME,
                    true,
                    new RuleMessage($constraint->message),
                    $constraint->groups
                )
            );

            return;
        }

        // A additional method is used to skip if not found
        if (!$this->useAdditional) {
            return;
        }

        /** @var IsTrue|IsFalse $constraint */
        $collection->set(
            'equals',
            new ConstraintRule(
                'equals',
                $boolType ? '1' : '0',
                new RuleMessage($constraint->message),
                $constraint->groups
            )
        );
    }

    public function supports(Constraint $constraint, FormInterface $form)
    {
        return in_array(
            get_class($constraint),
            array(
                IsTrue::class,
                IsFalse::class,
            ),
            true
        );
    }
}
