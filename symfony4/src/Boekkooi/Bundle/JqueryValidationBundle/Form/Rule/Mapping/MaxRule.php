<?php
namespace <PERSON><PERSON><PERSON><PERSON>\Bundle\JqueryValidationBundle\Form\Rule\Mapping;

use <PERSON>ekko<PERSON>\Bundle\JqueryValidationBundle\Exception\LogicException;
use Boekkooi\Bundle\JqueryValidationBundle\Form\Rule\ConstraintRule;
use <PERSON><PERSON><PERSON><PERSON>\Bundle\JqueryValidationBundle\Form\Rule\ConstraintMapperInterface;
use <PERSON><PERSON><PERSON><PERSON>\Bundle\JqueryValidationBundle\Form\RuleCollection;
use <PERSON><PERSON><PERSON><PERSON>\Bundle\JqueryValidationBundle\Form\RuleMessage;
use Symfony\Component\Form\FormInterface;
use Symfony\Component\Validator\Constraint;
use Symfony\Component\Validator\Constraints\LessThan;
use Symfony\Component\Validator\Constraints\LessThanOrEqual;
use Symfony\Component\Validator\Constraints\Range;

/**
 * <AUTHOR> Boekkooi <<EMAIL>>
 * @deprecated since symfony 4
 */
class MaxRule implements ConstraintMapperInterface
{
    const RULE_NAME = 'max';

    /**
     * {@inheritdoc}
     */
    public function resolve(Constraint $constraint, FormInterface $form, RuleCollection $collection)
    {
        /** @var \Symfony\Component\Validator\Constraints\AbstractComparison $constraint */
        $constraintClass = get_class($constraint);
        if ($constraintClass === LessThan::class) {
            $rule = new ConstraintRule(
                self::RULE_NAME,
                $constraint->value - 1, // TODO support floats
                new RuleMessage($constraint->message, array('{{ compared_value }}' => $constraint->value)),
                $constraint->groups
            );
        } elseif ($constraintClass === LessThanOrEqual::class) {
            $rule = new ConstraintRule(
                self::RULE_NAME,
                $constraint->value,
                new RuleMessage($constraint->message, array('{{ compared_value }}' => $constraint->value)),
                $constraint->groups
            );
        }

        elseif ($constraint instanceof Range && $constraint->max !== null) {
            /** @var Range $constraint */
            $rule = new ConstraintRule(
                self::RULE_NAME,
                $constraint->max,
                new RuleMessage($constraint->maxMessage, array('{{ limit }}' => $constraint->max)),
                $constraint->groups
            );
        } else {
            throw new LogicException();
        }

        $collection->set(
            self::RULE_NAME,
            $rule
        );
    }

    public function supports(Constraint $constraint, FormInterface $form)
    {
        $constraintClass = get_class($constraint);

        return
            in_array($constraintClass, array(
                LessThan::class,
                LessThanOrEqual::class,
            ), true) ||
            $constraint instanceof Range && $constraint->max !== null
        ;
    }
}
