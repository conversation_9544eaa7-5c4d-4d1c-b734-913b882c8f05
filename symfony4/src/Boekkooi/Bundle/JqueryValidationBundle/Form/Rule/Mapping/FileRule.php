<?php
namespace <PERSON><PERSON><PERSON><PERSON>\Bundle\JqueryValidationBundle\Form\Rule\Mapping;

use Symfony\Component\Validator\Constraint;
use Symfony\Component\Form\FormInterface;
use <PERSON>ek<PERSON><PERSON>\Bundle\JqueryValidationBundle\Form\RuleCollection;
use <PERSON>ek<PERSON><PERSON>\Bundle\JqueryValidationBundle\Exception\LogicException;
use <PERSON><PERSON><PERSON><PERSON>\Bundle\JqueryValidationBundle\Form\Rule\ConstraintRule;
use <PERSON><PERSON><PERSON>oi\Bundle\JqueryValidationBundle\Form\RuleMessage;
use Boekko<PERSON>\Bundle\JqueryValidationBundle\Form\Rule\ConstraintMapperInterface;
use Symfony\Component\Validator\Constraints\File;

/**
 * <AUTHOR> Boekkooi <<EMAIL>>
 * @deprecated since symfony 4
 */
class FileRule implements ConstraintMapperInterface
{
    const RULE_NAME = 'accept';

    /**
     * @var bool
     */
    private $active;

    public function __construct($active)
    {
        $this->active = $active;
    }

    /**
     * {@inheritdoc}
     */
    public function resolve(Constraint $constraint, FormInterface $form, RuleCollection $collection)
    {
        if (!$this->supports($constraint, $form)) {
            throw new LogicException();
        }

        /** @var File $constraint */
        $collection->set(
            self::RULE_NAME,
            new ConstraintRule(
                self::RULE_NAME,
                implode(',', $constraint->mimeTypes),
                new RuleMessage($constraint->mimeTypesMessage, array(
                    '{{ types }}' => implode(', ', $constraint->mimeTypes),
                )),
                $constraint->groups
            )
        );
    }

    public function supports(Constraint $constraint, FormInterface $form)
    {
        return $this->active &&
            get_class($constraint) === File::class &&
            !empty($constraint->mimeTypes);
    }
}
