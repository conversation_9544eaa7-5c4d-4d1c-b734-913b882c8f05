<?php
namespace <PERSON><PERSON><PERSON><PERSON>\Bundle\JqueryValidationBundle\Form\Rule\Processor;

use <PERSON>ekko<PERSON>\Bundle\JqueryValidationBundle\Form\FormRuleContextBuilder;
use Boekko<PERSON>\Bundle\JqueryValidationBundle\Form\FormRuleProcessorInterface;
use <PERSON>ekkooi\Bundle\JqueryValidationBundle\Form\Rule\ConstraintMapperInterface;
use <PERSON><PERSON><PERSON><PERSON>\Bundle\JqueryValidationBundle\Form\RuleCollection;
use Boekko<PERSON>\Bundle\JqueryValidationBundle\Form\FormRuleProcessorContext;

/**
 * <AUTHOR> Boek<PERSON>oi <<EMAIL>>
 * @deprecated since symfony 4
 */
class ConstraintMappingPass implements FormRuleProcessorInterface
{
    /**
     * @var ConstraintMapperInterface[]
     */
    protected $mappers = array();

    public function process(FormRuleProcessorContext $processContext, FormRuleContextBuilder $formRuleContext)
    {
        $constraints = $processContext->getConstraints();
        $form = $processContext->getForm();

        $collection = new RuleCollection();
        foreach ($this->mappers as $mapper) {
            foreach ($constraints as $constraint) {
                if (!$mapper->supports($constraint, $form)) {
                    continue;
                }

                $mapper->resolve($constraint, $form, $collection);
            }
        }

        $formRuleContext->add(
            $processContext->getView(),
            $collection
        );
    }

    public function addMapper(ConstraintMapperInterface $mapper)
    {
        $this->mappers[] = $mapper;
    }
}
