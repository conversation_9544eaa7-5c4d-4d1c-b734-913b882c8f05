<?php
namespace <PERSON><PERSON><PERSON><PERSON>\Bundle\JqueryValidationBundle\Form\Rule;

use Boekkooi\Bundle\JqueryValidationBundle\Form\Rule;
use Boekko<PERSON>\Bundle\JqueryValidationBundle\Form\RuleMessage;
use Symfony\Component\Validator\Constraint;

/**
 * <AUTHOR> <<EMAIL>>
 * @deprecated since symfony 4
 */
class ConstraintRule extends Rule
{
    /**
     * A list of validation groups
     * @var array
     */
    public $groups;

    public function __construct($name, $options = null, RuleMessage $message = null, array $groups = array(Constraint::DEFAULT_GROUP), array $conditions = array())
    {
        parent::__construct($name, $options, $message, $conditions);

        $this->groups = $groups;
    }
}
