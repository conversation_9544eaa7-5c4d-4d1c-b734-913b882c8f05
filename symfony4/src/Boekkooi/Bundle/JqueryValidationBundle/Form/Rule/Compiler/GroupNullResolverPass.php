<?php
namespace <PERSON><PERSON><PERSON><PERSON>\Bundle\JqueryValidationBundle\Form\Rule\Compiler;

use <PERSON><PERSON><PERSON><PERSON>\Bundle\JqueryValidationBundle\Form\FormRuleCompilerInterface;
use <PERSON>ekko<PERSON>\Bundle\JqueryValidationBundle\Form\FormRuleContextBuilder;
use Symfony\Component\PropertyAccess\PropertyPath;

/**
 * <AUTHOR> <<EMAIL>>
 * @deprecated since symfony 4
 */
class GroupNullResolverPass implements FormRuleCompilerInterface
{
    /**
     * @param FormRuleContextBuilder $formRuleContext
     */
    public function compile(FormRuleContextBuilder $formRuleContext)
    {
        $formGroups = $formRuleContext->getGroups();
        foreach ($formGroups as $name => $groups) {
            if ($groups !== null) {
                continue;
            }

            // Find the closest validation group
            $path = new PropertyPath($name);
            do {
                $parentName = (string) $path;
                if (isset($formGroups[$parentName]) && ($groups = $formGroups[$parentName]) !== null) {
                    $formRuleContext->addGroup($name, $groups);
                    break;
                }
                $path = $path->getParent();
            } while ($path !== null);
        }
    }
}
