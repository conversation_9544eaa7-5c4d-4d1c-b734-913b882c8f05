<?php
namespace <PERSON><PERSON><PERSON><PERSON>\Bundle\JqueryValidationBundle\Form\Rule\Compiler;

use <PERSON><PERSON><PERSON><PERSON>\Bundle\JqueryValidationBundle\Form\FormRuleCompilerInterface;
use <PERSON>ek<PERSON><PERSON>\Bundle\JqueryValidationBundle\Form\FormRuleContextBuilder;
use <PERSON>ek<PERSON>oi\Bundle\JqueryValidationBundle\Form\Rule\ConstraintRule;
use <PERSON><PERSON><PERSON><PERSON>\Bundle\JqueryValidationBundle\Form\RuleCollection;

/**
 * <AUTHOR> Boekkooi <<EMAIL>>
 * @deprecated since symfony 4
 */
class RejectUnusedRulesPass implements FormRuleCompilerInterface
{
    /**
     * @param FormRuleContextBuilder $formRuleContext
     */
    public function compile(FormRuleContextBuilder $formRuleContext)
    {
        $it = new \RecursiveIteratorIterator(
            new \RecursiveArrayIterator($formRuleContext->getGroups())
        );
        $groups = array_unique(array_filter(iterator_to_array($it, false)));

        /** @var RuleCollection $ruleCollection */
        foreach ($formRuleContext->all() as $ruleCollection) {
            foreach ($ruleCollection as $name => $rule) {
                if (!$rule instanceof ConstraintRule) {
                    continue;
                }

                $rule->groups = array_intersect($rule->groups, $groups);

                if (empty($rule->groups)) {
                    $ruleCollection->remove($name);
                }
            }
        }
    }
}
