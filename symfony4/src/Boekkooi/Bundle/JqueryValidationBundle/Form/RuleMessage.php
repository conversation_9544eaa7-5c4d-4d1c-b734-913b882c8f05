<?php
namespace <PERSON><PERSON><PERSON><PERSON>\Bundle\JqueryValidationBundle\Form;

/**
 * <AUTHOR> <<EMAIL>>
 * @deprecated since symfony 4
 */
class RuleMessage
{
    /**
     * A message text.
     * @var string
     */
    public $message;

    /**
     * A list of message parameters.
     * @var array
     */
    public $parameters;

    /**
     * @var null
     */
    public $plural;

    public function __construct($message, array $parameters = array(), $plural = null)
    {
        $this->message = $message;
        $this->parameters = $parameters;
        $this->plural = $plural;
    }
}
