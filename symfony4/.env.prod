# In all environments, the following files are loaded if they exist,
# the latter taking precedence over the former:
#
#  * .env                contains default values for the environment variables needed by the app
#  * .env.local          uncommitted file with local overrides
#  * .env.$APP_ENV       committed environment-specific defaults
#  * .env.$APP_ENV.local uncommitted environment-specific overrides
#
# Real environment variables win over .env files.
#
# DO NOT DEFINE PRODUCTION SECRETS IN THIS FILE NOR IN ANY OTHER COMMITTED FILES.
#
# Run "composer dump-env prod" to compile .env files for production use (requires symfony/flex >=1.2).
# https://symfony.com/doc/current/best_practices.html#use-environment-variables-for-infrastructure-configuration

###> symfony/framework-bundle ###
APP_ENV=prod
APP_SECRET=4e64208a9738364c34479bddff978282
#TRUSTED_PROXIES=*********/8,10.0.0.0/8,**********/12,***********/16
#TRUSTED_HOSTS='^(localhost|example\.com)$'
###< symfony/framework-bundle ###

###> symfony/mailer ###
# Production SMTP configuration - update with your actual SMTP server details
MAILER_DSN=smtp://localhost:25
#MAILER_URL=smtp://mailcatcher:1025?encryption=&auth_mode=
###< symfony/mailer ###

###> doctrine/doctrine-bundle ###
# Format described at https://www.doctrine-project.org/projects/doctrine-dbal/en/latest/reference/configuration.html#connecting-using-a-url
# For an SQLite database, use="sqlite:///%kernel.project_dir%/var/data.db"
# For a PostgreSQL database, use="postgresql://db_user:db_password@127.0.0.1:5432/db_name?serverVersion=11&charset=utf8"
# IMPORTANT=You MUST configure your server version, either here or in config/packages/doctrine.yaml
DATABASE_URL="mysql://${DB_USER:-user}:${DB_PASS:-password}@${DB_HOST:-mariadb}:${DB_PORT:-3306}/${DB_NAME:-simple-website}?serverVersion=${DB_SERVER_VERSION:-mariadb-10.8.3}&charset=${DB_CHARSET:-utf8}"
###< doctrine/doctrine-bundle ###

MESSENGER_TRANSPORT_DSN=doctrine://default

APP_SESSION_MAX_IDLE_TIME=1200

# REDIS_HOST=127.0.0.1
REDIS_HOST=redis-symfony4
REDIS_PORT=6379

################################################
#IZBERG config
################################################
IZBERG_API_DOMAIN=api.iceberg.technology
# ORIG # TB # IZBERG_API_SELLER_DOMAIN=stationone-old.izberg-marketplace.com
# OLD # TB # IZBERG_API_SELLER_DOMAIN=stationone.merchant.izberg-marketplace.com
IZBERG_API_SELLER_DOMAIN=seller-jwt.izberg-marketplace.com
IZBERG_API_VERSION=1
IZBERG_API_PROTOCOL=https
IZBERG_ACCESS_TOKEN=5a53ae2a8f3864236a8f0d7662f8fff35fa93ebd
IZBERG_APPLICATION_ID=79
IZBERG_APPLICATION_NAMESPACE=alstom
IZBERG_SECRET_KEY=4f3efb5c-b409-49c5-8612-fac2bf183fd9
IZBERG_EMAIL=<EMAIL>
IZBERG_USERNAME=alstom_admin2346
IZBERG_FIRST_NAME=ServiceAccount
IZBERG_LAST_NAME=OPEN
IZBERG_JWT_SECRET=i5jB8bjgkW9tPUfIS67DG2M8Hjd20aCJSl20nuf83TsSSEL58i
IZBERG_MERCHANT_BASE_URL='https://vendor.station-one.com/'
IZBERG_MERCHANT_CARRIER=133
IZBERG_MERCHANT_SHIPPING_PROVIDER=166
IZBERG_MERCHANT_ZONE=144
IZBERG_CARRIER_ENDPOINT='/v1/carrier/133/'
IZBERG_EMAIL_SELLER_DOMAIN=station-one.com


IZBERG_CONSOLE_USERNAME=consoleaccount_open1736
IZBERG_CONSOLE_ACCESS_TOKEN=0df11c55a7b326f1b241634653cc916b6a3e2e0d

IZBERG_WEBHOOK_USERNAME=asynchrone_open1111
IZBERG_WEBHOOK_ACCESS_TOKEN=5725bb3dd5ede2a3a8071ddaf04870563dbc73a7

IZBERG_ALGOLIA_APPLICATION_ID=RT2KKKW0AD
IZBERG_ALGOLIA_WRITE_APIKEY=0000000000000000000000000000000#********************************
IZBERG_ALGOLIA_READ_APIKEY=********************************
IZBERG_ALGOLIA_INDEX_FR_BASE_INDEX=PROD:channel:alstom:product_offer:backoffice:7441:2055:fr:
IZBERG_ALGOLIA_INDEX_EN_BASE_INDEX=PROD:channel:alstom:product_offer:backoffice:7500:2062:en:
IZBERG_ALGOLIA_INDEX_ES_BASE_INDEX=PROD:channel:alstom:product_offer:backoffice:7438:2052:es:
IZBERG_ALGOLIA_INDEX_DE_BASE_INDEX=PROD:channel:alstom:product_offer:backoffice:7522:2072:de:
IZBERG_ALGOLIA_INDEX_IT_BASE_INDEX=PROD:channel:alstom:product_offer:backoffice:7517:2067:it:
IZBERG_ALGOLIA_INDEX_NL_BASE_INDEX=PROD:channel:alstom:product_offer:backoffice:19694:17251:nl:
IZBERG_ALGOLIA_QUERY_SUGGESTION_INDEX_FR=DEV_RELEVANCE_FR_query_suggestions
IZBERG_ALGOLIA_QUERY_SUGGESTION_INDEX_EN=DEV_RELEVANCE_EN_query_suggestions
IZBERG_ALGOLIA_QUERY_SUGGESTION_INDEX_ES=DEV_RELEVANCE_ES_query_suggestions
IZBERG_ALGOLIA_QUERY_SUGGESTION_INDEX_DE=DEV_RELEVANCE_DE_query_suggestions
IZBERG_ALGOLIA_QUERY_SUGGESTION_INDEX_IT=DEV_RELEVANCE_IT_query_suggestions
IZBERG_ALGOLIA_QUERY_SUGGESTION_INDEX_NL=DEV_RELEVANCE_NL_query_suggestions

IZBERG_VENDOR_OPENIDCONNECT_CLIENT_ID=f553240fbd7996620fc5765a6eb1fc8d
IZBERG_VENDOR_OPENIDCONNECT_CLIENT_SECRET=2VGRfSVnCNuHbcCIpgForUBqGrFzyi3APLn7e1mfopE
#IZBERG_VENDOR_OPENIDCONNECT_CLIENT_ID=f553240fbd7996620fc5765a6eb1fc8d
#IZBERG_VENDOR_OPENIDCONNECT_CLIENT_SECRET=2VGRfSVnCNuHbcCIpgForUBqGrFzyi3APLn7e1mfopE
IZBERG_VENDOR_OPENIDCONNECT_URL_AUTHORIZE='https://izberg.me/auth'
IZBERG_VENDOR_OPENIDCONNECT_URL_ACCESS_TOKEN='https://api.izberg.me/auth/token'

IZBERG_DATE_PATTERN='Y-m-d\TH:i:s+'

###################################################
# Izberg shipping product
###################################################
IZBERG_TRANSPORT_ID=46684

###################################################
# Izberg fees operator commission id
###################################################
IZBERG_OPERATOR_FEES_EURO_COMMISSION_ID=4853
IZBERG_OPERATOR_FEES_USD_COMMISSION_ID=4852

################################################
#WEBHELP config
################################################
WEBHELP_SCHEME='https'
WEBHELP_DOMAINE='test.fdi-logbox.com/rc/kyc/'
WEBHELP_USERNAME='<EMAIL>'
WEBHELP_PASSWORD='MarketP2018!'
WEBHELP_IBAN_ACCOUNT_NAME_EUR="Account name EUR"
WEBHELP_IBAN_ACCOUNT_NAME_USD="Account name USD"
WEBHELP_IBAN_EUR="***************************\tEUR"
WEBHELP_IBAN_USD="***************************\tUSD"

################################################
# Upela Config
################################################
UPELA_API_URL='https://test.upela.com/api/v4/rateWidget'
UPELA_PRE_ORDER_API_URL='https://preorder.klh-competence.com'
UPELA_PRE_ORDER_USERNAME='<EMAIL>'
UPELA_PRE_ORDER_PASSWORD='password_test'

UPELA_SUPPORT_EMAIL_ADDRESS='["<EMAIL>"]'

################################################
#fosUser config
################################################
FOSUSER_FROM_EMAIL='<EMAIL>'
FOSUSER_FROM_NAME='Support'

################################################
# LIST OF CATEGORIES, that must not be displayed
################################################
IGNORED_CATEGORIES_ID='[86241]'

###################################################
# Recaptcha keys
###################################################
RECAPTCHA_PUBLIC_KEY=6Lcyuc4ZAAAAAMgYtImhTZktgjLUw6BYidh76KGB
RECAPTCHA_PRIVATE_KEY=6Lcyuc4ZAAAAAFyg7Zd3ylToBwIaFNB6r9m1hTOn

################################################
# OTHER
################################################

SUPPORTED_LOCALES='["en","fr","es","de","it","nl"]'
DISPLAY_FIRST_LEVEL_CATEGORY_ONLY=true;

DOMAIN='alstom.local'
PROTOCOL='http://'
ENVIRONMENT="[LOCALE] "

MAX_ITEM_COMPARISON=5
CACHE_PAYMENT_TERMS_TTL=60

PAYMENT_NB_DAYS_BEFORE_REMINDER=5
PAYMENT_NB_DAYS_BEFORE_LATE_REMINDER='[10,20,30]'
PAYMENT_NB_HOURS_BEFORE_REFUND=48

CRYPT_KEY=ODkuPMY1OS4zMTUg

ORDER_MAX_DAY_LIMIT_BEFORE_AUTOCANCEL=15
CREDIT_CART_TIMEOUT=30

CGU_SLUG='gtu-buyers'

MAIL_BCC='[]'

PURCHASE_REQUEST_EMAIL_ADDRESS='["<EMAIL>"]'

IZBERG_SHIPPING_PRODUCT_ID=6250253

MAILER_TRANSPORT=smtp
MAILER_HOST=mailhog
MAILER_USERNAME=null
MAILER_PASSWORD=null
MAILER_PORT=1025

EXPORT_CATALOGUE_TEMPLATE_PATH="../public/docs/catalog_export_template_28-05-2021.csv"

# TODO add clearance real category
STOCK_CLEARANCE_CATEGORY=1119169

###> lexik/jwt-authentication-bundle ###
JWT_SECRET_KEY=%kernel.project_dir%/config/jwt/private.pem
JWT_PUBLIC_KEY=%kernel.project_dir%/config/jwt/public.pem
JWT_PASSPHRASE=af74ecbe25416763a45c2582b8640418
###< lexik/jwt-authentication-bundle ###

###> nelmio/cors-bundle ###
CORS_ALLOW_ORIGIN='^https?://(localhost|127\.0\.0\.1)(:[0-9]+)?$'
###< nelmio/cors-bundle ###

E_CATALOG_URL=/e-catalog

###################################################
# Izberg M2M operator info
###################################################
IZBERG_M2MOPERATOR_CLIENT_ID="m2m-926b5e31f5aae4061dfaebd8eb6e18c6"
IZBERG_M2MOPERATOR_CLIENT_SECRET="SXKbv7F_sEiozE4HXqeZH7Rv1vsoRH6V3W0ehC1izSs"
IZBERG_DOMAIN_ID="802504BE7D"
#IZBERG_AUDIENCE="https://api.iceberg.technology"
IZBERG_AUDIENCE="https://api.izberg.me"
#IZBERG_CREATE_MERCHANT_USER_URL=" https://api.izberg.me/v1/user/"
IZBERG_CREATE_MERCHANT_USER_URL="https://api.izberg.me/v1/user/"
#IZBERG_IDENTITY_API_URL=" https://api.izberg.me/v1/"
IZBERG_IDENTITY_API_URL="https://api.izberg.me/v1/"
