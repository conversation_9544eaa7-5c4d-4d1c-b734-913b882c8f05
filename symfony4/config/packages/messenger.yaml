framework:
    messenger:
        default_bus: messenger.bus.default
        buses:
            messenger.bus.default:
                default_middleware: true
            query.bus: ~
            command.bus: ~

        transports:
            async_middleware_api:
                dsn: "%env(MESSENGER_TRANSPORT_DSN)%"
                options:
                    queue_name: middleware_api
                retry_strategy:
                    max_retries: 0

            async_priority_high:
                dsn: "%env(MESSENGER_TRANSPORT_DSN)%"
                options:
                    queue_name: high

            async_priority_low:
                dsn: "%env(MESSENGER_TRANSPORT_DSN)%"
                options:
                    queue_name: low

            sync: 'sync://'

        routing:
            # async is whatever name you gave your transport above
            'AppBundle\Message\AutoCancelOrder': async_priority_low
            'AppBundle\Message\ImportCompanyCatalogue': async_priority_low
            'AppBundle\Message\PaymentBankTransfer': async_priority_high
            'AppBundle\Message\PaymentTerm': async_priority_high
            'AppBundle\Message\SetMerchantOrderExtraInfo': async_priority_high
            'AppBundle\Message\SyncCreditNote': async_priority_low
            'AppBundle\Message\SyncInvoice': async_priority_low
            'AppBundle\Message\SyncOrder': async_priority_low
            'Api\Domain\PurchaseRequest\Message\CreatePurchaseRequest': async_middleware_api
            'Api\Domain\Order\Message\OrderPayloadMessage': async_middleware_api
            'Api\Domain\Invoice\Message\InvoicePayloadMessage': async_middleware_api
            'Api\Domain\CostCenter\Message\CostCenterPayloadMessage': async_middleware_api
            # Route email messages to high priority queue for faster processing
            'Symfony\Component\Mailer\Messenger\SendEmailMessage': async_priority_high
            'Api\Core\Bus\Query\QueryInterface': sync
            'Api\Core\Bus\Command\InputInterface': sync
