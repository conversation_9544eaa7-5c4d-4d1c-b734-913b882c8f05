#!/bin/bash

# Migration script for <PERSON><PERSON><PERSON> to Symfony Mailer
# This script helps migrate from the old Swiftmailer spool system to Symfony Messenger

echo "=== Station One Email System Migration ==="
echo "Migrating from Swiftmailer to Symfony Mailer with Messenger"
echo ""

# Check if running as root
if [ "$EUID" -eq 0 ]; then
    echo "⚠️  Warning: Running as root. Consider running as www-data user."
fi

# Function to check if command exists
command_exists() {
    command -v "$1" >/dev/null 2>&1
}

# Check prerequisites
echo "🔍 Checking prerequisites..."

if ! command_exists php; then
    echo "❌ PHP not found. Please install PHP first."
    exit 1
fi

if ! command_exists supervisorctl; then
    echo "❌ Supervisor not found. Please install supervisor first:"
    echo "   sudo apt-get install supervisor"
    exit 1
fi

echo "✅ Prerequisites check passed"
echo ""

# Check for old cron jobs
echo "🔍 Checking for old Swiftmailer cron jobs..."
CRON_CHECK=$(crontab -l 2>/dev/null | grep -i "swiftmailer:spool" || echo "")

if [ ! -z "$CRON_CHECK" ]; then
    echo "⚠️  Found old Swiftmailer cron jobs:"
    echo "$CRON_CHECK"
    echo ""
    echo "❌ CRITICAL: Please remove these cron jobs manually:"
    echo "   crontab -e"
    echo "   Then delete any lines containing 'swiftmailer:spool:send'"
    echo ""
    read -p "Have you removed the old cron jobs? (y/N): " -n 1 -r
    echo
    if [[ ! $REPLY =~ ^[Yy]$ ]]; then
        echo "Please remove the old cron jobs first, then run this script again."
        exit 1
    fi
else
    echo "✅ No old Swiftmailer cron jobs found"
fi

# Get application path
APP_PATH=$(pwd)
if [ ! -f "$APP_PATH/bin/console" ]; then
    echo "❌ Symfony console not found. Please run this script from the application root directory."
    exit 1
fi

echo "📁 Application path: $APP_PATH"

# Test console commands
echo ""
echo "🧪 Testing Symfony console commands..."

if ! php bin/console list | grep -q "messenger:consume"; then
    echo "❌ Messenger commands not available. Please check your Symfony installation."
    exit 1
fi

if php bin/console list | grep -q "swiftmailer:spool"; then
    echo "⚠️  Warning: Swiftmailer commands still available. This is normal during migration."
fi

echo "✅ Console commands check passed"

# Check messenger configuration
echo ""
echo "🔧 Checking messenger configuration..."

if ! php bin/console messenger:stats >/dev/null 2>&1; then
    echo "❌ Messenger not properly configured. Please check config/packages/messenger.yaml"
    exit 1
fi

echo "✅ Messenger configuration check passed"

# Setup supervisor configuration
echo ""
echo "🔧 Setting up Supervisor configuration..."

SUPERVISOR_CONF="/etc/supervisor/conf.d/messenger-worker.conf"

if [ -f "$SUPERVISOR_CONF" ]; then
    echo "⚠️  Supervisor configuration already exists: $SUPERVISOR_CONF"
    read -p "Do you want to update it? (y/N): " -n 1 -r
    echo
    if [[ $REPLY =~ ^[Yy]$ ]]; then
        UPDATE_SUPERVISOR=true
    else
        UPDATE_SUPERVISOR=false
    fi
else
    UPDATE_SUPERVISOR=true
fi

if [ "$UPDATE_SUPERVISOR" = true ]; then
    echo "📝 Creating supervisor configuration..."
    
    sudo tee "$SUPERVISOR_CONF" > /dev/null <<EOF
;/etc/supervisor/conf.d/messenger-worker.conf
[program:messenger-consume]
command=php $APP_PATH/bin/console messenger:consume async_priority_high async_priority_low --limit=10
user=www-data
numprocs=2
startsecs=0
autostart=true
autorestart=true
process_name=%(program_name)s_%(process_num)02d
stderr_logfile=/var/log/messenger-worker.err.log
stdout_logfile=/var/log/messenger-worker.out.log
EOF

    echo "✅ Supervisor configuration created"
    
    # Reload supervisor
    echo "🔄 Reloading supervisor..."
    sudo supervisorctl reread
    sudo supervisorctl update
    
    # Start workers
    echo "🚀 Starting messenger workers..."
    sudo supervisorctl start messenger-consume:*
    
    echo "✅ Messenger workers started"
else
    echo "⏭️  Skipping supervisor configuration update"
fi

# Check worker status
echo ""
echo "📊 Checking worker status..."
sudo supervisorctl status messenger-consume:*

# Test email functionality
echo ""
echo "🧪 Testing email functionality..."

if php bin/console mailer:test <EMAIL> >/dev/null 2>&1; then
    echo "✅ Email test passed"
else
    echo "⚠️  Email test failed. Please check your MAILER_DSN configuration."
fi

# Show queue status
echo ""
echo "📊 Current queue status:"
php bin/console messenger:stats

echo ""
echo "🎉 Migration completed!"
echo ""
echo "📋 Next steps:"
echo "1. Update your MAILER_DSN in .env.prod with your production SMTP settings"
echo "2. Test password reset functionality on your application"
echo "3. Monitor worker logs: tail -f /var/log/messenger-worker.out.log"
echo "4. Monitor queue status: php bin/console messenger:stats"
echo ""
echo "🚨 Remember: NEVER use 'swiftmailer:spool:send' commands anymore!"
echo "   Use 'php bin/console messenger:consume' instead."
