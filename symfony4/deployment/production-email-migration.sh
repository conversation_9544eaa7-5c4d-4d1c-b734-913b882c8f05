#!/bin/bash

# Production Email System Migration Script
# Migrates from Swiftmailer cron jobs to Symfony Messenger workers

set -e  # Exit on any error

echo "🚀 Station One Production Email Migration"
echo "=========================================="
echo ""

# Configuration
APP_PATH="/home/<USER>/alstom"
BACKUP_DIR="/tmp/email-migration-backup-$(date +%Y%m%d-%H%M%S)"
SUPERVISOR_CONF="/etc/supervisor/conf.d/messenger-worker.conf"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${GREEN}✅ $1${NC}"
}

print_warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

print_error() {
    echo -e "${RED}❌ $1${NC}"
}

print_info() {
    echo -e "${BLUE}ℹ️  $1${NC}"
}

# Check if running as root or with sudo
if [ "$EUID" -ne 0 ]; then
    print_error "This script must be run as root or with sudo"
    echo "Usage: sudo $0"
    exit 1
fi

# Create backup directory
mkdir -p "$BACKUP_DIR"
print_info "Backup directory: $BACKUP_DIR"

echo ""
echo "🔍 STEP 1: Analyzing current cron jobs"
echo "======================================"

# Check current crontab for www-data user
print_info "Checking www-data crontab..."
if crontab -u www-data -l > "$BACKUP_DIR/www-data-crontab-backup.txt" 2>/dev/null; then
    print_status "Current crontab backed up to: $BACKUP_DIR/www-data-crontab-backup.txt"
    
    # Check for swiftmailer entries
    SWIFT_ENTRIES=$(grep -i "swiftmailer" "$BACKUP_DIR/www-data-crontab-backup.txt" || echo "")
    if [ ! -z "$SWIFT_ENTRIES" ]; then
        print_warning "Found Swiftmailer cron entries:"
        echo "$SWIFT_ENTRIES"
        echo ""
    else
        print_info "No Swiftmailer entries found in www-data crontab"
    fi
else
    print_info "No existing crontab for www-data user"
fi

# Check root crontab as well
print_info "Checking root crontab..."
if crontab -l > "$BACKUP_DIR/root-crontab-backup.txt" 2>/dev/null; then
    SWIFT_ENTRIES_ROOT=$(grep -i "swiftmailer" "$BACKUP_DIR/root-crontab-backup.txt" || echo "")
    if [ ! -z "$SWIFT_ENTRIES_ROOT" ]; then
        print_warning "Found Swiftmailer cron entries in root crontab:"
        echo "$SWIFT_ENTRIES_ROOT"
        echo ""
    fi
else
    print_info "No existing root crontab"
fi

echo ""
echo "🛑 STEP 2: Removing old Swiftmailer cron jobs"
echo "============================================="

# Remove swiftmailer entries from www-data crontab
if [ -f "$BACKUP_DIR/www-data-crontab-backup.txt" ]; then
    # Create new crontab without swiftmailer entries
    grep -v -i "swiftmailer" "$BACKUP_DIR/www-data-crontab-backup.txt" > "$BACKUP_DIR/www-data-crontab-new.txt" || true
    
    # Apply new crontab
    crontab -u www-data "$BACKUP_DIR/www-data-crontab-new.txt"
    print_status "Removed Swiftmailer entries from www-data crontab"
else
    print_info "No www-data crontab to modify"
fi

echo ""
echo "📦 STEP 3: Installing Supervisor"
echo "================================"

# Check if supervisor is installed
if command -v supervisorctl >/dev/null 2>&1; then
    print_status "Supervisor is already installed"
else
    print_info "Installing Supervisor..."
    apt-get update
    apt-get install -y supervisor
    print_status "Supervisor installed successfully"
fi

echo ""
echo "⚙️  STEP 4: Configuring Messenger Workers"
echo "=========================================="

# Create supervisor configuration
print_info "Creating Supervisor configuration..."

cat > "$SUPERVISOR_CONF" << EOF
;/etc/supervisor/conf.d/messenger-worker.conf
[program:messenger-consume]
command=php $APP_PATH/bin/console messenger:consume async_priority_high async_priority_low --limit=10 --env=prod
user=www-data
numprocs=2
startsecs=0
autostart=true
autorestart=true
process_name=%(program_name)s_%(process_num)02d
stderr_logfile=/var/log/messenger-worker.err.log
stdout_logfile=/var/log/messenger-worker.out.log
redirect_stderr=false
stdout_logfile_maxbytes=10MB
stdout_logfile_backups=5
stderr_logfile_maxbytes=10MB
stderr_logfile_backups=5
EOF

print_status "Supervisor configuration created: $SUPERVISOR_CONF"

# Set proper permissions on log files
touch /var/log/messenger-worker.out.log /var/log/messenger-worker.err.log
chown www-data:www-data /var/log/messenger-worker.out.log /var/log/messenger-worker.err.log

echo ""
echo "🔄 STEP 5: Starting Messenger Workers"
echo "====================================="

# Reload supervisor configuration
print_info "Reloading Supervisor configuration..."
supervisorctl reread
supervisorctl update

# Start the workers
print_info "Starting Messenger workers..."
supervisorctl start messenger-consume:*

# Check status
print_info "Checking worker status..."
supervisorctl status messenger-consume:*

echo ""
echo "🧪 STEP 6: Testing Email System"
echo "==============================="

# Test console access
print_info "Testing console access..."
if sudo -u www-data php "$APP_PATH/bin/console" list | grep -q "messenger:consume"; then
    print_status "Console access working"
else
    print_error "Console access failed"
    exit 1
fi

# Test messenger stats
print_info "Testing messenger stats..."
if sudo -u www-data php "$APP_PATH/bin/console" messenger:stats --env=prod >/dev/null 2>&1; then
    print_status "Messenger system working"
else
    print_error "Messenger system failed"
    exit 1
fi

echo ""
echo "📊 STEP 7: Migration Summary"
echo "============================"

print_status "Migration completed successfully!"
echo ""
print_info "What was changed:"
echo "  • Removed old Swiftmailer cron jobs"
echo "  • Installed/configured Supervisor"
echo "  • Created Messenger worker processes"
echo "  • Started 2 worker processes for email handling"
echo ""
print_info "Backup files created in: $BACKUP_DIR"
echo ""
print_info "Monitoring commands:"
echo "  • Worker status: supervisorctl status messenger-consume:*"
echo "  • Queue stats: sudo -u www-data php $APP_PATH/bin/console messenger:stats --env=prod"
echo "  • Worker logs: tail -f /var/log/messenger-worker.out.log"
echo "  • Error logs: tail -f /var/log/messenger-worker.err.log"
echo ""
print_warning "IMPORTANT: Update your MAILER_DSN in $APP_PATH/.env.prod with production SMTP settings"
echo ""
print_status "Email system migration complete! 🎉"
