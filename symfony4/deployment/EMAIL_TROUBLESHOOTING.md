# Email System Troubleshooting Guide

## Quick Fix for Production Email Issues

### ❌ Problem: `swiftmailer:spool:send` command not found

**Error message:**
```
There are no commands defined in the "swiftmailer:spool" namespace.
```

**Solution:** This application uses Symfony Mailer with <PERSON>, not <PERSON><PERSON><PERSON>.

### ✅ Correct Commands

| Old Command (DON'T USE) | New Command (USE THIS) |
|------------------------|------------------------|
| `swiftmailer:spool:send --time-limit=10` | `messenger:consume async_priority_high async_priority_low --limit=10` |
| `swiftmailer:spool:send --message-limit=100` | `messenger:consume async_priority_high async_priority_low --limit=100` |

### 🔧 Production Setup Checklist

1. **Remove old cron jobs:**
   ```bash
   crontab -e
   # Delete any lines with 'swiftmailer:spool:send'
   ```

2. **Install Supervisor:**
   ```bash
   sudo apt-get install supervisor
   ```

3. **Create worker configuration:**
   ```bash
   sudo nano /etc/supervisor/conf.d/messenger-worker.conf
   ```
   
   Content:
   ```ini
   [program:messenger-consume]
   command=php /path/to/app/bin/console messenger:consume async_priority_high async_priority_low --limit=10
   user=www-data
   numprocs=2
   autostart=true
   autorestart=true
   ```

4. **Start workers:**
   ```bash
   sudo supervisorctl reread
   sudo supervisorctl update
   sudo supervisorctl start messenger-consume:*
   ```

5. **Configure SMTP:**
   Update `.env.prod`:
   ```bash
   MAILER_DSN=smtp://your-smtp-server:587?encryption=tls&auth_mode=login&username=user&password=pass
   ```

### 📊 Monitoring Commands

```bash
# Check queue status
php bin/console messenger:stats

# Check worker status
sudo supervisorctl status messenger-consume:*

# View worker logs
tail -f /var/log/messenger-worker.out.log
tail -f /var/log/messenger-worker.err.log

# Test email sending
php bin/console mailer:test <EMAIL>
```

### 🚨 Common Issues

#### Issue: Emails not being sent
**Check:**
1. Are messenger workers running? `sudo supervisorctl status`
2. Are there messages in queue? `php bin/console messenger:stats`
3. Is MAILER_DSN correct? Check `.env.prod`
4. Check worker logs for errors

#### Issue: Workers keep crashing
**Check:**
1. Worker logs: `tail -f /var/log/messenger-worker.err.log`
2. PHP memory limit
3. Database connection issues
4. SMTP server connectivity

#### Issue: Emails sent but not received
**Check:**
1. SMTP server logs
2. Email templates are correct
3. Recipient email addresses are valid
4. Check spam folders

### 🔄 Restart Workers

```bash
# Restart all workers
sudo supervisorctl restart messenger-consume:*

# Stop all workers
sudo supervisorctl stop messenger-consume:*

# Start all workers
sudo supervisorctl start messenger-consume:*
```

### 📞 Emergency Commands

If emails are stuck and need immediate processing:

```bash
# Process all pending messages (run manually)
php bin/console messenger:consume async_priority_high async_priority_low --limit=1000 --time-limit=300

# Clear failed messages (use with caution)
php bin/console messenger:failed:show
php bin/console messenger:failed:retry
```

### 🎯 Testing Password Reset

1. Go to password reset page
2. Enter email address
3. Check queue: `php bin/console messenger:stats`
4. Check if workers are processing: `sudo supervisorctl status`
5. Check email delivery in logs

### 📧 SMTP Configuration Examples

**Gmail:**
```
MAILER_DSN=smtp://smtp.gmail.com:587?encryption=tls&auth_mode=login&username=<EMAIL>&password=your-app-password
```

**SendGrid:**
```
MAILER_DSN=smtp://smtp.sendgrid.net:587?encryption=tls&auth_mode=login&username=apikey&password=your-api-key
```

**Local SMTP:**
```
MAILER_DSN=smtp://localhost:25
```

**Custom SMTP:**
```
MAILER_DSN=smtp://mail.yourserver.com:587?encryption=tls&auth_mode=login&username=<EMAIL>&password=yourpassword
```
