## PRODUCTION INSTALLATION

## Email Configuration

### IMPORTANT: Swiftmailer Migration Notice
This application has been migrated from Swiftmailer to Symfony Mailer.
**DO NOT USE** `swiftmailer:spool:send` commands - they will fail.

### Email Processing Setup

Emails are processed asynchronously using Symfony Messenger. The application routes email messages to the `async_priority_high` queue for fast processing.

### 1. Configure SMTP Settings

Update your production `.env.prod` file with your actual SMTP server details:

```bash
# Production SMTP configuration
MAILER_DSN=smtp://your-smtp-server:587?encryption=tls&auth_mode=login&username=your-username&password=your-password
```

Examples for common providers:
- **Gmail**: `smtp://smtp.gmail.com:587?encryption=tls&auth_mode=login&username=<EMAIL>&password=your-app-password`
- **SendGrid**: `smtp://smtp.sendgrid.net:587?encryption=tls&auth_mode=login&username=apikey&password=your-api-key`
- **Local SMTP**: `smtp://localhost:25` (if you have a local mail server)

### 2. Messenger: Queued Message Handling

Install supervisor on the production server:
```bash
sudo apt-get install supervisor
```

Configure supervisor by creating `/etc/supervisor/conf.d/messenger-worker.conf`:

```ini
;/etc/supervisor/conf.d/messenger-worker.conf
[program:messenger-consume]
command=php /path/to/your/app/bin/console messenger:consume async_priority_high async_priority_low --limit=10
user=www-data
numprocs=2
startsecs=0
autostart=true
autorestart=true
process_name=%(program_name)s_%(process_num)02d
stderr_logfile=/var/log/messenger-worker.err.log
stdout_logfile=/var/log/messenger-worker.out.log
```

Start the workers:
```bash
sudo supervisorctl reread
sudo supervisorctl update
sudo supervisorctl start messenger-consume:*
```

### 3. Remove Old Cron Jobs

**CRITICAL**: Remove any existing cron jobs that use `swiftmailer:spool:send`.
Check your crontab and remove lines like:
```bash
# REMOVE THIS - it will cause errors:
# */5 * * * * php /path/to/app/bin/console swiftmailer:spool:send --time-limit=10 --env=prod
```

### 4. Monitoring Email Processing

Monitor email queue status:
```bash
php bin/console messenger:stats
```

Monitor supervisor workers:
```bash
sudo supervisorctl status messenger-consume:*
```

Check worker logs:
```bash
tail -f /var/log/messenger-worker.out.log
tail -f /var/log/messenger-worker.err.log
```

