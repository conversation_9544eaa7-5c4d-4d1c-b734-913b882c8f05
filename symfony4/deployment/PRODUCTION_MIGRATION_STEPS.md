# Production Email System Migration - Step by Step Guide

## 🚨 CRITICAL: This migration must be done during maintenance window

**Estimated downtime**: 5-10 minutes for email processing only (web application remains available)

## Pre-Migration Checklist

- [ ] Backup current crontab: `sudo crontab -u www-data -l > /tmp/crontab-backup-$(date +%Y%m%d).txt`
- [ ] Verify application path: `/home/<USER>/alstom`
- [ ] Ensure you have root/sudo access
- [ ] Notify team about email processing maintenance

## Option A: Automated Migration (Recommended)

### 1. Download and Run Migration Script

```bash
# On production server
cd /home/<USER>/alstom
sudo ./deployment/production-email-migration.sh
```

The script will:
- ✅ Backup existing crontabs
- ✅ Remove old Swiftmailer cron jobs
- ✅ Install/configure Supervisor
- ✅ Start Messenger workers
- ✅ Test the new system

## Option B: Manual Migration (If script fails)

### 1. Backup Current Configuration

```bash
# Backup www-data crontab
sudo crontab -u www-data -l > /tmp/www-data-crontab-backup.txt

# Backup root crontab (if exists)
sudo crontab -l > /tmp/root-crontab-backup.txt 2>/dev/null || echo "No root crontab"
```

### 2. Remove Old Swiftmailer Cron Jobs

```bash
# Edit www-data crontab
sudo crontab -u www-data -e

# Remove or comment out lines containing 'swiftmailer:spool:send'
# The problematic line looks like:
# * * * * * [ `pgrep -fc swiftmailer:spool:send` -lt 2 ] && php /home/<USER>/alstom/bin/console swiftmailer:spool:send --time-limit=10 --env=prod

# Save and exit the editor
```

### 3. Install Supervisor (if not installed)

```bash
sudo apt-get update
sudo apt-get install supervisor
```

### 4. Create Supervisor Configuration

```bash
sudo nano /etc/supervisor/conf.d/messenger-worker.conf
```

**Content:**
```ini
;/etc/supervisor/conf.d/messenger-worker.conf
[program:messenger-consume]
command=php /home/<USER>/alstom/bin/console messenger:consume async_priority_high async_priority_low --limit=10 --env=prod
user=www-data
numprocs=2
startsecs=0
autostart=true
autorestart=true
process_name=%(program_name)s_%(process_num)02d
stderr_logfile=/var/log/messenger-worker.err.log
stdout_logfile=/var/log/messenger-worker.out.log
redirect_stderr=false
stdout_logfile_maxbytes=10MB
stdout_logfile_backups=5
stderr_logfile_maxbytes=10MB
stderr_logfile_backups=5
```

### 5. Create Log Files and Set Permissions

```bash
sudo touch /var/log/messenger-worker.out.log /var/log/messenger-worker.err.log
sudo chown www-data:www-data /var/log/messenger-worker.out.log /var/log/messenger-worker.err.log
```

### 6. Start Supervisor Workers

```bash
# Reload supervisor configuration
sudo supervisorctl reread
sudo supervisorctl update

# Start the workers
sudo supervisorctl start messenger-consume:*

# Check status
sudo supervisorctl status messenger-consume:*
```

## Post-Migration Verification

### 1. Verify Workers Are Running

```bash
sudo supervisorctl status messenger-consume:*
```

**Expected output:**
```
messenger-consume:messenger-consume_00   RUNNING   pid 12345, uptime 0:00:05
messenger-consume:messenger-consume_01   RUNNING   pid 12346, uptime 0:00:05
```

### 2. Test Messenger System

```bash
# Check queue status
sudo -u www-data php /home/<USER>/alstom/bin/console messenger:stats --env=prod

# Test email functionality (optional)
sudo -u www-data php /home/<USER>/alstom/bin/console mailer:test <EMAIL> --env=prod
```

### 3. Monitor Logs

```bash
# Watch worker output
tail -f /var/log/messenger-worker.out.log

# Watch for errors
tail -f /var/log/messenger-worker.err.log

# Check system logs for cron errors (should be gone)
tail -f /var/log/syslog | grep -i swiftmailer
```

### 4. Verify No More Cron Errors

Wait 2-3 minutes, then check:
```bash
# Should show no new swiftmailer errors
sudo grep "swiftmailer:spool:send" /var/log/syslog | tail -5
```

## Production SMTP Configuration

### Update MAILER_DSN in Production

Edit `/home/<USER>/alstom/.env.prod`:

```bash
# Replace with your production SMTP settings
MAILER_DSN=smtp://your-smtp-server:587?encryption=tls&auth_mode=login&username=your-username&password=your-password
```

**Common production examples:**

**Gmail/Google Workspace:**
```
MAILER_DSN=smtp://smtp.gmail.com:587?encryption=tls&auth_mode=login&username=<EMAIL>&password=your-app-password
```

**SendGrid:**
```
MAILER_DSN=smtp://smtp.sendgrid.net:587?encryption=tls&auth_mode=login&username=apikey&password=your-sendgrid-api-key
```

**Custom SMTP:**
```
MAILER_DSN=smtp://mail.yourdomain.com:587?encryption=tls&auth_mode=login&username=<EMAIL>&password=your-password
```

## Monitoring and Maintenance

### Daily Monitoring Commands

```bash
# Check worker health
sudo supervisorctl status messenger-consume:*

# Check queue status
sudo -u www-data php /home/<USER>/alstom/bin/console messenger:stats --env=prod

# Check recent worker activity
tail -20 /var/log/messenger-worker.out.log
```

### Restart Workers (if needed)

```bash
# Restart all workers
sudo supervisorctl restart messenger-consume:*

# Restart specific worker
sudo supervisorctl restart messenger-consume:messenger-consume_00
```

### Emergency: Process Stuck Emails

```bash
# If emails are stuck, manually process queue
sudo -u www-data php /home/<USER>/alstom/bin/console messenger:consume async_priority_high async_priority_low --limit=100 --time-limit=300 --env=prod
```

## Rollback Plan (Emergency Only)

If the new system fails and you need to temporarily restore the old cron job:

```bash
# Restore old crontab (TEMPORARY ONLY - will still fail)
sudo crontab -u www-data /tmp/www-data-crontab-backup.txt

# Stop messenger workers
sudo supervisorctl stop messenger-consume:*
```

**Note:** This rollback will NOT work because Swiftmailer is removed. This is only to stop the new system while you troubleshoot.

## Success Criteria

- [ ] No more "swiftmailer:spool:send" errors in `/var/log/syslog`
- [ ] Supervisor workers running: `sudo supervisorctl status messenger-consume:*`
- [ ] Queue processing: `sudo -u www-data php /home/<USER>/alstom/bin/console messenger:stats --env=prod`
- [ ] Password reset emails being sent and received
- [ ] Worker logs showing activity: `tail -f /var/log/messenger-worker.out.log`

## Support Contacts

After migration, monitor for 24-48 hours and verify:
1. Password reset emails are working
2. All other application emails are working
3. No error logs related to email processing
4. Queue is processing normally
