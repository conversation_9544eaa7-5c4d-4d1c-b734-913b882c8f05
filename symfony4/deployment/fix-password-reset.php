<?php

/**
 * Password Reset Email Fix Script
 * 
 * This script fixes the most common issues preventing password reset emails from being sent:
 * 1. Enables the RESET_PASSWORD_TO_USER transactional email
 * 2. Ensures the email template exists and is published
 * 3. Creates email content for all supported languages
 */

echo "🔧 Password Reset Email Fix Script\n";
echo "==================================\n\n";

// Database configuration from environment
$host = $_ENV['DB_HOST'] ?? 'mariadb';
$port = $_ENV['DB_PORT'] ?? '3306';
$dbname = $_ENV['DB_NAME'] ?? 'simple-website';
$username = $_ENV['DB_USER'] ?? 'user';
$password = $_ENV['DB_PASS'] ?? 'password';

try {
    // Connect to database
    echo "📡 Connecting to database...\n";
    $dsn = "mysql:host=$host;port=$port;dbname=$dbname;charset=utf8";
    $pdo = new PDO($dsn, $username, $password, [
        PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
        PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
    ]);
    echo "✅ Database connection successful\n\n";

    // Step 1: Enable transactional email
    echo "🔧 Step 1: Enabling RESET_PASSWORD_TO_USER transactional email...\n";
    $stmt = $pdo->prepare("
        INSERT INTO transactional_email (email_identifier, active) 
        VALUES ('RESET_PASSWORD_TO_USER', 1)
        ON DUPLICATE KEY UPDATE active = 1
    ");
    $stmt->execute();
    echo "✅ Transactional email enabled\n\n";

    // Step 2: Ensure email template node exists
    echo "🔧 Step 2: Creating/updating email template node...\n";
    $stmt = $pdo->prepare("
        INSERT INTO node (type, slug, status, created_at, updated_at, title_visible, order_node)
        VALUES ('email', 'RESET_PASSWORD_TO_USER', 'published', NOW(), NOW(), 1, 1)
        ON DUPLICATE KEY UPDATE 
            status = 'published',
            updated_at = NOW()
    ");
    $stmt->execute();
    echo "✅ Email template node created/updated\n\n";

    // Get the node ID
    $stmt = $pdo->prepare("SELECT id FROM node WHERE slug = 'RESET_PASSWORD_TO_USER' AND type = 'email' LIMIT 1");
    $stmt->execute();
    $node = $stmt->fetch();
    
    if (!$node) {
        throw new Exception("Failed to create/find email template node");
    }
    
    $nodeId = $node['id'];
    echo "📝 Using node ID: $nodeId\n\n";

    // Step 3: Create email content for all languages
    echo "🔧 Step 3: Creating email content for all languages...\n";
    
    $languages = [
        'en' => [
            'title' => '[LOCALE] Your reset password request',
            'body' => '<p>Dear {{firstName}} {{lastName}},</p>\r\n\r\n<p>Your reset password request has been received.</p>\r\n\r\n<p>We invite you to click this link to set up a new password: {{url}}</p>\r\n\r\n<p>The StationOne team remains at your disposal for any further information or support.</p>\r\n\r\n<p><br />\r\nBest regards,<br />\r\nThe StationOne team<br />\r\nhttps://www.station-one.com</p>'
        ],
        'fr' => [
            'title' => '[LOCALE] Votre demande de réinitialisation de mot de passe',
            'body' => '<p>Cher {{firstName}} {{lastName}},</p>\r\n\r\n<p>Votre demande de réinitialisation de mot de passe a été reçue.</p>\r\n\r\n<p>Nous vous invitons à cliquer sur ce lien pour définir un nouveau mot de passe : {{url}}</p>\r\n\r\n<p>L\'équipe StationOne reste à votre disposition pour tout complément d\'information ou support.</p>\r\n\r\n<p><br />\r\nCordialement,<br />\r\nL\'équipe StationOne<br />\r\nhttps://www.station-one.com</p>'
        ],
        'es' => [
            'title' => '[LOCALE] Su solicitud de restablecimiento de contraseña',
            'body' => '<p>Estimado {{firstName}} {{lastName}},</p>\r\n\r\n<p>Su solicitud de restablecimiento de contraseña ha sido recibida.</p>\r\n\r\n<p>Le invitamos a hacer clic en este enlace para establecer una nueva contraseña: {{url}}</p>\r\n\r\n<p>El equipo de StationOne permanece a su disposición para cualquier información adicional o soporte.</p>\r\n\r\n<p><br />\r\nSaludos cordiales,<br />\r\nEl equipo StationOne<br />\r\nhttps://www.station-one.com</p>'
        ],
        'de' => [
            'title' => '[LOCALE] Ihre Anfrage zur Passwort-Zurücksetzung',
            'body' => '<p>Liebe(r) {{firstName}} {{lastName}},</p>\r\n\r\n<p>Ihre Anfrage zur Passwort-Zurücksetzung wurde erhalten.</p>\r\n\r\n<p>Wir laden Sie ein, auf diesen Link zu klicken, um ein neues Passwort festzulegen: {{url}}</p>\r\n\r\n<p>Das StationOne-Team steht Ihnen für weitere Informationen oder Unterstützung zur Verfügung.</p>\r\n\r\n<p><br />\r\nMit freundlichen Grüßen,<br />\r\nDas StationOne-Team<br />\r\nhttps://www.station-one.com</p>'
        ],
        'it' => [
            'title' => '[LOCALE] La tua richiesta di reimpostazione password',
            'body' => '<p>Caro {{firstName}} {{lastName}},</p>\r\n\r\n<p>La tua richiesta di reimpostazione password è stata ricevuta.</p>\r\n\r\n<p>Ti invitiamo a cliccare su questo link per impostare una nuova password: {{url}}</p>\r\n\r\n<p>Il team StationOne rimane a tua disposizione per ulteriori informazioni o supporto.</p>\r\n\r\n<p><br />\r\nCordiali saluti,<br />\r\nIl team StationOne<br />\r\nhttps://www.station-one.com</p>'
        ],
        'nl' => [
            'title' => '[LOCALE] Uw verzoek voor wachtwoord reset',
            'body' => '<p>Beste {{firstName}} {{lastName}},</p>\r\n\r\n<p>Uw verzoek voor wachtwoord reset is ontvangen.</p>\r\n\r\n<p>We nodigen u uit om op deze link te klikken om een nieuw wachtwoord in te stellen: {{url}}</p>\r\n\r\n<p>Het StationOne team blijft tot uw beschikking voor verdere informatie of ondersteuning.</p>\r\n\r\n<p><br />\r\nMet vriendelijke groeten,<br />\r\nHet StationOne team<br />\r\nhttps://www.station-one.com</p>'
        ]
    ];

    $stmt = $pdo->prepare("
        INSERT INTO node_content (node_id, lang, title, body, created_at, updated_at)
        VALUES (?, ?, ?, ?, NOW(), NOW())
        ON DUPLICATE KEY UPDATE 
            title = VALUES(title),
            body = VALUES(body),
            updated_at = NOW()
    ");

    foreach ($languages as $lang => $content) {
        $stmt->execute([$nodeId, $lang, $content['title'], $content['body']]);
        echo "  ✅ Created/updated content for language: $lang\n";
    }

    echo "\n🔧 Step 4: Verification...\n";

    // Verify transactional email
    $stmt = $pdo->prepare("SELECT active FROM transactional_email WHERE email_identifier = 'RESET_PASSWORD_TO_USER'");
    $stmt->execute();
    $transEmail = $stmt->fetch();
    
    if ($transEmail && $transEmail['active'] == 1) {
        echo "✅ Transactional email is ENABLED\n";
    } else {
        echo "❌ Transactional email is DISABLED or missing\n";
    }

    // Verify email template
    $stmt = $pdo->prepare("SELECT status FROM node WHERE slug = 'RESET_PASSWORD_TO_USER' AND type = 'email'");
    $stmt->execute();
    $template = $stmt->fetch();
    
    if ($template && $template['status'] == 'published') {
        echo "✅ Email template is PUBLISHED\n";
    } else {
        echo "❌ Email template is not published or missing\n";
    }

    // Verify content languages
    $stmt = $pdo->prepare("
        SELECT GROUP_CONCAT(DISTINCT nc.lang ORDER BY nc.lang) as languages
        FROM node_content nc
        JOIN node n ON nc.node_id = n.id
        WHERE n.slug = 'RESET_PASSWORD_TO_USER' AND n.type = 'email'
    ");
    $stmt->execute();
    $content = $stmt->fetch();
    
    if ($content && $content['languages']) {
        echo "✅ Email content available in languages: " . $content['languages'] . "\n";
    } else {
        echo "❌ No email content found\n";
    }

    echo "\n🎉 Password reset email fix completed successfully!\n";
    echo "\n📋 Next steps:\n";
    echo "1. Test password reset functionality on the application\n";
    echo "2. Check MailHog (http://localhost:8025) for email delivery\n";
    echo "3. Verify MAILER_DSN is correctly configured for production\n";
    echo "4. Ensure Symfony Messenger workers are running in production\n\n";

} catch (Exception $e) {
    echo "❌ Error: " . $e->getMessage() . "\n";
    echo "Stack trace:\n" . $e->getTraceAsString() . "\n";
    exit(1);
}
