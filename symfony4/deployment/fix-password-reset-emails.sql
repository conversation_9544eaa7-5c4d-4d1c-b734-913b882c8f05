-- Fix Password Reset Email Issues
-- This script ensures the password reset email functionality works correctly

-- 1. Enable the RESET_PASSWORD_TO_USER transactional email
INSERT INTO transactional_email (email_identifier, active) 
VALUES ('RESET_PASSWORD_TO_USER', 1)
ON DUPLICATE KEY UPDATE active = 1;

-- 2. Ensure the email template exists in the node table
INSERT INTO node (type, slug, status, created_at, updated_at, title_visible, order_node)
VALUES ('email', 'RESET_PASSWORD_TO_USER', 'published', NOW(), NOW(), 1, 1)
ON DUPLICATE KEY UPDATE 
    status = 'published',
    updated_at = NOW();

-- 3. Get the node ID for the email template
SET @node_id = (SELECT id FROM node WHERE slug = 'RESET_PASSWORD_TO_USER' AND type = 'email' LIMIT 1);

-- 4. Insert email content for different languages
INSERT INTO node_content (node_id, lang, title, body, created_at, updated_at)
VALUES 
    (@node_id, 'en', '[LOCALE] Your reset password request', 
     '<p>Dear {{firstName}} {{lastName}},</p>\r\n\r\n<p>Your reset password request has been received.</p>\r\n\r\n<p>We invite you to click this link to set up a new password: {{url}}</p>\r\n\r\n<p>The StationOne team remains at your disposal for any further information or support.</p>\r\n\r\n<p><br />\r\nBest regards,<br />\r\nThe StationOne team<br />\r\nhttps://www.station-one.com</p>',
     NOW(), NOW()),
    (@node_id, 'fr', '[LOCALE] Votre demande de réinitialisation de mot de passe', 
     '<p>Cher {{firstName}} {{lastName}},</p>\r\n\r\n<p>Votre demande de réinitialisation de mot de passe a été reçue.</p>\r\n\r\n<p>Nous vous invitons à cliquer sur ce lien pour définir un nouveau mot de passe : {{url}}</p>\r\n\r\n<p>L''équipe StationOne reste à votre disposition pour tout complément d''information ou support.</p>\r\n\r\n<p><br />\r\nCordialement,<br />\r\nL''équipe StationOne<br />\r\nhttps://www.station-one.com</p>',
     NOW(), NOW()),
    (@node_id, 'es', '[LOCALE] Su solicitud de restablecimiento de contraseña', 
     '<p>Estimado {{firstName}} {{lastName}},</p>\r\n\r\n<p>Su solicitud de restablecimiento de contraseña ha sido recibida.</p>\r\n\r\n<p>Le invitamos a hacer clic en este enlace para establecer una nueva contraseña: {{url}}</p>\r\n\r\n<p>El equipo de StationOne permanece a su disposición para cualquier información adicional o soporte.</p>\r\n\r\n<p><br />\r\nSaludos cordiales,<br />\r\nEl equipo StationOne<br />\r\nhttps://www.station-one.com</p>',
     NOW(), NOW()),
    (@node_id, 'de', '[LOCALE] Ihre Anfrage zur Passwort-Zurücksetzung', 
     '<p>Liebe(r) {{firstName}} {{lastName}},</p>\r\n\r\n<p>Ihre Anfrage zur Passwort-Zurücksetzung wurde erhalten.</p>\r\n\r\n<p>Wir laden Sie ein, auf diesen Link zu klicken, um ein neues Passwort festzulegen: {{url}}</p>\r\n\r\n<p>Das StationOne-Team steht Ihnen für weitere Informationen oder Unterstützung zur Verfügung.</p>\r\n\r\n<p><br />\r\nMit freundlichen Grüßen,<br />\r\nDas StationOne-Team<br />\r\nhttps://www.station-one.com</p>',
     NOW(), NOW()),
    (@node_id, 'it', '[LOCALE] La tua richiesta di reimpostazione password', 
     '<p>Caro {{firstName}} {{lastName}},</p>\r\n\r\n<p>La tua richiesta di reimpostazione password è stata ricevuta.</p>\r\n\r\n<p>Ti invitiamo a cliccare su questo link per impostare una nuova password: {{url}}</p>\r\n\r\n<p>Il team StationOne rimane a tua disposizione per ulteriori informazioni o supporto.</p>\r\n\r\n<p><br />\r\nCordiali saluti,<br />\r\nIl team StationOne<br />\r\nhttps://www.station-one.com</p>',
     NOW(), NOW()),
    (@node_id, 'nl', '[LOCALE] Uw verzoek voor wachtwoord reset', 
     '<p>Beste {{firstName}} {{lastName}},</p>\r\n\r\n<p>Uw verzoek voor wachtwoord reset is ontvangen.</p>\r\n\r\n<p>We nodigen u uit om op deze link te klikken om een nieuw wachtwoord in te stellen: {{url}}</p>\r\n\r\n<p>Het StationOne team blijft tot uw beschikking voor verdere informatie of ondersteuning.</p>\r\n\r\n<p><br />\r\nMet vriendelijke groeten,<br />\r\nHet StationOne team<br />\r\nhttps://www.station-one.com</p>',
     NOW(), NOW())
ON DUPLICATE KEY UPDATE 
    title = VALUES(title),
    body = VALUES(body),
    updated_at = NOW();

-- 5. Verify the setup
SELECT 'Transactional Email Status' as 'Check', 
       CASE WHEN active = 1 THEN 'ENABLED ✅' ELSE 'DISABLED ❌' END as 'Status'
FROM transactional_email 
WHERE email_identifier = 'RESET_PASSWORD_TO_USER';

SELECT 'Email Template Status' as 'Check',
       CASE WHEN status = 'published' THEN 'PUBLISHED ✅' ELSE CONCAT('STATUS: ', status, ' ❌') END as 'Status'
FROM node 
WHERE slug = 'RESET_PASSWORD_TO_USER' AND type = 'email';

SELECT 'Email Content Languages' as 'Check',
       GROUP_CONCAT(DISTINCT lang ORDER BY lang) as 'Available Languages'
FROM node_content nc
JOIN node n ON nc.node_id = n.id
WHERE n.slug = 'RESET_PASSWORD_TO_USER' AND n.type = 'email';
